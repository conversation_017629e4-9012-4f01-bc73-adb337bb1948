version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - NODE_ENV=development
    ports:
      - "8080:80"
    environment:
      # 本地测试环境变量
      - NODE_ENV=development
      - API_BASE_URL=http://localhost:3000/api
      - APP_BASE_PATH=/
      # 启用模拟服务进行本地测试
      - USE_MOCK=true
    restart: unless-stopped
    # 如果有本地后端服务，可以添加依赖
    # depends_on:
    #   - backend-mock
  
  # 可选：添加模拟后端服务
  # backend-mock:
  #   image: node:20-alpine
  #   working_dir: /app
  #   volumes:
  #     - ./mock-server:/app
  #   ports:
  #     - "3000:3000"
  #   command: sh -c "npm install && npm start"
  #   environment:
  #     - NODE_ENV=development
  #   restart: unless-stopped
