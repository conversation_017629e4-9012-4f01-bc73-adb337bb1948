#!/bin/bash

echo "=== 清理未使用的功能组件 ==="

# 1. 删除多语言相关文件
echo "🗑️  删除多语言功能..."
if [ -d "src/locales" ]; then
  rm -rf src/locales
  echo "✅ 删除 src/locales 目录"
fi

# 2. 删除多语言相关的依赖配置
echo "🗑️  清理多语言配置..."
if [ -f ".vscode/settings.json" ]; then
  # 备份原文件
  cp .vscode/settings.json .vscode/settings.json.backup
  # 删除 i18n-ally 配置
  cat .vscode/settings.json | grep -v "i18n-ally" > .vscode/settings.json.tmp
  mv .vscode/settings.json.tmp .vscode/settings.json
  echo "✅ 清理 .vscode/settings.json 中的 i18n 配置"
fi

# 3. 删除未使用的文档文件
echo "🗑️  删除未使用的文档..."
unused_docs=(
  "docs/i18n-strategy.md"
  "docs/architecture-refactor-imports.md"
)

for doc in "${unused_docs[@]}"; do
  if [ -f "$doc" ]; then
    rm "$doc"
    echo "✅ 删除 $doc"
  fi
done

# 4. 删除未使用的脚本文件
echo "🗑️  删除未使用的脚本..."
unused_scripts=(
  "scripts/update-api-imports.js"
)

for script in "${unused_scripts[@]}"; do
  if [ -f "$script" ]; then
    rm "$script"
    echo "✅ 删除 $script"
  fi
done

# 5. 检查并列出可能未使用的组件
echo "🔍 检查可能未使用的组件..."

# 查找可能未使用的工具函数
echo "📋 可能未使用的工具函数："
find src/utils -name "*.ts" -o -name "*.js" | while read file; do
  filename=$(basename "$file" | sed 's/\.[^.]*$//')
  if [ "$filename" != "index" ] && [ "$filename" != "env" ]; then
    # 检查是否在其他文件中被导入
    usage_count=$(grep -r "import.*$filename\|from.*$filename" src --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx" | grep -v "$file" | wc -l)
    if [ "$usage_count" -eq 0 ]; then
      echo "  ⚠️  $file (可能未使用)"
    fi
  fi
done

echo ""
echo "=== 清理完成 ==="
echo "📝 建议手动检查以下内容："
echo "1. package.json 中的 i18n 相关依赖"
echo "2. apiClient.ts 中的 i18n 导入"
echo "3. enum.ts 中的 LocalEnum 和 I18N 相关枚举"
