name: Deploy <PERSON>Chain

on:
  push:
    branches:
      - 2025-05-17      # 提交代码到 main 分支时触发部署
    paths-ignore: # 规定以下文件变更不触发部署
      - README.md
      - LICENSE

jobs:
  deploy:
    runs-on: ubuntu-latest # 使用ubuntu系统镜像
    steps:
      - name: Get version
        id: get_version
        run: echo ::set-output name=VERSION::${GITHUB_REF/refs\/tags\//}

      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Login to DockerHub
        uses: docker/login-action@v2.2.0
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Build and push Docker image
        id: docker_build
        run: |
          # 给构建脚本添加执行权限
          chmod +x ./build.sh
          # 执行构建脚本，并推送镜像
          ./build.sh --push

      - name: Images info
        run: |
          echo "Image built and pushed: starrier/frostchain:0.0.1"
          docker images starrier/frostchain:0.0.1 --format "{{.ID}} {{.Size}}"

      # 部署前端代码
      # 注意：我们已经在上面的构建脚本中构建了前端代码
      # 并将构建产物复制到了 dist 目录
      - name: Deploy to Server # 推送打包产物
        uses: AEnterprise/rsync-deploy@v1.0.2
        env:
          DEPLOY_KEY: ${{ secrets.DEPLOY_KEY }} # SSH私钥，在仓库Setting里配置
          ARGS: "-e -c -r --delete"
          SERVER_PORT: "22" # SSH端口
          FOLDER: dist # 要推送的文件夹，路径相对于代码仓库的根目录
          SERVER_IP: ${{ secrets.SSH_HOST }} # 服务器的ip
          USERNAME: ${{ secrets.SSH_USERNAME }} # 服务器登录名，可以在终端输入 whoami 查看
          SERVER_DESTINATION: /home/<USER>
      - name: Deploy Post # 后置处理，比如这里会将/home/<USER>
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USERNAME }}
          key: ${{ secrets.DEPLOY_KEY }}
          script: |
            cd /home
            cp -r demo/dist demo-temp
            rm -rf demo
            mv demo-temp demo

