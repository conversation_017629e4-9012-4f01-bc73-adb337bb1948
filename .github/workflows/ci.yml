name: CI

on:
  push:
    branches: [ main ]  # 主分支推送时触发
  pull_request:
    branches: [ main ]  # 针对 main 分支的 PR 触发

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4  # 拉取代码
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Install Dependencies
        run: npm install
      - name: Run Tests
        run: npm test  # 运行你的测试命令