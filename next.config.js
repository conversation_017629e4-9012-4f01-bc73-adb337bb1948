/** @type {import('next').NextConfig} */
const nextConfig = {
  // 图片优化配置
  images: {
    domains: [
      'localhost',
      'example.com',
      'cdn.example.com',
    ],
    formats: ['image/webp', 'image/avif'],
  },

  // 重定向配置
  async redirects() {
    return [
      {
        source: '/old-home',
        destination: '/',
        permanent: true,
      },
      {
        source: '/old-about',
        destination: '/about',
        permanent: true,
      },
    ];
  },

  // 重写配置
  async rewrites() {
    return [
      {
        source: '/api/v1/:path*',
        destination: `${process.env.API_BASE_URL || 'http://localhost:8080'}/api/v1/:path*`,
      },
    ];
  },

  // 压缩配置
  compress: true,

  // 电源配置
  poweredByHeader: false,
};

module.exports = nextConfig;
