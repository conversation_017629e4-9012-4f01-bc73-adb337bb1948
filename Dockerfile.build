# 使用 Node.js 镜像
FROM node:20-alpine

# 设置工作目录
WORKDIR /app

# 增加 Node.js 内存限制
ENV NODE_OPTIONS="--max-old-space-size=4096"

# 复制 package.json 和 yarn.lock
COPY package.json yarn.lock ./

# 安装所有依赖，包括开发依赖
RUN yarn install --network-timeout 300000 --frozen-lockfile --production=false

# 确保安装了必要的开发依赖
RUN yarn add @vitejs/plugin-react path --dev

# 复制简化版的 Vite 配置
COPY vite.config.js ./

# 复制源代码
COPY . .

# 显示环境信息
RUN echo "Node version: $(node -v)" && \
    echo "Yarn version: $(yarn -v)" && \
    echo "Environment: production"

# 执行路径别名修复脚本
RUN echo "Fixing path aliases..." && \
    chmod +x build-and-deploy.sh && \
    ./build-and-deploy.sh || true

# 构建前端代码
RUN echo "Starting build process..." && \
    echo "Vite config:" && \
    cat vite.config.js && \
    echo "Theme provider imports:" && \
    cat src/theme/theme-provider.tsx | grep import && \
    echo "Theme type imports:" && \
    cat src/theme/type.ts | grep import && \
    echo "Node modules:" && \
    ls -la node_modules/@vitejs && \
    echo "Building..." && \
    yarn build

# 输出目录是 /app/dist
