# 渐进式迁移策略

## 🎯 迁移策略概述

### 核心原则
- **零停机迁移**：用户无感知的平滑迁移
- **功能对等**：确保迁移后功能完全一致
- **样式保持**：视觉效果与原版本完全相同
- **性能提升**：在保持功能的基础上提升性能

### 迁移架构
```
┌─────────────────┐    ┌─────────────────┐
│   原项目 (Vite)  │    │ 新项目 (Next.js) │
│   Port: 5173    │    │   Port: 3000     │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────┬───────────────┘
                 │
    ┌─────────────────────┐
    │   Nginx 反向代理     │
    │   智能路由分发       │
    │   Port: 80/443      │
    └─────────────────────┘
```

## 📊 迁移阶段规划

### 第一阶段：基础设施准备 (1-2天)
- [ ] 搭建 Next.js 项目
- [ ] 配置开发环境
- [ ] 设置反向代理
- [ ] 建立监控系统

### 第二阶段：静态页面迁移 (3-5天)
- [ ] 关于页面 (/about)
- [ ] 联系页面 (/contact)
- [ ] 服务页面 (/services)

### 第三阶段：动态页面迁移 (5-7天)
- [ ] 首页 (/)
- [ ] 产品列表页 (/products)
- [ ] 产品详情页 (/products/[id])

### 第四阶段：复杂功能迁移 (3-5天)
- [ ] 用户认证
- [ ] 数据分析
- [ ] 表单提交

### 第五阶段：全量切换 (1-2天)
- [ ] 性能测试
- [ ] 全量切换
- [ ] 监控验证

## 🔧 技术实现方案

### 1. 反向代理配置

#### Nginx 配置示例
```nginx
# /etc/nginx/sites-available/frost-chain
upstream vite_app {
    server localhost:5173;
}

upstream nextjs_app {
    server localhost:3000;
}

server {
    listen 80;
    server_name localhost;

    # 默认路由到原项目
    location / {
        # 检查是否启用新版本
        if ($cookie_use_nextjs = "true") {
            proxy_pass http://nextjs_app;
            break;
        }
        
        # 根据迁移进度路由
        if ($uri ~ ^/(about|contact)$) {
            proxy_pass http://nextjs_app;
            break;
        }
        
        # 默认路由到原项目
        proxy_pass http://vite_app;
    }

    # API 路由
    location /api/ {
        # 优先使用新的 API
        proxy_pass http://nextjs_app;
        
        # 如果新 API 不可用，回退到原 API
        proxy_next_upstream error timeout http_404;
        proxy_next_upstream_tries 2;
    }

    # 静态资源
    location /static/ {
        try_files $uri @vite_static;
    }

    location @vite_static {
        proxy_pass http://vite_app;
    }
}
```

### 2. 功能对比验证系统

#### 自动化对比测试
```typescript
// tests/migration-comparison.test.ts
import { test, expect } from '@playwright/test';

interface ComparisonTest {
  path: string;
  name: string;
  actions?: () => Promise<void>;
}

const comparisonTests: ComparisonTest[] = [
  { path: '/about', name: '关于页面' },
  { path: '/contact', name: '联系页面' },
  { path: '/products', name: '产品列表页' },
];

comparisonTests.forEach(({ path, name, actions }) => {
  test(`${name} - 功能对比测试`, async ({ browser }) => {
    // 创建两个浏览器上下文
    const viteContext = await browser.newContext();
    const nextjsContext = await browser.newContext();
    
    const vitePage = await viteContext.newPage();
    const nextjsPage = await nextjsContext.newPage();
    
    // 访问相同路径
    await vitePage.goto(`http://localhost:5173${path}`);
    await nextjsPage.goto(`http://localhost:3000${path}`);
    
    // 等待页面加载完成
    await vitePage.waitForLoadState('networkidle');
    await nextjsPage.waitForLoadState('networkidle');
    
    // 执行自定义操作
    if (actions) {
      await actions();
    }
    
    // 对比页面标题
    const viteTitle = await vitePage.title();
    const nextjsTitle = await nextjsPage.title();
    expect(nextjsTitle).toBe(viteTitle);
    
    // 对比页面内容
    const viteContent = await vitePage.textContent('body');
    const nextjsContent = await nextjsPage.textContent('body');
    
    // 移除动态内容（如时间戳）进行对比
    const normalizeContent = (content: string) => 
      content?.replace(/\d{4}-\d{2}-\d{2}/g, 'DATE')
             .replace(/\d{2}:\d{2}:\d{2}/g, 'TIME') || '';
    
    expect(normalizeContent(nextjsContent))
      .toBe(normalizeContent(viteContent));
    
    // 截图对比
    const viteScreenshot = await vitePage.screenshot();
    const nextjsScreenshot = await nextjsPage.screenshot();
    
    // 保存截图用于人工对比
    await vitePage.screenshot({ 
      path: `screenshots/vite-${name}-${Date.now()}.png` 
    });
    await nextjsPage.screenshot({ 
      path: `screenshots/nextjs-${name}-${Date.now()}.png` 
    });
    
    await viteContext.close();
    await nextjsContext.close();
  });
});
```

### 3. 样式一致性验证

#### CSS 对比工具
```typescript
// tools/style-comparison.ts
import puppeteer from 'puppeteer';
import { diffImages } from 'pixelmatch';
import fs from 'fs';

interface StyleComparisonResult {
  path: string;
  isIdentical: boolean;
  diffPercentage: number;
  diffImagePath?: string;
}

export class StyleComparison {
  private browser: puppeteer.Browser | null = null;

  async init() {
    this.browser = await puppeteer.launch({ headless: true });
  }

  async comparePage(path: string): Promise<StyleComparisonResult> {
    if (!this.browser) throw new Error('Browser not initialized');

    const page1 = await this.browser.newPage();
    const page2 = await this.browser.newPage();

    // 设置相同的视口
    await page1.setViewport({ width: 1200, height: 800 });
    await page2.setViewport({ width: 1200, height: 800 });

    // 访问两个版本
    await page1.goto(`http://localhost:5173${path}`);
    await page2.goto(`http://localhost:3000${path}`);

    // 等待页面稳定
    await page1.waitForTimeout(2000);
    await page2.waitForTimeout(2000);

    // 截图
    const screenshot1 = await page1.screenshot();
    const screenshot2 = await page2.screenshot();

    // 对比图片
    const diff = diffImages(
      screenshot1,
      screenshot2,
      null,
      1200,
      800,
      { threshold: 0.1 }
    );

    const diffPercentage = (diff / (1200 * 800)) * 100;
    const isIdentical = diffPercentage < 1; // 1% 以内认为相同

    await page1.close();
    await page2.close();

    return {
      path,
      isIdentical,
      diffPercentage,
    };
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}
```

### 4. 性能对比监控

#### 性能指标对比
```typescript
// tools/performance-comparison.ts
import { chromium } from 'playwright';

interface PerformanceMetrics {
  path: string;
  version: 'vite' | 'nextjs';
  metrics: {
    FCP: number;
    LCP: number;
    FID: number;
    CLS: number;
    TTFB: number;
    loadTime: number;
  };
}

export class PerformanceComparison {
  async measurePage(path: string, version: 'vite' | 'nextjs'): Promise<PerformanceMetrics> {
    const browser = await chromium.launch();
    const page = await browser.newPage();

    const baseUrl = version === 'vite' ? 'http://localhost:5173' : 'http://localhost:3000';
    
    // 开始性能监控
    await page.goto(`${baseUrl}${path}`, { waitUntil: 'networkidle' });

    // 获取 Web Vitals
    const metrics = await page.evaluate(() => {
      return new Promise((resolve) => {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const metrics: any = {};

          entries.forEach((entry) => {
            if (entry.entryType === 'paint') {
              if (entry.name === 'first-contentful-paint') {
                metrics.FCP = entry.startTime;
              }
            }
            if (entry.entryType === 'largest-contentful-paint') {
              metrics.LCP = entry.startTime;
            }
            if (entry.entryType === 'layout-shift') {
              metrics.CLS = (metrics.CLS || 0) + entry.value;
            }
          });

          // 获取导航时间
          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
          metrics.TTFB = navigation.responseStart - navigation.requestStart;
          metrics.loadTime = navigation.loadEventEnd - navigation.fetchStart;

          resolve(metrics);
        });

        observer.observe({ entryTypes: ['paint', 'largest-contentful-paint', 'layout-shift'] });

        // 5秒后返回结果
        setTimeout(() => resolve({}), 5000);
      });
    });

    await browser.close();

    return {
      path,
      version,
      metrics: metrics as any,
    };
  }

  async comparePerformance(path: string) {
    const viteMetrics = await this.measurePage(path, 'vite');
    const nextjsMetrics = await this.measurePage(path, 'nextjs');

    const comparison = {
      path,
      vite: viteMetrics.metrics,
      nextjs: nextjsMetrics.metrics,
      improvements: {
        FCP: ((viteMetrics.metrics.FCP - nextjsMetrics.metrics.FCP) / viteMetrics.metrics.FCP) * 100,
        LCP: ((viteMetrics.metrics.LCP - nextjsMetrics.metrics.LCP) / viteMetrics.metrics.LCP) * 100,
        loadTime: ((viteMetrics.metrics.loadTime - nextjsMetrics.metrics.loadTime) / viteMetrics.metrics.loadTime) * 100,
      },
    };

    return comparison;
  }
}
```

## 🔍 监控和验证流程

### 1. 每日验证检查清单

#### 功能验证
- [ ] 页面正常加载
- [ ] 导航功能正常
- [ ] 表单提交正常
- [ ] 数据显示正确
- [ ] 交互响应正常

#### 样式验证
- [ ] 布局一致
- [ ] 颜色正确
- [ ] 字体显示正常
- [ ] 响应式布局正常
- [ ] 动画效果一致

#### 性能验证
- [ ] 加载速度对比
- [ ] 内存使用对比
- [ ] 网络请求对比
- [ ] Core Web Vitals 对比

### 2. 自动化监控脚本

```bash
#!/bin/bash
# migration-monitor.sh

echo "🔍 开始迁移验证..."

# 启动两个项目
echo "启动原项目..."
cd /path/to/vite-project && npm run dev &
VITE_PID=$!

echo "启动新项目..."
cd /path/to/nextjs-project && npm run dev &
NEXTJS_PID=$!

# 等待项目启动
sleep 10

# 运行对比测试
echo "运行功能对比测试..."
npm run test:comparison

echo "运行样式对比测试..."
npm run test:styles

echo "运行性能对比测试..."
npm run test:performance

# 生成报告
echo "生成对比报告..."
npm run generate:report

# 清理进程
kill $VITE_PID $NEXTJS_PID

echo "✅ 验证完成，请查看报告"
```

### 3. 问题回滚机制

#### 快速回滚脚本
```bash
#!/bin/bash
# rollback.sh

ROUTE=$1

if [ -z "$ROUTE" ]; then
    echo "请指定要回滚的路由，例如: ./rollback.sh /about"
    exit 1
fi

echo "🔄 回滚路由: $ROUTE"

# 更新 Nginx 配置
sed -i "s|if (\$uri ~ ^\($ROUTE\)\$) {|# ROLLBACK: if (\$uri ~ ^\($ROUTE\)\$) {|g" /etc/nginx/sites-available/frost-chain

# 重新加载 Nginx
sudo nginx -s reload

echo "✅ 路由 $ROUTE 已回滚到原版本"
```

## 📊 迁移进度追踪

### 迁移状态面板
```typescript
// components/MigrationDashboard.tsx
interface MigrationStatus {
  route: string;
  status: 'pending' | 'in-progress' | 'testing' | 'completed' | 'rollback';
  functionalityScore: number;
  styleScore: number;
  performanceImprovement: number;
  lastUpdated: string;
}

export const MigrationDashboard = () => {
  const [migrationStatus, setMigrationStatus] = useState<MigrationStatus[]>([]);

  return (
    <div className="migration-dashboard">
      <h2>迁移进度面板</h2>
      <table>
        <thead>
          <tr>
            <th>路由</th>
            <th>状态</th>
            <th>功能一致性</th>
            <th>样式一致性</th>
            <th>性能提升</th>
            <th>最后更新</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          {migrationStatus.map((item) => (
            <tr key={item.route}>
              <td>{item.route}</td>
              <td>
                <StatusBadge status={item.status} />
              </td>
              <td>
                <ScoreBar score={item.functionalityScore} />
              </td>
              <td>
                <ScoreBar score={item.styleScore} />
              </td>
              <td>
                <PerformanceIndicator improvement={item.performanceImprovement} />
              </td>
              <td>{item.lastUpdated}</td>
              <td>
                <ActionButtons route={item.route} status={item.status} />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
```

## ⚠️ 风险控制

### 1. 风险识别
- **功能缺失**：新版本缺少原版本功能
- **样式差异**：视觉效果不一致
- **性能下降**：新版本性能反而更差
- **用户体验**：交互方式改变

### 2. 风险缓解
- **A/B 测试**：小部分用户先体验新版本
- **灰度发布**：逐步增加新版本流量
- **实时监控**：监控错误率和性能指标
- **快速回滚**：发现问题立即回滚

### 3. 应急预案
```typescript
// 应急回滚触发条件
const emergencyRollbackConditions = {
  errorRate: 5, // 错误率超过 5%
  performanceDrop: 30, // 性能下降超过 30%
  userComplaints: 10, // 用户投诉超过 10 个
};

// 自动回滚逻辑
if (shouldTriggerEmergencyRollback()) {
  await executeEmergencyRollback();
  await notifyTeam('紧急回滚已执行');
}
```

通过这个渐进式迁移策略，您可以：
- ✅ 确保每一步都经过充分验证
- ✅ 实时监控功能、样式、性能的一致性
- ✅ 快速发现和解决问题
- ✅ 保证用户体验不受影响
- ✅ 随时可以安全回滚
