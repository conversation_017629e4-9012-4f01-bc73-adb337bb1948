# 项目架构规范

## 目录结构

```
src/
├── assets/              # 静态资源
│   ├── images/          # 图片资源
│   ├── fonts/           # 字体资源
│   └── icons/           # 图标资源
├── components/          # 通用UI组件
│   ├── common/          # 基础组件
│   ├── layout/          # 布局组件
│   └── feedback/        # 反馈组件
├── features/            # 业务功能模块
│   ├── home/            # 首页功能
│   ├── auth/            # 认证功能
│   └── user/            # 用户功能
├── hooks/               # 自定义Hooks
├── services/            # 服务层
│   ├── api/             # API通信
│   ├── analytics/       # 分析服务
│   └── storage/         # 存储服务
├── store/               # 状态管理
│   ├── slices/          # 状态切片
│   └── middleware/      # 中间件
├── theme/               # 主题配置
│   ├── tokens/          # 设计令牌
│   └── adapter/         # UI库适配器
├── utils/               # 工具函数
├── routes/              # 路由配置
├── types/               # 类型定义
├── App.tsx              # 应用入口
└── index.tsx            # 渲染入口
```

## 模块职责

- **components**: 纯展示组件，不包含业务逻辑
- **features**: 业务功能模块，可包含业务逻辑和UI
- **services**: 抽象的服务层，处理API通信、数据转换等
- **store**: 全局状态管理
- **theme**: 主题相关配置