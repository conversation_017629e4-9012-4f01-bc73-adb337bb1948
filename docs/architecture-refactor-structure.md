# 目录结构重构方案

## 1. 标准化目录结构

```
src/
├── assets/                # 静态资源
│   ├── images/            # 图片资源
│   ├── fonts/             # 字体资源
│   └── icons/             # 图标资源
├── components/            # 通用UI组件（与业务无关）
│   ├── common/            # 基础组件（按钮、输入框等）
│   ├── layout/            # 布局组件（页头、页脚、侧边栏等）
│   └── feedback/          # 反馈组件（加载中、错误提示等）
├── features/              # 业务功能模块
│   ├── home/              # 首页功能
│   │   ├── components/    # 模块专用组件
│   │   ├── hooks/         # 模块专用钩子
│   │   ├── utils/         # 模块专用工具函数
│   │   ├── types.ts       # 模块类型定义
│   │   ├── Home.tsx       # 主页面组件
│   │   └── index.ts       # 导出文件
│   ├── about/             # 关于页功能
│   │   ├── components/    # 模块专用组件
│   │   ├── About.tsx      # 主页面组件
│   │   └── index.ts       # 导出文件
│   └── team/              # 团队页功能
│       ├── components/    # 模块专用组件
│       ├── Team.tsx       # 主页面组件
│       └── index.ts       # 导出文件
├── hooks/                 # 全局共享钩子
├── layouts/               # 页面布局
│   ├── MainLayout.tsx     # 主布局
│   ├── TeamLayout.tsx     # 团队页布局
│   └── index.ts           # 导出文件
├── services/              # 服务层
│   ├── api/               # API通信
│   │   ├── interfaces/    # 服务接口
│   │   ├── base/          # 基础类
│   │   ├── ApiClient.ts   # API客户端
│   │   ├── ApiProvider.tsx # API上下文提供者
│   │   └── index.ts       # 导出文件
│   ├── analytics/         # 分析服务
│   └── storage/           # 存储服务
├── store/                 # 状态管理
│   ├── slices/            # 状态切片
│   ├── middleware/        # 中间件
│   └── index.ts           # 导出文件
├── theme/                 # 主题配置
│   ├── tokens/            # 设计令牌
│   └── adapter/           # UI库适配器
├── utils/                 # 工具函数
├── routes/                # 路由配置
│   ├── AppRoutes.tsx      # 主路由配置
│   ├── ProtectedRoute.tsx # 受保护路由
│   └── index.ts           # 导出文件
├── types/                 # 类型定义
├── App.tsx                # 应用入口
└── main.tsx               # 渲染入口
```

## 2. 功能模块标准化

### 步骤 1: 创建功能模块模板
每个功能模块应包含以下文件结构:

```
features/[feature-name]/
├── components/            # 模块专用组件
│   ├── ComponentA.tsx
│   ├── ComponentB.tsx
│   └── index.ts           # 导出所有组件
├── hooks/                 # 模块专用钩子
│   ├── useFeatureData.ts
│   └── index.ts           # 导出所有钩子
├── utils/                 # 模块专用工具函数
│   ├── helpers.ts
│   └── index.ts           # 导出所有工具函数
├── types.ts               # 模块类型定义
├── [FeatureName].tsx      # 主页面组件
└── index.ts               # 导出文件
```

### 步骤 2: 标准化导出文件
```typescript
// features/about/index.ts
export * from './About';
export * from './components';
export * from './hooks';
export * from './utils';
export * from './types';
```

## 3. 组件分类标准

### 通用组件 (components/)
- 与业务逻辑无关
- 高度可复用
- 接受明确定义的 props
- 不直接访问全局状态或 API

### 功能组件 (features/[feature]/components/)
- 与特定业务功能相关
- 可能访问特定功能的状态或 API
- 通常只在特定功能模块内复用

### 布局组件 (layouts/)
- 定义页面整体结构
- 包含导航、页头、页脚等
- 通过 children 渲染内容区域

## 4. 迁移策略

### 步骤 1: 创建新的目录结构
```bash
# 创建标准目录结构
mkdir -p src/{assets,components,features,hooks,layouts,services,store,theme,utils,routes,types}
mkdir -p src/components/{common,layout,feedback}
mkdir -p src/services/{api,analytics,storage}
mkdir



1. 现状分析
目前  about 相关文件分散在不同位置：
src/features/about/About.tsx - 主组件
src/features/about/About.stories.tsx - Storybook 故事
src/features/about/about.module.scss - 样式文件
src/features/about/ImageColumn.tsx (推测存在)
src/features/about/FeatureList.tsx (推测存在)
src/pages/About.tsx - 页面组件
