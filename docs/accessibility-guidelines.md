# 可访问性指南

## 基本原则
- 遵循 WCAG 2.1 AA 标准
- 支持键盘导航
- 提供适当的颜色对比度

## 实现方案
- 使用语义化 HTML
- 添加适当的 ARIA 属性
- 实现焦点管理
- 支持屏幕阅读器

## 组件示例
```jsx
// 可访问的按钮组件
const AccessibleButton = ({ onClick, children, isLoading, ...props }) => (
  <button
    onClick={onClick}
    disabled={isLoading}
    aria-busy={isLoading}
    {...props}
  >
    {isLoading ? (
      <>
        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
        <span>加载中...</span>
      </>
    ) : (
      children
    )}
  </button>
);