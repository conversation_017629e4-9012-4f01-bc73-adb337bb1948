# 组件导入路径规范化方案

## 1. 路径别名配置

### 步骤 1: 配置 TypeScript 路径别名
```json
// tsconfig.json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@components/*": ["src/components/*"],
      "@features/*": ["src/features/*"],
      "@services/*": ["src/services/*"],
      "@hooks/*": ["src/hooks/*"],
      "@utils/*": ["src/utils/*"],
      "@store/*": ["src/store/*"],
      "@assets/*": ["src/assets/*"],
      "@types/*": ["src/types/*"]
    }
  }
}
```

### 步骤 2: 配置 Vite 路径别名
```javascript
// vite.config.js
import { defineConfig } from 'vite';
import path from 'path';

export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@features': path.resolve(__dirname, './src/features'),
      '@services': path.resolve(__dirname, './src/services'),
      '@hooks': path.resolve(__dirname, './src/hooks'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@store': path.resolve(__dirname, './src/store'),
      '@assets': path.resolve(__dirname, './src/assets'),
      '@types': path.resolve(__dirname, './src/types')
    }
  }
});
```

## 2. 组件导出规范

### 步骤 1: 为每个组件目录创建 index 文件
```typescript
// src/components/layout/index.ts
export * from './PageFooter';
export * from './Header';
export * from './Sidebar';
export * from './MainLayout';
```

### 步骤 2: 为功能模块创建 index 文件
```typescript
// src/features/about/index.ts
export * from './About';
export * from './Team';
export * from './History';
```

## 3. 导入路径重构工具

### 步骤 1: 创建导入路径重构脚本
```javascript
// scripts/refactor-imports.js
const fs = require('fs');
const path = require('path');
const glob = require('glob');

// 定义路径映射
const pathMappings = {
  '../components/': '@components/',
  '../features/': '@features/',
  '../services/': '@services/',
  '../hooks/': '@hooks/',
  '../utils/': '@utils/',
  '../store/': '@store/',
  '../assets/': '@assets/',
  '../types/': '@types/'
};

// 查找所有 JS/TS 文件
const files = glob.sync('src/**/*.{js,jsx,ts,tsx}');

files.forEach(file => {
  let content = fs.readFileSync(file, 'utf8');
  let modified = false;
  
  // 替换相对路径导入
  Object.entries(pathMappings).forEach(([oldPath, newPath]) => {
    const regex = new RegExp(`import\\s+(.+?)\\s+from\\s+['"]${oldPath}`, 'g');
    if (regex.test(content)) {
      content = content.replace(regex, `import $1 from '${newPath}`);
      modified = true;
    }
  });
  
  // 保存修改后的文件
  if (modified) {
    fs.writeFileSync(file, content, 'utf8');
    console.log(`Updated imports in ${file}`);
  }
});
```

## 4. 导入路径检查工具

### 步骤 1: 配置 ESLint 规则
```javascript
// .eslintrc.js
module.exports = {
  // 其他配置...
  rules: {
    // 强制使用别名导入
    'no-restricted-imports': [
      'error',
      {
        patterns: [
          {
            group: ['../*'],
            message: '请使用别名导入 (@components/, @features/ 等) 而不是相对路径'
          }
        ]
      }
    ],
    // 确保导入路径排序
    'import/order': [
      'error',
      {
        groups: ['builtin', 'external', 'internal', 'parent', 'sibling', 'index'],
        pathGroups: [
          {
            pattern: '@/**',
            group: 'internal',
            position: 'after'
          }
        ],
        'newlines-between': 'always',
        alphabetize: {
          order: 'asc',
          caseInsensitive: true
        }
      }
    ]
  }
};