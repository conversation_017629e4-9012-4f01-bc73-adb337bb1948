# 部署指南

本文档提供了如何在生产环境中部署应用的指南。

## Docker 部署

### 构建 Docker 镜像

```bash
# 构建 Docker 镜像
docker build -t your-app-name:latest .
```

### 运行 Docker 容器

```bash
# 运行 Docker 容器
docker run -d -p 80:80 \
  -e NODE_ENV=production \
  -e API_BASE_URL=/api \
  -e APP_BASE_PATH=/ \
  your-app-name:latest
```

### 使用 Docker Compose 部署

```bash
# 使用 Docker Compose 部署
docker-compose up -d
```

## 环境变量

应用支持以下环境变量：

### 构建时环境变量

这些环境变量在构建时使用，定义在 `.env.production` 文件中：

- `VITE_API_BASE_URL`: API 基础路径
- `VITE_APP_BASE_PATH`: 应用基础路径
- `VITE_USE_MOCK`: 是否使用模拟服务

### 运行时环境变量

这些环境变量在运行时使用，通过 Docker 环境变量传递：

- `NODE_ENV`: 环境名称（production、development、test）
- `API_BASE_URL`: API 基础路径
- `APP_BASE_PATH`: 应用基础路径

## 多环境部署

如果需要部署到多个环境（如测试环境、预发布环境、生产环境），可以使用不同的环境变量：

```bash
# 测试环境
docker run -d -p 80:80 \
  -e NODE_ENV=test \
  -e API_BASE_URL=https://test-api.example.com \
  -e APP_BASE_PATH=/ \
  your-app-name:latest

# 预发布环境
docker run -d -p 80:80 \
  -e NODE_ENV=staging \
  -e API_BASE_URL=https://staging-api.example.com \
  -e APP_BASE_PATH=/ \
  your-app-name:latest

# 生产环境
docker run -d -p 80:80 \
  -e NODE_ENV=production \
  -e API_BASE_URL=https://api.example.com \
  -e APP_BASE_PATH=/ \
  your-app-name:latest
```

## 注意事项

1. 确保在构建 Docker 镜像时使用 `--mode production` 参数，以使用生产环境配置
2. 运行时环境变量会覆盖构建时环境变量
3. 如果需要更改 Nginx 配置，可以修改 `nginx.conf` 文件
