# 本地测试环境使用指南

本文档提供了如何在本地使用 Docker 测试应用的指南。

## 快速开始

### 使用 npm 脚本

```bash
# 构建本地测试环境的 Docker 镜像
npm run docker:build:local

# 运行本地测试环境的 Docker 容器
npm run docker:run:local

# 或者使用 Docker Compose 一键启动
npm run docker:local:up

# 查看日志
npm run docker:local:logs

# 停止并删除容器
npm run docker:local:down
```

### 使用启动脚本

```bash
# 使用启动脚本一键启动本地测试环境
./start-local-test.sh
```

## 访问本地测试环境

本地测试环境启动后，可以通过以下地址访问：

- 前端应用：http://localhost:8080

## 环境变量配置

本地测试环境使用以下环境变量：

- `NODE_ENV=development`：环境名称
- `API_BASE_URL=http://localhost:3000/api`：API 基础路径
- `APP_BASE_PATH=/`：应用基础路径
- `USE_MOCK=true`：启用模拟服务

## 手动构建和运行

如果需要手动构建和运行本地测试环境，可以使用以下命令：

```bash
# 构建本地测试环境的 Docker 镜像
docker build -t ice-company-website:local-test \
  --build-arg NODE_ENV=development \
  .

# 运行本地测试环境的 Docker 容器
docker run -d -p 8080:80 \
  -e NODE_ENV=development \
  -e API_BASE_URL=http://localhost:3000/api \
  -e APP_BASE_PATH=/ \
  -e USE_MOCK=true \
  --name ice-company-local-test \
  ice-company-website:local-test
```

## 使用 Docker Compose

也可以使用 Docker Compose 启动本地测试环境：

```bash
# 启动本地测试环境
docker-compose -f docker-compose.local.yml up -d

# 查看日志
docker-compose -f docker-compose.local.yml logs -f

# 停止并删除容器
docker-compose -f docker-compose.local.yml down
```

## 注意事项

1. 本地测试环境使用端口 8080，确保该端口未被占用
2. 如果需要连接本地后端服务，确保后端服务运行在端口 3000 上
3. 如果需要修改环境变量，可以编辑 `docker-compose.local.yml` 文件
