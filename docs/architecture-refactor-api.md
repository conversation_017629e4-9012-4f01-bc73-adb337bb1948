# API 服务层重构方案

## 1. 统一 API 客户端

### 步骤 1: 创建统一的 API 客户端基类
```typescript
// src/services/api/base/ApiClientBase.ts
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse, RetryConfig } from '@/types/api';

export abstract class ApiClientBase {
  protected instance: AxiosInstance;
  
  constructor(baseURL: string, timeout: number = 10000) {
    this.instance = axios.create({
      baseURL,
      timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    this.setupInterceptors();
  }
  
  protected abstract setupInterceptors(): void;
  
  public async request<T>(config: AxiosRequestConfig & RetryConfig): Promise<T> {
    // 实现请求逻辑，包括重试机制
  }
}
```

### 步骤 2: 实现具体的 API 客户端
```typescript
// src/services/api/ApiClient.ts
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiClientBase } from './base/ApiClientBase';
import { userStore } from '@/store/userStore';
import { toast } from 'sonner';
import { t } from '@/locales/i18n';

export class ApiClient extends ApiClientBase {
  constructor() {
    const apiUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';
    super(apiUrl);
  }
  
  protected setupInterceptors(): void {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const token = userStore.getState().token;
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );
    
    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        // 处理成功响应
        return response.data;
      },
      (error) => {
        // 处理错误响应
        const { response, message } = error || {};
        const errMsg = response?.data?.message || message || t('api.errorMessage');
        
        toast.error(errMsg, {
          position: 'top-center',
        });
        
        if (response?.status === 401) {
          userStore.getState().actions.logout();
        }
        
        return Promise.reject(error);
      }
    );
  }
}
```

## 2. 服务层重构

### 步骤 1: 创建服务接口
```typescript
// src/services/api/interfaces/IFeatureService.ts
import { Feature } from '@/types/feature';

export interface IFeatureService {
  fetchFeatures(): Promise<Feature[]>;
  getFeatureById(id: string): Promise<Feature>;
  createFeature(feature: Omit<Feature, 'id'>): Promise<Feature>;
  updateFeature(id: string, feature: Partial<Feature>): Promise<Feature>;
  deleteFeature(id: string): Promise<void>;
}
```

### 步骤 2: 实现具体服务
```typescript
// src/services/api/FeatureService.ts
import { ApiClient } from './ApiClient';
import { IFeatureService } from './interfaces/IFeatureService';
import { Feature } from '@/types/feature';

export class FeatureService implements IFeatureService {
  private apiClient: ApiClient;
  
  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }
  
  async fetchFeatures(): Promise<Feature[]> {
    return this.apiClient.request<Feature[]>({
      method: 'GET',
      url: '/features',
    });
  }
  
  async getFeatureById(id: string): Promise<Feature> {
    return this.apiClient.request<Feature>({
      method: 'GET',
      url: `/features/${id}`,
    });
  }
  
  async createFeature(feature: Omit<Feature, 'id'>): Promise<Feature> {
    return this.apiClient.request<Feature>({
      method: 'POST',
      url: '/features',
      data: feature,
    });
  }
  
  async updateFeature(id: string, feature: Partial<Feature>): Promise<Feature> {
    return this.apiClient.request<Feature>({
      method: 'PATCH',
      url: `/features/${id}`,
      data: feature,
    });
  }
  
  async deleteFeature(id: string): Promise<void> {
    return this.apiClient.request<void>({
      method: 'DELETE',
      url: `/features/${id}`,
    });
  }
}
```

## 3. 依赖注入与服务提供

### 步骤 1: 创建服务容器
```typescript
// src/services/api/ServiceContainer.ts
import { ApiClient } from './ApiClient';
import { FeatureService } from './FeatureService';
import { UserService } from './UserService';
import { IFeatureService } from './interfaces/IFeatureService';
import { IUserService } from './interfaces/IUserService';

export class ServiceContainer {
  private static instance: ServiceContainer;
  private apiClient: ApiClient;
  private services: Map<string, any> = new Map();
  
  private constructor() {
    this.apiClient = new ApiClient();
    this.registerServices();
  }
  
  public static getInstance(): ServiceContainer {
    if (!ServiceContainer.instance) {
      ServiceContainer.instance = new ServiceContainer();
    }
    return ServiceContainer.instance;
  }
  
  private registerServices(): void {
    // 注册所有服务
    this.services.set('featureService', new FeatureService(this.apiClient));
    this.services.set('userService', new UserService(this.apiClient));
    // 添加更多服务...
  }
  
  public getFeatureService(): IFeatureService {
    return this.services.get('featureService');
  }
  
  public getUserService(): IUserService {
    return this.services.get('userService');
  }
  
  // 获取其他服务的方法...
}
```

### 步骤 2: 创建 React Context Provider
```typescript
// src/services/api/ApiProvider.tsx
import React, { createContext, useContext } from 'react';
import { ServiceContainer } from './ServiceContainer';
import { IFeatureService } from './interfaces/IFeatureService';
import { IUserService } from './interfaces/IUserService';

interface ApiContextValue {
  featureService: IFeatureService;
  userService: IUserService;
  // 其他服务...
}

const ApiContext = createContext<ApiContextValue | undefined>(undefined);

export const ApiProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const serviceContainer = ServiceContainer.getInstance();
  
  const value: ApiContextValue = {
    featureService: serviceContainer.getFeatureService(),
    userService: serviceContainer.getUserService(),
    // 其他服务...
  };
  
  return <ApiContext.Provider value={value}>{children}</ApiContext.Provider>;
};

// 自定义 hooks 用于访问服务
export const useFeatureService = (): IFeatureService => {
  const context = useContext(ApiContext);
  if (!context) {
    throw new Error('useFeatureService must be used within an ApiProvider');
  }
  return context.featureService;
};

export const useUserService = (): IUserService => {
  const context = useContext(ApiContext);
  if (!context) {
    throw new Error('useUserService must be used within an ApiProvider');
  }
  return context.userService;
};

// 其他服务的 hooks...