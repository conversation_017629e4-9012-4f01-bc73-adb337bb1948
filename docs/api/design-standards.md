# API 设计规范

## 🎯 设计原则

### 1. 一致性原则
- 统一的命名规范
- 统一的响应格式
- 统一的错误处理
- 统一的状态码使用

### 2. 简洁性原则
- 清晰的接口设计
- 简单的参数结构
- 直观的返回数据
- 易于理解的文档

### 3. 可扩展性原则
- 向后兼容的版本管理
- 灵活的参数设计
- 可扩展的数据结构
- 模块化的接口设计

## 📡 统一响应格式

### 基础响应结构
```typescript
interface BaseResponse<T = any> {
  /** 响应状态码 */
  code: number;
  /** 响应消息 */
  message: string;
  /** 响应数据 */
  data: T;
  /** 请求是否成功 */
  success: boolean;
  /** 时间戳 */
  timestamp: number;
  /** 请求追踪ID */
  traceId?: string;
}
```

### 分页响应结构
```typescript
interface PaginatedResponse<T = any> extends BaseResponse<T[]> {
  /** 分页信息 */
  pagination: {
    /** 当前页码 */
    current: number;
    /** 每页大小 */
    pageSize: number;
    /** 总数量 */
    total: number;
    /** 总页数 */
    totalPages: number;
  };
}
```

### 响应示例

#### 成功响应
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": "123",
    "name": "用户名"
  },
  "success": true,
  "timestamp": 1640995200000,
  "traceId": "trace-123456"
}
```

#### 错误响应
```json
{
  "code": 1001,
  "message": "参数验证失败",
  "data": {
    "errors": [
      {
        "field": "email",
        "message": "邮箱格式不正确"
      }
    ]
  },
  "success": false,
  "timestamp": 1640995200000,
  "traceId": "trace-123456"
}
```

## 🔢 状态码规范

### HTTP 状态码
```typescript
export const HTTP_STATUS = {
  OK: 200,                    // 请求成功
  CREATED: 201,               // 资源创建成功
  NO_CONTENT: 204,            // 请求成功，无返回内容
  BAD_REQUEST: 400,           // 请求参数错误
  UNAUTHORIZED: 401,          // 未授权
  FORBIDDEN: 403,             // 权限不足
  NOT_FOUND: 404,             // 资源不存在
  METHOD_NOT_ALLOWED: 405,    // 请求方法不允许
  CONFLICT: 409,              // 资源冲突
  UNPROCESSABLE_ENTITY: 422,  // 请求格式正确，但语义错误
  TOO_MANY_REQUESTS: 429,     // 请求过于频繁
  INTERNAL_SERVER_ERROR: 500, // 服务器内部错误
  BAD_GATEWAY: 502,           // 网关错误
  SERVICE_UNAVAILABLE: 503,   // 服务不可用
  GATEWAY_TIMEOUT: 504,       // 网关超时
} as const;
```

### 业务状态码
```typescript
export const BUSINESS_CODE = {
  SUCCESS: 0,                     // 成功
  INVALID_PARAMS: 1001,           // 参数错误
  RESOURCE_NOT_FOUND: 1002,       // 资源不存在
  RESOURCE_ALREADY_EXISTS: 1003,  // 资源已存在
  INSUFFICIENT_PERMISSIONS: 1004, // 权限不足
  OPERATION_FAILED: 1005,         // 操作失败
  DATA_VALIDATION_FAILED: 1006,   // 数据验证失败
  EXTERNAL_SERVICE_ERROR: 1007,   // 外部服务错误
} as const;
```

## 🛠️ 请求配置

### 请求配置接口
```typescript
interface RequestConfig {
  /** 是否显示加载状态 */
  showLoading?: boolean;
  /** 是否显示错误提示 */
  showError?: boolean;
  /** 是否显示成功提示 */
  showSuccess?: boolean;
  /** 自定义错误处理 */
  customErrorHandler?: (error: ApiError) => void;
  /** 请求超时时间 */
  timeout?: number;
  /** 重试次数 */
  retryCount?: number;
}
```

### 使用示例
```typescript
// 基础使用
const response = await unifiedApiClient.get<UserInfo>('/user/profile');

// 带配置使用
const response = await unifiedApiClient.post<LoginResponse>(
  '/auth/login',
  loginData,
  {
    showLoading: true,
    showSuccess: true,
    showError: true,
    timeout: 5000,
    retryCount: 3,
  }
);
```

## 🔗 API 端点规范

### 端点命名规范
```typescript
export const API_ENDPOINTS = {
  // 用户相关
  USER: {
    LOGIN: { path: '/auth/login', method: 'POST' as const },
    LOGOUT: { path: '/auth/logout', method: 'POST' as const },
    PROFILE: { path: '/user/profile', method: 'GET' as const },
    UPDATE_PROFILE: { path: '/user/profile', method: 'PUT' as const },
  },
  
  // 产品相关
  PRODUCT: {
    LIST: { path: '/products', method: 'GET' as const },
    DETAIL: { path: '/products/:id', method: 'GET' as const },
    CREATE: { path: '/products', method: 'POST' as const },
    UPDATE: { path: '/products/:id', method: 'PUT' as const },
    DELETE: { path: '/products/:id', method: 'DELETE' as const },
  },
  
  // 分析相关
  ANALYTICS: {
    REPORT: { path: '/analytics/report', method: 'POST' as const },
    STATS: { path: '/analytics/stats', method: 'GET' as const },
  },
} as const;
```

### RESTful 设计原则
- **GET**：获取资源
- **POST**：创建资源
- **PUT**：更新整个资源
- **PATCH**：部分更新资源
- **DELETE**：删除资源

## ⚠️ 错误处理规范

### 错误类型定义
```typescript
export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',        // 网络错误
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',        // 请求超时
  SERVER_ERROR = 'SERVER_ERROR',          // 服务器错误
  CLIENT_ERROR = 'CLIENT_ERROR',          // 客户端错误
  AUTH_ERROR = 'AUTH_ERROR',              // 认证错误
  PERMISSION_ERROR = 'PERMISSION_ERROR',  // 权限错误
  BUSINESS_ERROR = 'BUSINESS_ERROR',      // 业务逻辑错误
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',        // 未知错误
}
```

### 错误处理流程
1. **错误捕获**：在请求拦截器中捕获错误
2. **错误分类**：根据状态码和错误类型分类
3. **错误转换**：转换为用户友好的错误消息
4. **错误展示**：通过 Toast 或其他方式展示
5. **错误上报**：记录错误日志并上报

### 错误处理示例
```typescript
// 自动错误处理
try {
  const data = await userService.login(loginData);
  // 成功处理
} catch (error) {
  // 错误会被自动处理和展示
  console.error('登录失败:', error);
}

// 自定义错误处理
const data = await unifiedApiClient.post('/auth/login', loginData, {
  customErrorHandler: (error) => {
    if (error.code === 1001) {
      // 自定义处理参数错误
      showCustomErrorDialog(error.message);
    }
  }
});
```

## 🔐 认证和授权

### 认证机制
- **JWT Token**：使用 JSON Web Token 进行身份认证
- **Token 刷新**：自动刷新过期的 Token
- **Token 存储**：安全存储在 localStorage 或 sessionStorage

### 请求头设置
```typescript
// 自动添加认证头
headers: {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json',
  'X-Trace-Id': generateTraceId(),
  'X-Timestamp': Date.now().toString(),
}
```

### 权限控制
```typescript
// 端点权限配置
export const API_ENDPOINTS = {
  USER: {
    PROFILE: { 
      path: '/user/profile', 
      method: 'GET' as const,
      requiresAuth: true,  // 需要认证
      permissions: ['user:read']  // 需要的权限
    },
  },
} as const;
```

## 📊 性能优化

### 缓存策略
```typescript
// 缓存配置
interface CacheConfig {
  /** 缓存时间（毫秒） */
  ttl?: number;
  /** 是否启用缓存 */
  enabled?: boolean;
  /** 缓存键生成函数 */
  keyGenerator?: (url: string, params?: any) => string;
}

// 使用缓存
const response = await unifiedApiClient.get('/user/profile', undefined, {
  cache: {
    ttl: 5 * 60 * 1000, // 5分钟缓存
    enabled: true,
  }
});
```

### 请求优化
- **请求合并**：合并相似的请求
- **请求去重**：避免重复请求
- **请求取消**：取消不需要的请求
- **请求重试**：自动重试失败的请求

## 📝 API 文档规范

### 接口文档格式
```typescript
/**
 * 用户登录
 * @route POST /auth/login
 * @param {LoginRequest} data - 登录数据
 * @returns {Promise<BaseResponse<LoginResponse>>} 登录响应
 * @example
 * ```typescript
 * const response = await userService.login({
 *   username: '<EMAIL>',
 *   password: 'password123'
 * });
 * ```
 */
export const login = async (data: LoginRequest): Promise<BaseResponse<LoginResponse>> => {
  // 实现代码
};
```

### 类型定义
```typescript
// 请求类型
export interface LoginRequest {
  /** 用户名或邮箱 */
  username: string;
  /** 密码 */
  password: string;
  /** 是否记住登录状态 */
  remember?: boolean;
}

// 响应类型
export interface LoginResponse {
  /** 访问令牌 */
  token: string;
  /** 用户信息 */
  user: UserInfo;
  /** 令牌过期时间 */
  expiresAt: string;
}
```

## 🧪 测试规范

### API 测试
```typescript
// 单元测试示例
describe('userService', () => {
  it('should login successfully', async () => {
    const mockResponse = {
      code: 0,
      message: '登录成功',
      data: { token: 'mock-token', user: mockUser },
      success: true,
      timestamp: Date.now(),
    };
    
    jest.spyOn(unifiedApiClient, 'post').mockResolvedValue(mockResponse);
    
    const result = await userService.login({
      username: '<EMAIL>',
      password: 'password123'
    });
    
    expect(result.data.token).toBe('mock-token');
  });
});
```

### 集成测试
```typescript
// 集成测试示例
describe('API Integration', () => {
  it('should handle authentication flow', async () => {
    // 测试完整的认证流程
    const loginResponse = await userService.login(testCredentials);
    expect(loginResponse.success).toBe(true);
    
    const profileResponse = await userService.getProfile();
    expect(profileResponse.success).toBe(true);
  });
});
```

## 📋 最佳实践

### 1. 接口设计
- 保持接口的简洁和一致性
- 使用语义化的 URL 和方法
- 提供清晰的错误信息
- 支持分页和过滤

### 2. 性能优化
- 合理使用缓存策略
- 避免不必要的请求
- 使用适当的超时设置
- 实现请求重试机制

### 3. 安全考虑
- 验证所有输入参数
- 使用 HTTPS 传输
- 实现适当的认证和授权
- 防止常见的安全漏洞

### 4. 监控和调试
- 记录详细的请求日志
- 实现错误追踪和上报
- 监控 API 性能指标
- 提供调试工具和信息
