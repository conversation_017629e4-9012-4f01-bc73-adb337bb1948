# 国际化策略

## 技术选型
- 使用 i18next 实现国际化
- 按需加载语言包
- 支持语言检测和切换

## 实现方案
```jsx
// src/i18n/index.ts
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    fallbackLng: 'zh',
    ns: ['common', 'feature', 'about'],
    defaultNS: 'common',
    debug: process.env.NODE_ENV === 'development',
    interpolation: {
      escapeValue: false
    },
    react: {
      useSuspense: true
    }
  });

export default i18n;
```

## 翻译管理
- 使用 Lokalise 或 Crowdin 管理翻译
- 实现翻译自动化流程
- 支持多语言 SEO