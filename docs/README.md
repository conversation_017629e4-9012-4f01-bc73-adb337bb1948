# 项目文档目录

## 📚 文档结构

### 🏗️ 架构文档
- [项目架构概览](./architecture/overview.md)
- [目录结构说明](./architecture/directory-structure.md)
- [技术栈说明](./architecture/tech-stack.md)
- [设计模式](./architecture/design-patterns.md)

### 🔧 开发文档
- [开发环境搭建](./development/setup.md)
- [开发规范](./development/coding-standards.md)
- [Git 工作流](./development/git-workflow.md)
- [代码审查指南](./development/code-review.md)

### 📡 API 文档
- [API 设计规范](./api/design-standards.md)
- [API 接口文档](./api/endpoints.md)
- [错误处理规范](./api/error-handling.md)
- [认证授权](./api/authentication.md)

### 🧩 组件文档
- [组件开发指南](./components/development-guide.md)
- [组件库文档](./components/component-library.md)
- [布局系统](./components/layout-system.md)
- [主题系统](./components/theme-system.md)

### 🔍 测试文档
- [测试策略](./testing/strategy.md)
- [单元测试指南](./testing/unit-testing.md)
- [集成测试指南](./testing/integration-testing.md)
- [E2E 测试指南](./testing/e2e-testing.md)

### 📊 性能文档
- [性能监控指南](./performance/monitoring.md)
- [性能优化策略](./performance/optimization.md)
- [性能基准测试](./performance/benchmarks.md)
- [性能问题排查](./performance/troubleshooting.md)

### 🚀 部署文档
- [部署指南](./deployment/guide.md)
- [环境配置](./deployment/environment.md)
- [Docker 部署](./deployment/docker.md)
- [CI/CD 流程](./deployment/cicd.md)

### 🛠️ 运维文档
- [监控告警](./operations/monitoring.md)
- [日志管理](./operations/logging.md)
- [故障排查](./operations/troubleshooting.md)
- [备份恢复](./operations/backup.md)

### 📋 项目管理
- [项目规划](./project/planning.md)
- [版本管理](./project/versioning.md)
- [发布流程](./project/release-process.md)
- [变更日志](./project/changelog.md)

## 🎯 快速导航

### 新手入门
1. [项目架构概览](./architecture/overview.md) - 了解项目整体架构
2. [开发环境搭建](./development/setup.md) - 搭建开发环境
3. [开发规范](./development/coding-standards.md) - 学习编码规范
4. [组件开发指南](./components/development-guide.md) - 开始组件开发

### 常用文档
- [API 接口文档](./api/endpoints.md) - 查看所有 API 接口
- [组件库文档](./components/component-library.md) - 查看可用组件
- [性能监控指南](./performance/monitoring.md) - 了解性能监控
- [故障排查](./operations/troubleshooting.md) - 解决常见问题

### 高级主题
- [设计模式](./architecture/design-patterns.md) - 深入理解架构设计
- [性能优化策略](./performance/optimization.md) - 学习性能优化
- [测试策略](./testing/strategy.md) - 完善测试覆盖
- [CI/CD 流程](./deployment/cicd.md) - 自动化部署

## 📝 文档贡献

### 文档更新流程
1. 在对应目录下创建或修改 Markdown 文件
2. 遵循文档编写规范
3. 提交 Pull Request
4. 经过审查后合并

### 文档编写规范
- 使用清晰的标题结构
- 提供代码示例和截图
- 保持内容的时效性
- 添加必要的链接和引用

### 文档维护
- 定期检查文档的准确性
- 及时更新过时的信息
- 收集用户反馈并改进
- 保持文档的完整性

## 🔗 相关链接

- [项目仓库](https://github.com/your-org/frost-chain)
- [在线演示](https://frost-chain-demo.com)
- [问题反馈](https://github.com/your-org/frost-chain/issues)
- [讨论区](https://github.com/your-org/frost-chain/discussions)
