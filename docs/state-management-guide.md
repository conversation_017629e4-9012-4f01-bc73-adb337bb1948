# 状态管理指南

## 概述

本项目采用分层状态管理策略，结合 Zustand 和 React Query 处理不同类型的状态。

## 状态分类

1. **UI 状态**: 组件内部状态，使用 React 的 useState/useReducer
2. **应用状态**: 全局共享状态，使用 Zustand
3. **服务器状态**: API 数据，使用 React Query

## 使用指南

### 访问应用状态

```tsx
import { useAppState } from '@/hooks/useAppState';

const MyComponent = () => {
  const { user, userActions, settings } = useAppState();
  
  return (
    <div>
      {user.isAuthenticated ? (
        <button onClick={userActions.logout}>退出登录</button>
      ) : (
        <button>登录</button>
      )}
      
      <div>当前主题: {settings.theme}</div>
    </div>
  );
};
```

### 访问服务器状态

```tsx
import { useQueryState } from '@/hooks/useQueryState';
import { fetchProducts } from '@/services/api/productService';

const ProductList = () => {
  const { data, isLoading, error, refresh } = useQueryState(
    'products',
    fetchProducts
  );
  
  if (isLoading) return <div>加载中...</div>;
  if (error) return <div>加载失败</div>;
  
  return (
    <div>
      <button onClick={refresh}>刷新</button>
      {data.map(product => (
        <div key={product.id}>{product.name}</div>
      ))}
    </div>
  );
};
```

## 最佳实践

1. 使用 `useAppState` 访问全局状态，避免直接导入 store
2. 使用 `useQueryState` 处理所有 API 数据
3. 保持状态切片小而专注
4. 使用 TypeScript 确保类型安全