# 文档标准

## 组件文档
每个组件应包含:
- 组件用途
- Props 定义
- 使用示例
- 注意事项

## API 文档
使用 OpenAPI/Swagger 定义 API:
```yaml
# src/services/api/openapi.yaml
openapi: 3.0.0
info:
  title: 寒链 API
  version: 1.0.0
paths:
  /api/feature:
    get:
      summary: 获取特性列表
      responses:
        '200':
          description: 成功获取特性列表
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Feature'
components:
  schemas:
    Feature:
      type: object
      properties:
        title:
          type: string
        text:
          type: string