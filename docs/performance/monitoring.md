# 性能监控指南

## 🎯 监控目标

### 核心性能指标
- **页面加载时间**：用户从点击链接到页面完全加载的时间
- **首次内容绘制 (FCP)**：浏览器首次渲染任何内容的时间
- **最大内容绘制 (LCP)**：页面主要内容加载完成的时间
- **首次输入延迟 (FID)**：用户首次交互到浏览器响应的时间
- **累积布局偏移 (CLS)**：页面布局稳定性指标
- **内存使用情况**：JavaScript 堆内存使用量

### 性能阈值标准
```typescript
const PERFORMANCE_THRESHOLDS = {
  pageLoadTime: 3000,           // 页面加载 < 3秒
  firstContentfulPaint: 1800,   // FCP < 1.8秒
  largestContentfulPaint: 2500, // LCP < 2.5秒
  firstInputDelay: 100,         // FID < 100毫秒
  cumulativeLayoutShift: 0.1,   // CLS < 0.1
  memoryUsage: 100,             // 内存使用 < 100MB
};
```

## 🛠️ 监控工具

### 1. 性能优化器
```typescript
import { performanceOptimizer } from '@/utils/performanceOptimizer';

// 获取当前性能指标
const metrics = performanceOptimizer.collectCurrentMetrics();

// 获取完整性能报告
const report = performanceOptimizer.getPerformanceReport();

// 强制上报性能数据
await performanceOptimizer.forceReport();
```

### 2. 组件性能监控
```typescript
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor';

const MyComponent = () => {
  const { metrics, getPerformanceReport } = usePerformanceMonitor({
    componentName: 'MyComponent',
    threshold: 16, // 60fps
    monitorMemory: true,
  });

  return <div>组件内容</div>;
};
```

### 3. 函数性能追踪
```typescript
import { withPerformanceTracking, performanceTest } from '@/utils/performanceOptimizer';

// 装饰器方式
const optimizedFunction = withPerformanceTracking(
  myFunction,
  'MyFunction'
);

// 测试工具方式
const result = await performanceTest.time(
  () => expensiveOperation(),
  'ExpensiveOperation'
);
```

## 📊 监控实施

### 1. 自动监控
系统会自动收集以下性能数据：
- 页面加载完成时的性能指标
- 组件渲染性能（开发环境）
- 内存使用情况
- 网络连接信息

### 2. 手动监控
```typescript
// 在关键操作前后手动收集性能数据
const startTime = performance.now();

// 执行关键操作
await criticalOperation();

const endTime = performance.now();
console.log(`操作耗时: ${endTime - startTime}ms`);

// 检查内存使用
performanceTest.memory('CriticalOperation');
```

### 3. 实时监控
```typescript
// 设置性能监控观察器
const observer = new PerformanceObserver((list) => {
  for (const entry of list.getEntries()) {
    console.log('性能条目:', entry);
  }
});

observer.observe({ entryTypes: ['measure', 'navigation', 'paint'] });
```

## 🔍 监控数据分析

### 性能指标解读

#### 页面加载时间
- **优秀**: < 1秒
- **良好**: 1-3秒
- **需要优化**: > 3秒

#### 首次内容绘制 (FCP)
- **优秀**: < 1.8秒
- **需要改进**: 1.8-3秒
- **差**: > 3秒

#### 最大内容绘制 (LCP)
- **优秀**: < 2.5秒
- **需要改进**: 2.5-4秒
- **差**: > 4秒

#### 首次输入延迟 (FID)
- **优秀**: < 100毫秒
- **需要改进**: 100-300毫秒
- **差**: > 300毫秒

#### 累积布局偏移 (CLS)
- **优秀**: < 0.1
- **需要改进**: 0.1-0.25
- **差**: > 0.25

### 性能报告示例
```typescript
interface PerformanceReport {
  url: string;
  userAgent: string;
  metrics: {
    pageLoadTime: 2500,           // 2.5秒
    firstContentfulPaint: 1200,   // 1.2秒
    largestContentfulPaint: 2100, // 2.1秒
    firstInputDelay: 50,          // 50毫秒
    cumulativeLayoutShift: 0.05,  // 0.05
    memoryUsage: {
      usedJSHeapSize: 45000000,   // 45MB
      totalJSHeapSize: 60000000,  // 60MB
      jsHeapSizeLimit: 2000000000 // 2GB
    }
  };
  timestamp: 1640995200000;
  sessionId: "session_123456";
}
```

## 🚨 性能警告系统

### 自动警告
当性能指标超过阈值时，系统会自动发出警告：

```typescript
// 控制台警告示例
⚠️ 性能警告
  页面加载时间过长: 3500ms (阈值: 3000ms)
  内存使用过高: 120.5MB (阈值: 100MB)
```

### 自定义警告
```typescript
// 设置自定义性能阈值
performanceOptimizer.updateThresholds({
  pageLoadTime: 2000,  // 更严格的阈值
  memoryUsage: 80,     // 更低的内存限制
});
```

## 📈 性能数据上报

### 自动上报
- **定时上报**：每30秒自动上报一次
- **页面卸载上报**：用户离开页面时上报
- **阈值触发上报**：性能指标超过阈值时立即上报

### 上报数据格式
```typescript
// 上报到 /analytics/performance 接口
{
  reports: [
    {
      url: "https://example.com/page",
      userAgent: "Mozilla/5.0...",
      metrics: { /* 性能指标 */ },
      timestamp: 1640995200000,
      sessionId: "session_123456",
      userId: "user_789"
    }
  ]
}
```

### 手动上报
```typescript
// 强制立即上报当前性能数据
await performanceOptimizer.forceReport();
```

## 🔧 性能优化建议

### 基于监控数据的优化策略

#### 页面加载优化
```typescript
// 1. 代码分割
const LazyComponent = React.lazy(() => import('./LazyComponent'));

// 2. 资源预加载
<link rel="preload" href="/critical.css" as="style" />
<link rel="prefetch" href="/next-page.js" as="script" />

// 3. 图片优化
<img 
  src="image.webp" 
  loading="lazy" 
  alt="描述"
  width="300" 
  height="200" 
/>
```

#### 渲染性能优化
```typescript
// 1. 使用 React.memo
const OptimizedComponent = React.memo(MyComponent);

// 2. 使用 useMemo 和 useCallback
const memoizedValue = useMemo(() => expensiveCalculation(a, b), [a, b]);
const memoizedCallback = useCallback(() => doSomething(a, b), [a, b]);

// 3. 虚拟滚动
import { FixedSizeList as List } from 'react-window';
```

#### 内存优化
```typescript
// 1. 清理事件监听器
useEffect(() => {
  const handleScroll = () => { /* ... */ };
  window.addEventListener('scroll', handleScroll);
  
  return () => {
    window.removeEventListener('scroll', handleScroll);
  };
}, []);

// 2. 避免内存泄漏
useEffect(() => {
  let isMounted = true;
  
  fetchData().then(data => {
    if (isMounted) {
      setData(data);
    }
  });
  
  return () => {
    isMounted = false;
  };
}, []);
```

## 📱 移动端性能监控

### 移动端特殊考虑
- **网络条件**：监控不同网络条件下的性能
- **设备性能**：考虑低端设备的性能限制
- **电池使用**：监控 CPU 密集型操作

### 移动端优化
```typescript
// 检测网络连接类型
const connection = navigator.connection;
if (connection && connection.effectiveType === '4g') {
  // 高质量网络，加载高清资源
} else {
  // 低质量网络，加载压缩资源
}

// 检测设备性能
const hardwareConcurrency = navigator.hardwareConcurrency;
if (hardwareConcurrency >= 4) {
  // 高性能设备，启用复杂动画
} else {
  // 低性能设备，简化动画
}
```

## 🎯 性能监控最佳实践

### 1. 监控策略
- **全面覆盖**：监控所有关键页面和组件
- **分层监控**：页面级、组件级、函数级监控
- **实时监控**：实时收集和分析性能数据

### 2. 数据分析
- **趋势分析**：分析性能指标的变化趋势
- **对比分析**：对比不同版本的性能差异
- **用户分析**：分析不同用户群体的性能体验

### 3. 持续优化
- **定期评估**：定期评估性能监控效果
- **优化迭代**：基于监控数据持续优化
- **团队协作**：与开发团队分享性能洞察

### 4. 监控工具集成
```typescript
// 集成第三方监控工具
import { init as initSentry } from '@sentry/react';
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

// 初始化 Sentry
initSentry({
  dsn: 'YOUR_SENTRY_DSN',
  integrations: [
    new BrowserTracing(),
  ],
  tracesSampleRate: 1.0,
});

// 收集 Web Vitals 指标
getCLS(console.log);
getFID(console.log);
getFCP(console.log);
getLCP(console.log);
getTTFB(console.log);
```
