# 埋点分析使用指南

## 概述
本文档介绍如何在项目中实现埋点分析功能，用于收集用户行为数据，支持PV/UV分析、功能使用率和点击率报表。

## 核心功能
- 页面浏览跟踪 (PV)
- 自定义事件跟踪
- 用户行为分析
- 本地开发环境上报

## 基本使用

### 1. 在组件中使用

```tsx
import { useTracker } from '../components/TrackerProvider';

function MyComponent() {
  const { trackEvent, trackPageView } = useTracker();

  // 页面加载时跟踪PV
  useEffect(() => {
    trackPageView('产品详情页', {
      productId: '123',
      category: '电子产品'
    });
  }, []);

  const handleClick = () => {
    // 跟踪按钮点击事件
    trackEvent('add_to_cart', {
      productId: '123',
      price: 2999
    });
  };

  return <button onClick={handleClick}>加入购物车</button>;
}
```

### 2. 上报数据结构

#### 页面浏览数据 (PV)
```json
{
  "eventType": "pageview",
  "pageName": "产品详情页",
  "timestamp": "2023-06-15T08:30:45.123Z",
  "sessionId": "session_xyz123",
  "userId": "user_abc456",
  "url": "https://example.com/products/123",
  "referrer": "https://example.com/home",
  "deviceInfo": {
    "userAgent": "Mozilla/5.0...",
    "screenWidth": 1920,
    "screenHeight": 1080,
    "language": "zh-CN"
  },
  "productId": "123",
  "category": "电子产品"
}
```

#### 自定义事件数据
```json
{
  "eventType": "event",
  "eventName": "add_to_cart",
  "timestamp": "2023-06-15T08:31:10.456Z",
  "sessionId": "session_xyz123",
  "userId": "user_abc456",
  "url": "https://example.com/products/123",
  "deviceInfo": {
    "userAgent": "Mozilla/5.0...",
    "screenWidth": 1920,
    "screenHeight": 1080,
    "language": "zh-CN"
  },
  "productId": "123",
  "price": 2999
}
```

## 最佳实践

1. **命名规范**
   - 页面名称使用"模块_页面"格式，如`product_detail`
   - 事件名称使用"动作_对象"格式，如`click_add_button`

2. **数据量控制**
   - 单个事件数据不超过2KB
   - 避免上报敏感信息

3. **性能优化**
   - 高频事件使用`trackEvent`的异步上报
   - 关键事件可考虑即时上报

4. **开发环境**
   - 本地开发时数据上报到`http://localhost:808/api/analysics/data`
   - 生产环境需配置不同的上报地址

## 本地开发环境配置

### 1. 启动本地分析服务
建议使用以下方式之一运行本地分析服务：

1. **Mock服务** (推荐)
```bash
npm install -g json-server
echo '{
  "analysics/data": [],
  "analysics/health": { "status": "ok" }
}' > db.json
json-server --watch db.json --port 808
```

2. **使用Docker**
```bash
docker run -p 808:808 -v $(pwd)/analytics-data:/data -d analytics-service
```

### 2. 验证服务可用性
访问以下端点确认服务正常运行：
- `http://localhost:808/api/analysics/health` (应返回200 OK)
- `http://localhost:808/api/analysics/data` (应返回空数组)

## 常见问题

Q: 如何测试埋点是否生效？
A: 在浏览器开发者工具的Network面板查看`/api/analysics/data`请求

Q: 开发环境上报失败怎么办？
A: 1. 确认本地分析服务已启动
    2. 检查控制台警告信息
    3. 数据会降级记录到控制台

Q: 用户ID是如何生成的？
A: 首次访问时生成并存储在localStorage中，后续访问会复用

Q: 会话(Session)的持续时间是多久？
A: 默认30分钟无活动后视为新会话

Q: 生产环境如何配置？
A: 需替换为真实分析服务地址，建议通过环境变量配置
