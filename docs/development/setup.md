# 开发环境搭建

## 🎯 环境要求

### 系统要求
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **Node.js**: 18.0.0 或更高版本
- **包管理器**: Yarn 1.22+ 或 npm 8.0+
- **Git**: 2.20.0 或更高版本

### 推荐工具
- **IDE**: Visual Studio Code
- **浏览器**: Chrome 90+ (开发调试)
- **终端**: 支持现代终端特性的工具

## 🚀 快速开始

### 1. 克隆项目
```bash
# 使用 HTTPS
git clone https://github.com/your-org/frost-chain.git

# 或使用 SSH
<NAME_EMAIL>:your-org/frost-chain.git

# 进入项目目录
cd frost-chain
```

### 2. 安装依赖
```bash
# 使用 Yarn (推荐)
yarn install

# 或使用 npm
npm install
```

### 3. 环境配置
```bash
# 复制环境变量模板
cp .env.example .env.local

# 编辑环境变量
vim .env.local
```

### 4. 启动开发服务器
```bash
# 启动开发服务器
yarn dev

# 或使用 npm
npm run dev
```

访问 [http://localhost:5173](http://localhost:5173) 查看应用。

## ⚙️ 环境变量配置

### 环境变量文件
```bash
# .env.local
NODE_ENV=development

# API 配置
VITE_API_BASE_URL=http://localhost:8080/api
VITE_API_PROXY_TARGET=http://localhost:8080

# 应用配置
VITE_APP_TITLE=Frost Chain
VITE_APP_VERSION=1.0.0

# 功能开关
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_DEBUG=true

# 第三方服务
VITE_SENTRY_DSN=your_sentry_dsn
VITE_GOOGLE_ANALYTICS_ID=your_ga_id
```

### 环境变量说明
| 变量名 | 描述 | 默认值 | 必需 |
|--------|------|--------|------|
| `VITE_API_BASE_URL` | API 基础 URL | `http://localhost:8080/api` | 是 |
| `VITE_API_PROXY_TARGET` | API 代理目标 | `http://localhost:8080` | 是 |
| `VITE_APP_TITLE` | 应用标题 | `Frost Chain` | 否 |
| `VITE_ENABLE_ANALYTICS` | 启用分析 | `false` | 否 |
| `VITE_ENABLE_DEBUG` | 启用调试 | `false` | 否 |

## 🛠️ 开发工具配置

### Visual Studio Code

#### 推荐扩展
```json
// .vscode/extensions.json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json",
    "christian-kohler.path-intellisense",
    "formulahendry.auto-rename-tag",
    "ms-vscode.vscode-css-peek"
  ]
}
```

#### 工作区设置
```json
// .vscode/settings.json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },
  "files.associations": {
    "*.css": "tailwindcss"
  }
}
```

#### 调试配置
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch Chrome",
      "request": "launch",
      "type": "chrome",
      "url": "http://localhost:5173",
      "webRoot": "${workspaceFolder}/src",
      "sourceMapPathOverrides": {
        "webpack:///src/*": "${webRoot}/*"
      }
    }
  ]
}
```

### Git 配置

#### Git Hooks (Husky)
```json
// package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"
    }
  },
  "lint-staged": {
    "*.{ts,tsx,js,jsx}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{css,scss,md}": [
      "prettier --write"
    ]
  }
}
```

#### 提交信息规范
```bash
# .commitlintrc.js
module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'type-enum': [
      2,
      'always',
      [
        'feat',     // 新功能
        'fix',      // 修复
        'docs',     // 文档
        'style',    // 样式
        'refactor', // 重构
        'test',     // 测试
        'chore',    // 构建/工具
        'perf',     // 性能优化
        'ci',       // CI/CD
        'revert'    // 回滚
      ]
    ]
  }
};
```

## 📦 项目脚本

### 开发脚本
```bash
# 启动开发服务器
yarn dev

# 启动开发服务器（指定端口）
yarn dev --port 3000

# 启动开发服务器（指定主机）
yarn dev --host 0.0.0.0
```

### 构建脚本
```bash
# 构建生产版本
yarn build

# 构建并预览
yarn build && yarn preview

# 分析构建产物
yarn build --analyze
```

### 代码质量脚本
```bash
# ESLint 检查
yarn lint

# ESLint 修复
yarn lint:fix

# TypeScript 类型检查
yarn type-check

# Prettier 格式化
yarn format

# 运行所有检查
yarn check-all
```

### 测试脚本
```bash
# 运行单元测试
yarn test

# 运行测试并监听变化
yarn test:watch

# 运行测试并生成覆盖率报告
yarn test:coverage

# 运行 E2E 测试
yarn test:e2e
```

## 🔧 故障排除

### 常见问题

#### 1. 依赖安装失败
```bash
# 清理缓存
yarn cache clean
# 或
npm cache clean --force

# 删除 node_modules 重新安装
rm -rf node_modules yarn.lock
yarn install
```

#### 2. 端口被占用
```bash
# 查找占用端口的进程
lsof -ti:5173

# 杀死进程
kill -9 <PID>

# 或使用不同端口
yarn dev --port 3000
```

#### 3. TypeScript 错误
```bash
# 重启 TypeScript 服务
# 在 VS Code 中: Ctrl+Shift+P -> "TypeScript: Restart TS Server"

# 清理 TypeScript 缓存
rm -rf node_modules/.cache
```

#### 4. 环境变量不生效
```bash
# 确保环境变量以 VITE_ 开头
VITE_API_URL=http://localhost:8080

# 重启开发服务器
yarn dev
```

### 性能问题

#### 1. 开发服务器启动慢
```bash
# 检查 node_modules 大小
du -sh node_modules

# 清理不必要的依赖
yarn install --production
yarn install
```

#### 2. 热更新慢
```typescript
// vite.config.ts
export default defineConfig({
  server: {
    hmr: {
      overlay: false // 禁用错误覆盖层
    }
  }
});
```

## 🌐 浏览器支持

### 支持的浏览器
- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

### 浏览器配置
```json
// .browserslistrc
> 1%
last 2 versions
not dead
not ie 11
```

## 📱 移动端开发

### 移动端调试
```bash
# 启动开发服务器并允许外部访问
yarn dev --host 0.0.0.0

# 使用手机访问
# http://你的电脑IP:5173
```

### 移动端测试工具
- **Chrome DevTools**: 设备模拟
- **Safari Web Inspector**: iOS 调试
- **Weinre**: 远程调试
- **BrowserStack**: 真机测试

## 🔍 调试技巧

### React DevTools
```bash
# 安装 React DevTools 浏览器扩展
# Chrome: https://chrome.google.com/webstore/detail/react-developer-tools/
# Firefox: https://addons.mozilla.org/en-US/firefox/addon/react-devtools/
```

### 性能调试
```typescript
// 启用 React 性能分析
if (process.env.NODE_ENV === 'development') {
  import('react-dom/profiling').then(({ unstable_trace }) => {
    // 性能追踪代码
  });
}
```

### 网络调试
```bash
# 使用代理工具
# Charles Proxy
# Fiddler
# Wireshark
```

## 📚 学习资源

### 官方文档
- [React 官方文档](https://react.dev/)
- [TypeScript 官方文档](https://www.typescriptlang.org/)
- [Vite 官方文档](https://vitejs.dev/)
- [Ant Design 官方文档](https://ant.design/)

### 推荐教程
- [React TypeScript 最佳实践](https://react-typescript-cheatsheet.netlify.app/)
- [现代 JavaScript 教程](https://javascript.info/)
- [CSS Grid 完整指南](https://css-tricks.com/snippets/css/complete-guide-grid/)

### 社区资源
- [React 中文社区](https://react.docschina.org/)
- [TypeScript 中文手册](https://www.tslang.cn/)
- [前端开发者手册](https://frontendmasters.com/guides/front-end-handbook/)
