# 组件开发指南

## 🎯 开发原则

### 1. 单一职责原则
每个组件应该只负责一个功能，保持简单和专注。

```typescript
// ✅ 好的例子 - 单一职责
const Button: React.FC<ButtonProps> = ({ children, onClick, variant }) => {
  return (
    <button className={`btn btn-${variant}`} onClick={onClick}>
      {children}
    </button>
  );
};

// ❌ 避免 - 职责过多
const ButtonWithModalAndForm: React.FC = () => {
  // 包含按钮、模态框、表单等多个职责
};
```

### 2. 可复用性原则
组件应该设计为可在多个场景中复用。

```typescript
// ✅ 可复用的组件
interface CardProps {
  title?: string;
  children: React.ReactNode;
  actions?: React.ReactNode;
  className?: string;
}

const Card: React.FC<CardProps> = ({ title, children, actions, className }) => {
  return (
    <div className={`card ${className || ''}`}>
      {title && <div className="card-header">{title}</div>}
      <div className="card-body">{children}</div>
      {actions && <div className="card-footer">{actions}</div>}
    </div>
  );
};
```

### 3. 可组合性原则
组件应该能够与其他组件组合使用。

```typescript
// ✅ 可组合的组件
const Form = ({ children, onSubmit }) => (
  <form onSubmit={onSubmit}>{children}</form>
);

const FormField = ({ label, children }) => (
  <div className="form-field">
    <label>{label}</label>
    {children}
  </div>
);

// 使用
<Form onSubmit={handleSubmit}>
  <FormField label="用户名">
    <Input name="username" />
  </FormField>
  <FormField label="密码">
    <Input type="password" name="password" />
  </FormField>
</Form>
```

## 🏗️ 组件结构

### 标准组件模板
```typescript
import React, { memo, forwardRef } from 'react';
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor';
import { createOptimizedComponent } from '@/components/common/OptimizedComponent';

// 1. 类型定义
export interface MyComponentProps {
  /** 组件标题 */
  title?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 点击事件处理器 */
  onClick?: (event: React.MouseEvent) => void;
  /** 子组件 */
  children?: React.ReactNode;
  /** 自定义类名 */
  className?: string;
}

// 2. 组件实现
const MyComponentBase = forwardRef<HTMLDivElement, MyComponentProps>(
  ({ title, disabled = false, onClick, children, className, ...props }, ref) => {
    // 3. 性能监控（开发环境）
    usePerformanceMonitor({
      componentName: 'MyComponent',
      enabled: process.env.NODE_ENV === 'development',
    });

    // 4. 事件处理
    const handleClick = (event: React.MouseEvent) => {
      if (disabled) return;
      onClick?.(event);
    };

    // 5. 渲染
    return (
      <div
        ref={ref}
        className={`my-component ${disabled ? 'disabled' : ''} ${className || ''}`}
        onClick={handleClick}
        {...props}
      >
        {title && <h3 className="my-component__title">{title}</h3>}
        <div className="my-component__content">{children}</div>
      </div>
    );
  }
);

// 6. 优化和导出
export const MyComponent = createOptimizedComponent(MyComponentBase, {
  componentName: 'MyComponent',
  enablePerformanceMonitor: true,
  enableErrorBoundary: true,
});

MyComponent.displayName = 'MyComponent';
```

### 目录结构
```
src/components/
├── common/              # 通用组件
│   ├── Button/
│   │   ├── index.ts
│   │   ├── Button.tsx
│   │   ├── Button.test.tsx
│   │   ├── Button.stories.tsx
│   │   └── Button.module.scss
│   └── ...
├── feedback/            # 反馈组件
├── layout/              # 布局组件
└── providers/           # 提供者组件
```

## 🎨 样式规范

### CSS Modules
```scss
// Button.module.scss
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    opacity: 0.8;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  // 变体样式
  &--primary {
    background-color: #1890ff;
    color: white;
  }

  &--secondary {
    background-color: #f0f0f0;
    color: #333;
  }

  // 尺寸样式
  &--small {
    padding: 4px 8px;
    font-size: 12px;
  }

  &--large {
    padding: 12px 24px;
    font-size: 16px;
  }
}
```

### 样式组件使用
```typescript
import styles from './Button.module.scss';

const Button: React.FC<ButtonProps> = ({ variant, size, children, ...props }) => {
  const className = [
    styles.button,
    variant && styles[`button--${variant}`],
    size && styles[`button--${size}`],
  ].filter(Boolean).join(' ');

  return (
    <button className={className} {...props}>
      {children}
    </button>
  );
};
```

## 🔧 性能优化

### 1. 记忆化组件
```typescript
import { memo } from 'react';

// 使用 React.memo 优化
const ExpensiveComponent = memo<ExpensiveComponentProps>(
  ({ data, onUpdate }) => {
    // 复杂的渲染逻辑
    return <div>{/* 渲染内容 */}</div>;
  },
  // 自定义比较函数
  (prevProps, nextProps) => {
    return prevProps.data.id === nextProps.data.id;
  }
);
```

### 2. 回调函数优化
```typescript
import { useCallback, useMemo } from 'react';

const OptimizedComponent: React.FC<Props> = ({ items, onItemClick }) => {
  // 缓存计算结果
  const processedItems = useMemo(() => {
    return items.map(item => ({
      ...item,
      processed: true,
    }));
  }, [items]);

  // 缓存回调函数
  const handleItemClick = useCallback((id: string) => {
    onItemClick(id);
  }, [onItemClick]);

  return (
    <div>
      {processedItems.map(item => (
        <Item
          key={item.id}
          data={item}
          onClick={handleItemClick}
        />
      ))}
    </div>
  );
};
```

### 3. 懒加载组件
```typescript
import { lazy, Suspense } from 'react';
import { LoadingSpinner } from '@/components/feedback/LoadingSpinner';

// 懒加载重型组件
const HeavyComponent = lazy(() => import('./HeavyComponent'));

const ParentComponent: React.FC = () => {
  return (
    <div>
      <Suspense fallback={<LoadingSpinner />}>
        <HeavyComponent />
      </Suspense>
    </div>
  );
};
```

## 🧪 测试规范

### 单元测试
```typescript
// Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from './Button';

describe('Button', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button')).toBeInTheDocument();
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('disables when disabled prop is true', () => {
    render(<Button disabled>Click me</Button>);
    expect(screen.getByRole('button')).toBeDisabled();
  });

  it('applies custom className', () => {
    render(<Button className="custom-class">Click me</Button>);
    expect(screen.getByRole('button')).toHaveClass('custom-class');
  });
});
```

### Storybook 故事
```typescript
// Button.stories.tsx
import type { Meta, StoryObj } from '@storybook/react';
import { Button } from './Button';

const meta: Meta<typeof Button> = {
  title: 'Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'danger'],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    variant: 'primary',
    children: 'Primary Button',
  },
};

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: 'Secondary Button',
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
    children: 'Disabled Button',
  },
};
```

## 🔍 错误处理

### 错误边界集成
```typescript
import { ErrorBoundaryWithFallback } from '@/components/common/ErrorBoundary';

const SafeComponent: React.FC<Props> = ({ children }) => {
  return (
    <ErrorBoundaryWithFallback
      name="SafeComponent"
      level="component"
      fallback={(error, errorInfo, retry) => (
        <div className="error-fallback">
          <h3>组件加载失败</h3>
          <p>{error.message}</p>
          <button onClick={retry}>重试</button>
        </div>
      )}
    >
      {children}
    </ErrorBoundaryWithFallback>
  );
};
```

### 错误处理 Hook
```typescript
import { useErrorHandler } from '@/hooks/useErrorHandler';

const ComponentWithErrorHandling: React.FC = () => {
  const { handleError, handleAsyncError } = useErrorHandler();

  const handleSubmit = async (data: FormData) => {
    try {
      await handleAsyncError(submitForm(data));
      // 成功处理
    } catch (error) {
      // 错误已被自动处理
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* 表单内容 */}
    </form>
  );
};
```

## 📚 文档规范

### JSDoc 注释
```typescript
/**
 * 按钮组件
 * 
 * @example
 * ```tsx
 * <Button variant="primary" onClick={handleClick}>
 *   点击我
 * </Button>
 * ```
 */
export interface ButtonProps {
  /** 按钮变体 */
  variant?: 'primary' | 'secondary' | 'danger';
  /** 按钮尺寸 */
  size?: 'small' | 'medium' | 'large';
  /** 是否禁用 */
  disabled?: boolean;
  /** 点击事件处理器 */
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  /** 按钮内容 */
  children: React.ReactNode;
}
```

### README 文档
```markdown
# Button 组件

通用按钮组件，支持多种变体和尺寸。

## 使用方法

```tsx
import { Button } from '@/components/common/Button';

<Button variant="primary" onClick={handleClick}>
  点击我
</Button>
```

## API

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| variant | 'primary' \| 'secondary' \| 'danger' | 'primary' | 按钮变体 |
| size | 'small' \| 'medium' \| 'large' | 'medium' | 按钮尺寸 |
| disabled | boolean | false | 是否禁用 |
| onClick | (event: MouseEvent) => void | - | 点击事件处理器 |

## 示例

### 基础用法
```tsx
<Button>默认按钮</Button>
```

### 不同变体
```tsx
<Button variant="primary">主要按钮</Button>
<Button variant="secondary">次要按钮</Button>
<Button variant="danger">危险按钮</Button>
```
```

## 🎯 最佳实践

### 1. 组件设计
- **保持简单**：每个组件只做一件事
- **接口清晰**：提供清晰的 Props 接口
- **文档完善**：提供详细的使用文档
- **测试覆盖**：确保充分的测试覆盖

### 2. 性能考虑
- **合理使用 memo**：避免不必要的重渲染
- **优化依赖**：减少 useEffect 和 useMemo 的依赖
- **懒加载**：对重型组件使用懒加载
- **代码分割**：合理分割组件代码

### 3. 可维护性
- **统一命名**：使用一致的命名规范
- **模块化**：保持组件的模块化
- **版本控制**：合理管理组件版本
- **向后兼容**：保持 API 的向后兼容性

### 4. 团队协作
- **代码审查**：进行充分的代码审查
- **知识分享**：分享组件设计经验
- **文档维护**：及时更新组件文档
- **标准统一**：遵循团队开发标准
