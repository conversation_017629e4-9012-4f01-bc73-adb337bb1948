# TypeScript 迁移指南

## 迁移目标

将所有 `.jsx` 文件转换为 `.tsx` 文件，同时添加适当的类型定义，提高代码质量和开发体验。

## 迁移步骤

### 1. 自动迁移

使用项目根目录下的迁移脚本进行初步转换：

```bash
# 安装依赖
npm install glob chalk --save-dev

# 运行迁移脚本
node scripts/jsx-to-tsx.js
```

脚本会自动：
- 查找所有 `.jsx` 文件
- 转换 PropTypes 为 TypeScript 接口
- 添加组件类型注解
- 创建新的 `.tsx` 文件
- 备份原始 `.jsx` 文件

### 2. 手动优化

自动转换后，需要手动优化以下内容：

1. **复杂类型定义**：
   - 检查并完善复杂对象类型
   - 优化联合类型和泛型类型

2. **导入共享类型**：
   - 使用项目中定义的共享类型替换自动生成的简单类型
   ```typescript
   import { WithDataProps, FeatureItemType } from '@/types/common';
   ```

3. **添加事件类型**：
   - 为事件处理函数添加正确的事件类型
   ```typescript
   const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
     // ...
   };
   ```

4. **状态类型**：
   - 为 useState 添加明确的类型
   ```typescript
   const [isOpen, setIsOpen] = useState<boolean>(false);
   const [items, setItems] = useState<ItemType[]>([]);
   ```

### 3. 测试与验证

每个组件迁移后：
1. 运行 TypeScript 类型检查
   ```bash
   npm run typecheck
   ```

2. 在浏览器中测试组件功能
3. 修复发现的类型错误和功能问题

### 4. 最佳实践

迁移过程中应遵循以下最佳实践：

1. **使用接口而非类型别名**：
   - 优先使用 `interface` 定义组件 props
   - 使用 `type` 定义联合类型和工具类型

2. **避免使用 `any`**：
   - 尽量使用具体类型或 `unknown`
   - 必要时使用泛型提高灵活性

3. **组件类型注解**：
   - 函数组件使用 `React.FC<Props>` 或 `(props: Props) => JSX.Element`
   - 类组件使用 `React.Component<Props, State>`

4. **导出类型**：
   - 导出可重用的接口和类型
   - 使用命名空间组织相关类型

## 常见问题

### 1. 第三方库缺少类型定义

解决方案：
```bash
# 安装第三方库的类型定义
npm install @types/库名 --save-dev

# 如果没有官方类型定义，创建自定义声明文件
# src/types/declarations.d.ts
```

### 2. 复杂 PropTypes 转换

对于复杂的 PropTypes（如嵌套的 shape），需要手动创建对应的接口：

```typescript
// 原 PropTypes
MyComponent.propTypes = {
  user: PropTypes.shape({
    name: PropTypes.string.isRequired,
    age: PropTypes.number,
    address: PropTypes.shape({
      street: PropTypes.string,
      city: PropTypes.string,
    }),
  }),
};

// 转换为 TypeScript 接口
interface Address {
  street?: string;
  city?: string;
}

interface User {
  name: string;
  age?: number;
  address?: Address;
}

interface MyComponentProps {
  user?: User;
}
```

### 3. 条件渲染类型检查

处理条件渲染时的类型检查：

```typescript
// 使用可选链和类型守卫
if (data?.items && data.items.length > 0) {
  // 这里 TypeScript 知道 data.items 是数组且非空
}

// 使用类型断言（谨慎使用）
const items = data?.items as ItemType[];
```