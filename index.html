<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">

  <!-- 预连接关键外部资源 -->
  <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
  <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
  <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

  <!-- 预加载关键资源 -->
  <link rel="preload" href="/css/style.css" as="style">
  <link rel="preload" href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" as="style">
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>

  <!-- 图标优化 -->
  <link rel="shortcut icon" href="/img/favicon.ico" type="image/x-icon">
  <link rel="icon" type="image/png" sizes="192x192" href="/img/android-chrome-192x192.png">
  <link rel="apple-touch-icon" href="/img/apple-touch-icon.png">
  <link rel="apple-touch-icon" sizes="72x72" href="/img/apple-touch-icon-72x72.png">
  <link rel="apple-touch-icon" sizes="114x114" href="/img/apple-touch-icon-114x114.png">

  <!-- 样式表合并 -->
  <!-- Bootstrap 5 全家桶 -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css"
        rel="stylesheet"
        integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH"
        crossorigin="anonymous"
        media="print" onload="this.media='all'">
  <!--  Font Awesome -->
  <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
        crossorigin="anonymous"
        media="print" onload="this.media='all'">

  <!-- 预加载关键字体图标 -->
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>

  <!-- Google Fonts 合并请求 -->
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&family=Lato:wght@400;700&family=Raleway:wght@300;400;500;600;700;800;900&display=swap"
        rel="stylesheet"
        media="print" onload="this.media='all'">

  <!-- 预加载关键字体 -->
  <link rel="preload" as="font" href="https://fonts.gstatic.com/s/opensans/v35/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTS-mu0SC55I.woff2" crossorigin>

  <!-- 本地样式合并 -->
  <link rel="stylesheet" href="/css/nivo-lightbox/nivo-lightbox.css" media="print" onload="this.media='all'"> <!-- 合并两个nivo文件 -->
  <link rel="stylesheet" href="/css/style.css">
  <link rel="stylesheet" href="/css/a11y.css">

  <!-- 动画库 -->
  <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
        media="print" onload="this.media='all'">

  <!-- 元信息优化 -->
  <title>寒链 - 工业冰战略合作伙伴</title>
  <meta name="description" content="全网直供工业用冰解决方案：食品保鲜、水产冷链、医药仓储、工地降温。极速响应，专业服务，让高温焦虑彻底退散！">
  <meta name="author" content="寒链科技">
  <meta name="keywords" content="工业用冰,冷链解决方案,食品保鲜,工地降温">

  <!-- Open Graph / 社交媒体元数据 -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://frologi.com/">
  <meta property="og:title" content="寒链 - 工业冰战略合作伙伴">
  <meta property="og:description" content="全网直供工业用冰解决方案：食品保鲜、水产冷链、医药仓储、工地降温。">
  <meta property="og:image" content="https://frologi.com/img/og-image.jpg">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:locale" content="zh_CN">
  <meta property="og:site_name" content="上海寒链实业有限公司">

  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="寒链 - 工业冰战略合作伙伴">
  <meta name="twitter:description" content="全网直供工业用冰解决方案：食品保鲜、水产冷链、医药仓储、工地降温。">
  <meta name="twitter:image" content="https://frologi.com/img/twitter-image.jpg">

  <!-- 关键 CSS 内联 -->
  <style>
    /* 关键样式内联，减少渲染阻塞 */
    body { margin: 0; font-family: 'Open Sans', sans-serif; }
    .navbar { background-color: #fff; box-shadow: 0 2px 4px rgba(0,0,0,.1); }
    .container { max-width: 1200px; margin: 0 auto; padding: 0 15px; }
    #react { min-height: 100vh; display: flex; flex-direction: column; }
  </style>
  <script>
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?518ba7c29aae38e16c0b41d072dd21ae";
      var s = document.getElementsByTagName("script")[0];
      s.parentNode.insertBefore(hm, s);
    })();
  </script>
  <!-- 结构化数据 - 公司信息 -->
  <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "上海寒链实业有限公司",
      "url": "https://frologi.com/",
      "logo": "https://frologi.com/img/logo.png",
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "+86-13761182299",
        "contactType": "customer service",
        "email": "<EMAIL>"
      },
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "上海市宝山区长江西路2311号1-2层",
        "addressLocality": "上海",
        "addressRegion": "上海市",
        "postalCode": "200000",
        "addressCountry": "CN"
      }
    }
  </script>

  <!-- 结构化数据 - 首页 -->
  <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "url": "https://frologi.com/",
      "name": "寒链 - 工业冰战略合作伙伴",
      "description": "全网直供工业用冰解决方案：食品保鲜、水产冷链、医药仓储、工地降温。",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://www.hanchain.com/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
  </script>

  <!-- 谷歌分析 Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-LEMVNEB0VM"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-LEMVNEB0VM');
  </script>
  <!-- Google Tag Manager -->
  <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
          j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
          'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
  })(window,document,'script','dataLayer','GTM-56XGGZP3');</script>
  <!-- End Google Tag Manager -->
</head>

<body id="page-top" data-bs-spy="scroll" data-bs-target=".navbar">
<!-- 跳过导航链接 -->
<a href="#main-content" class="skip-nav">跳过导航到主要内容</a>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-56XGGZP3"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
<!-- 使用 Bootstrap 5 的 Scrollspy 属性 -->
<div id="react"></div>

<!-- 性能优化脚本 -->
<script>
  // 性能监控脚本
  (function() {
    // 记录页面加载时间
    window.performance.mark('app-start');

    // 定义关键指标监控
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        console.log(`[Performance] ${entry.name}: ${entry.startTime.toFixed(2)}ms`);
      });
    });
    observer.observe({ entryTypes: ['paint', 'largest-contentful-paint', 'layout-shift'] });

    // 监控长任务
    const longTaskObserver = new PerformanceObserver((list) => {
      list.getEntries().forEach(entry => {
        console.log(`[Long Task] Duration: ${entry.duration.toFixed(2)}ms`);
      });
    });
    longTaskObserver.observe({ entryTypes: ['longtask'] });

    // 延迟加载非关键资源
    window.addEventListener('load', () => {
      // 使用 requestIdleCallback 在浏览器空闲时加载非关键资源
      const loadNonCritical = () => {
        // 加载非关键 CSS
        const nonCriticalCSS = document.createElement('link');
        nonCriticalCSS.rel = 'stylesheet';
        nonCriticalCSS.href = '/css/non-critical.css';
        document.head.appendChild(nonCriticalCSS);

        // 预加载其他页面
        const pagesToPreload = [
          '/product',
          '/contact'
        ];

        pagesToPreload.forEach(page => {
          const link = document.createElement('link');
          link.rel = 'prefetch';
          link.href = page;
          document.head.appendChild(link);
        });
      };

      if ('requestIdleCallback' in window) {
        window.requestIdleCallback(loadNonCritical, { timeout: 2000 });
      } else {
        setTimeout(loadNonCritical, 2000);
      }
    });
  })();
</script>

<!-- 脚本优化 -->
<script type="module" src="/src/index.tsx" defer></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz"
        crossorigin="anonymous"
        defer></script>
</body>