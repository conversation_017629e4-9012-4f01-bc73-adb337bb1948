import  apiClient  from './apiClient';
import type { UserInfo, LoginRequest, LoginResponse, RegisterRequest } from '@/types/user';

// 用户资料类型
export interface UserProfile extends UserInfo {
  bio?: string;
  phone?: string;
  address?: string;
  company?: string;
  website?: string;
}

/**
 * 用户登录
 */
export const login = async (data: LoginRequest): Promise<LoginResponse> => {
  return apiClient.post<LoginResponse>('/auth/login', data);
};

/**
 * 用户注册
 */
export const register = async (data: RegisterRequest): Promise<LoginResponse> => {
  return apiClient.post<LoginResponse>('/auth/register', data);
};

/**
 * 获取用户资料
 */
export const fetchUserProfile = async (userId?: string): Promise<UserProfile> => {
  if (!userId) {
    throw new Error('用户ID不能为空');
  }
  
  try {
    return await apiClient.get<UserProfile>(`/users/${userId}/profile`);
  } catch (error) {
    console.error('获取用户资料失败:', error);
    
    // 返回模拟数据作为备用
    return {
      id: userId,
      username: '测试用户',
      email: '<EMAIL>',
      avatar: 'https://randomuser.me/api/portraits/lego/1.jpg',
      role: '普通用户',
      createdAt: new Date().toISOString(),
      bio: '这是一个测试用户',
    };
  }
};

/**
 * 更新用户资料
 */
export const updateUserProfile = async (userId: string, data: Partial<UserProfile>): Promise<UserProfile> => {
  try {
    return await apiClient.patch<UserProfile>(`/users/${userId}/profile`, data);
  } catch (error) {
    console.error('更新用户资料失败:', error);
    throw error;
  }
};