import apiClient from './apiClient';
import { faRocket, faShieldAlt, faClock, faCogs } from '@fortawesome/free-solid-svg-icons';
import type { FeatureItem, EnhancedFeature } from '../../types/feature';

// API 端点枚举
export enum FeatureApiEndpoints {
  Features = '/api/feature',
}

// 图标映射配置
const FEATURE_ICONS = [
  faRocket,   // 高性能
  faShieldAlt,// 安全可靠
  faClock,    // 24/7 服务
  faCogs      // 智能配置
];

/**
 * 获取特性列表
 */
export const fetchFeatures = async (): Promise<EnhancedFeature[]> => {
  try {
    const data = await apiClient.get<FeatureItem[]>(FeatureApiEndpoints.Features);

    // 增强特性数据
    return data.map((item, index) => ({
      ...item,
      id: `feature-${index}-${Date.now()}`,
      icon: FEATURE_ICONS[index % FEATURE_ICONS.length] || faCogs,
      description: item.text // 确保兼容 EnhancedFeature 接口的 description 字段

    }));
  } catch (error) {
    console.error('获取特性数据失败:', error);
    throw new Error('无法加载产品特性，请稍后重试');
  }
};

// 导出服务对象
export const featureService = {
  fetchFeatures,
};

export default featureService;