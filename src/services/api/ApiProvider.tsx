import React, { createContext, useContext } from 'react';
import apiClient from './apiClient';
import { userService, featureService, settingsService, productService } from './serviceFactory';
import { UserService } from './services/userService';
import { FeatureService } from '@/services/api/services';
import { SettingsService } from '@/services/api/services';
import { ProductService } from './services/productService';

// 创建服务上下文
interface ApiContextType {
  client: typeof apiClient;
  services: {
    user: UserService;
    feature: FeatureService;
    settings: SettingsService;
    product: ProductService;
  };
}

const ApiContext = createContext<ApiContextType | undefined>(undefined);

// API Provider 组件
export const ApiProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // 使用从 serviceFactory 导出的服务实例
  const services = {
    user: userService,
    feature: featureService,
    settings: settingsService,
    product: productService,
  };
  
  const apiContextValue: ApiContextType = {
    client: apiClient,
    services,
  };

  return (
    <ApiContext.Provider value={apiContextValue}>
      {children}
    </ApiContext.Provider>
  );
};

// 自定义钩子，用于访问API客户端
export const useApi = (): {
  request: <T>(config: any) => Promise<T>;
  get: <T>(url: string, config?: any) => Promise<T>;
  post: <T>(url: string, data?: any, config?: any) => Promise<T>;
  put: <T>(url: string, data?: any, config?: any) => Promise<T>;
  delete: <T>(url: string, config?: any) => Promise<T>;
  createAbortSignal: (timeoutMs?: number) => AbortSignal;
} => {
  const context = useContext(ApiContext);
  if (context === undefined) {
    throw new Error('useApi must be used within an ApiProvider');
  }
  return context.client;
};

// 自定义钩子，用于访问用户服务
export const useUserService = () => {
  const context = useContext(ApiContext);
  if (context === undefined) {
    throw new Error('useUserService must be used within an ApiProvider');
  }
  return context.services.user;
};

// 自定义钩子，用于访问特性服务
export const useFeatureService = () => {
  const context = useContext(ApiContext);
  if (context === undefined) {
    throw new Error('useFeatureService must be used within an ApiProvider');
  }
  return context.services.feature;
};

// 自定义钩子，用于访问设置服务
export const useSettingsService = () => {
  const context = useContext(ApiContext);
  if (context === undefined) {
    throw new Error('useSettingsService must be used within an ApiProvider');
  }
  return context.services.settings;
};

// 自定义钩子，用于访问产品服务
export const useProductService = () => {
  const context = useContext(ApiContext);
  if (context === undefined) {
    throw new Error('useProductService must be used within an ApiProvider');
  }
  return context.services.product;
};

export default ApiProvider;
