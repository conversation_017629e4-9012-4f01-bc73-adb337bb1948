import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { toast } from 'sonner';
import { userStore } from '../../store/userStore';
import env from '../../utils/env';
import {
  BaseResponse,
  PaginatedResponse,
  RequestConfig,
  ApiError,
  ErrorType,
  ErrorDetail,
  HTTP_STATUS,
  BUSINESS_CODE,
} from './types';

/**
 * 统一的 API 客户端
 * 提供标准化的请求处理、错误处理和响应格式
 */
class UnifiedApiClient {
  private instance: AxiosInstance;
  private defaultConfig: RequestConfig = {
    showLoading: true,
    showError: true,
    showSuccess: false,
    timeout: 10000,
    retryCount: 3,
  };

  constructor() {
    this.instance = axios.create({
      baseURL: env.VITE_API_BASE_URL,
      timeout: this.defaultConfig.timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 添加认证 token
        const token = userStore.getState().token;
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // 添加请求追踪ID
        config.headers['X-Trace-Id'] = this.generateTraceId();

        // 添加时间戳
        config.headers['X-Timestamp'] = Date.now().toString();

        return config;
      },
      (error) => {
        return Promise.reject(this.createApiError(error));
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        return this.handleSuccessResponse(response);
      },
      (error) => {
        return Promise.reject(this.handleErrorResponse(error));
      }
    );
  }

  /**
   * 处理成功响应
   */
  private handleSuccessResponse(response: AxiosResponse): AxiosResponse {
    const { data } = response;

    // 检查业务状态码
    if (data.code !== undefined && data.code !== BUSINESS_CODE.SUCCESS) {
      throw this.createApiError(
        data.message || '业务处理失败',
        data.code,
        data.data,
        data.traceId
      );
    }

    return response;
  }

  /**
   * 处理错误响应
   */
  private handleErrorResponse(error: any): ApiError {
    if (error instanceof ApiError) {
      return error;
    }

    const { response, request, message } = error;

    if (response) {
      // 服务器响应错误
      const { status, data } = response;
      return this.createApiError(
        data?.message || this.getDefaultErrorMessage(status),
        status,
        data,
        data?.traceId
      );
    } else if (request) {
      // 网络错误
      return this.createApiError(
        '网络连接失败，请检查网络设置',
        0,
        null,
        null,
        ErrorType.NETWORK_ERROR
      );
    } else {
      // 其他错误
      return this.createApiError(
        message || '发生未知错误',
        500,
        null,
        null,
        ErrorType.UNKNOWN_ERROR
      );
    }
  }

  /**
   * 创建 API 错误对象
   */
  private createApiError(
    message: string,
    code: number = 500,
    data?: any,
    traceId?: string,
    type?: ErrorType
  ): ApiError {
    const errorType = type || this.getErrorType(code);
    const apiError = new ApiError(message, code, data, traceId);
    
    // 添加错误类型
    (apiError as any).type = errorType;
    
    return apiError;
  }

  /**
   * 根据状态码获取错误类型
   */
  private getErrorType(code: number): ErrorType {
    if (code === 0) return ErrorType.NETWORK_ERROR;
    if (code === HTTP_STATUS.UNAUTHORIZED) return ErrorType.AUTH_ERROR;
    if (code === HTTP_STATUS.FORBIDDEN) return ErrorType.PERMISSION_ERROR;
    if (code >= 400 && code < 500) return ErrorType.CLIENT_ERROR;
    if (code >= 500) return ErrorType.SERVER_ERROR;
    return ErrorType.UNKNOWN_ERROR;
  }

  /**
   * 获取默认错误消息
   */
  private getDefaultErrorMessage(status: number): string {
    const messages: Record<number, string> = {
      [HTTP_STATUS.BAD_REQUEST]: '请求参数错误',
      [HTTP_STATUS.UNAUTHORIZED]: '未授权，请重新登录',
      [HTTP_STATUS.FORBIDDEN]: '权限不足',
      [HTTP_STATUS.NOT_FOUND]: '请求的资源不存在',
      [HTTP_STATUS.METHOD_NOT_ALLOWED]: '请求方法不允许',
      [HTTP_STATUS.TOO_MANY_REQUESTS]: '请求过于频繁，请稍后重试',
      [HTTP_STATUS.INTERNAL_SERVER_ERROR]: '服务器内部错误',
      [HTTP_STATUS.BAD_GATEWAY]: '网关错误',
      [HTTP_STATUS.SERVICE_UNAVAILABLE]: '服务暂时不可用',
      [HTTP_STATUS.GATEWAY_TIMEOUT]: '网关超时',
    };

    return messages[status] || '请求失败，请稍后重试';
  }

  /**
   * 生成请求追踪ID
   */
  private generateTraceId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 通用请求方法
   */
  public async request<T = any>(
    config: AxiosRequestConfig,
    requestConfig: RequestConfig = {}
  ): Promise<BaseResponse<T>> {
    const mergedConfig = { ...this.defaultConfig, ...requestConfig };

    try {
      // 显示加载状态
      if (mergedConfig.showLoading) {
        // 这里可以集成全局加载状态管理
      }

      const response = await this.instance.request<BaseResponse<T>>(config);
      
      // 显示成功提示
      if (mergedConfig.showSuccess && response.data.message) {
        toast.success(response.data.message);
      }

      return response.data;
    } catch (error) {
      const apiError = error as ApiError;
      
      // 自定义错误处理
      if (mergedConfig.customErrorHandler) {
        mergedConfig.customErrorHandler(apiError);
      } else if (mergedConfig.showError) {
        toast.error(apiError.message);
      }

      // 特殊错误处理
      if (apiError.code === HTTP_STATUS.UNAUTHORIZED) {
        // 清除用户信息并跳转到登录页
        userStore.getState().actions.clearUserInfo();
        window.location.href = '/login';
      }

      throw apiError;
    } finally {
      // 隐藏加载状态
      if (mergedConfig.showLoading) {
        // 这里可以集成全局加载状态管理
      }
    }
  }

  /**
   * GET 请求
   */
  public get<T = any>(
    url: string,
    params?: any,
    config?: RequestConfig
  ): Promise<BaseResponse<T>> {
    return this.request<T>({ method: 'GET', url, params }, config);
  }

  /**
   * POST 请求
   */
  public post<T = any>(
    url: string,
    data?: any,
    config?: RequestConfig
  ): Promise<BaseResponse<T>> {
    return this.request<T>({ method: 'POST', url, data }, config);
  }

  /**
   * PUT 请求
   */
  public put<T = any>(
    url: string,
    data?: any,
    config?: RequestConfig
  ): Promise<BaseResponse<T>> {
    return this.request<T>({ method: 'PUT', url, data }, config);
  }

  /**
   * DELETE 请求
   */
  public delete<T = any>(
    url: string,
    config?: RequestConfig
  ): Promise<BaseResponse<T>> {
    return this.request<T>({ method: 'DELETE', url }, config);
  }

  /**
   * 分页请求
   */
  public async getPaginated<T = any>(
    url: string,
    params?: any,
    config?: RequestConfig
  ): Promise<PaginatedResponse<T>> {
    const response = await this.request<T[]>({ method: 'GET', url, params }, config);
    return response as PaginatedResponse<T>;
  }
}

// 导出单例实例
export const unifiedApiClient = new UnifiedApiClient();
export default unifiedApiClient;
