import apiClient from './apiClient';
import { UserService } from './services/userService';
import { FeatureService } from '@/services/api/services';
import { SettingsService } from '@/services/api/services';
import { ProductService } from './services/productService';

/**
 * 服务工厂
 * 集中创建和管理所有服务实例，避免循环依赖
 */
class ServiceFactory {
  private services: Record<string, any> = {};
  
  constructor(private apiClient: any) {
    this.initServices();
  }
  
  private initServices() {
    // 创建所有服务实例
    this.services.user = new UserService(this.apiClient);
    this.services.feature = new FeatureService(this.apiClient);
    this.services.settings = new SettingsService(this.apiClient);
    this.services.product = new ProductService(this.apiClient);
  }
  
  /**
   * 获取用户服务
   */
  getUserService(): UserService {
    return this.services.user;
  }
  
  /**
   * 获取特性服务
   */
  getFeatureService(): FeatureService {
    return this.services.feature;
  }
  
  /**
   * 获取设置服务
   */
  getSettingsService(): SettingsService {
    return this.services.settings;
  }
  
  /**
   * 获取产品服务
   */
  getProductService(): ProductService {
    return this.services.product;
  }
}

// 创建服务工厂实例
export const serviceFactory = new ServiceFactory(apiClient);

// 导出各个服务实例，方便直接使用
export const userService = serviceFactory.getUserService();
export const featureService = serviceFactory.getFeatureService();
export const settingsService = serviceFactory.getSettingsService();
export const productService = serviceFactory.getProductService();
