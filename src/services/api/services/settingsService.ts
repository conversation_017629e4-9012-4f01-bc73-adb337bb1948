// 设置类型
export interface AppSettings {
  theme: string;
  language: string;
  notifications: boolean;
}

// API 端点枚举
export enum SettingsApiEndpoints {
  Settings = '/api/settings',
}

/**
 * 设置服务类
 */
export class SettingsService {
  constructor(private apiClient: any) {}

  /**
   * 获取用户设置
   */
  async getSettings(): Promise<AppSettings> {
    try {
      return await this.apiClient.get<AppSettings>(SettingsApiEndpoints.Settings);
    } catch (error) {
      console.error('获取设置失败:', error);
      // 返回默认设置
      return {
        theme: 'system',
        language: 'zh_CN',
        notifications: true
      };
    }
  }

  /**
   * 更新用户设置
   * @param settings 设置数据
   */
  async updateSettings(settings: Partial<AppSettings>): Promise<AppSettings> {
    return this.apiClient.put<AppSettings>(SettingsApiEndpoints.Settings, settings);
  }
}