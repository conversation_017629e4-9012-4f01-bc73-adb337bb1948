import type { UserInfo, UserToken } from '@/types/user';

// API 端点枚举
export enum UserApiEndpoints {
  SignIn = '/auth/signin',
  SignUp = '/auth/signup',
  Logout = '/auth/logout',
  Refresh = '/auth/refresh',
  User = '/user',
}

// 请求类型
export interface SignInRequest {
  username: string;
  password: string;
}

export interface SignUpRequest extends SignInRequest {
  email: string;
}

// 响应类型
export type SignInResponse = UserToken & { user: UserInfo };

/**
 * 用户服务类
 */
export class UserService {
  constructor(private apiClient: any) {}

  /**
   * 用户登录
   * @param data 登录信息
   */
  async signin(data: SignInRequest): Promise<SignInResponse> {
    return this.apiClient.post<SignInResponse>(UserApiEndpoints.SignIn, data);
  }

  /**
   * 用户注册
   * @param data 注册信息
   */
  async signup(data: SignUpRequest): Promise<SignInResponse> {
    return this.apiClient.post<SignInResponse>(UserApiEndpoints.SignUp, data);
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    return this.apiClient.get<void>(UserApiEndpoints.Logout);
  }

  /**
   * 根据ID查找用户
   * @param id 用户ID
   */
  async findUserById(id: string): Promise<UserInfo> {
    return this.apiClient.get<UserInfo>(`${UserApiEndpoints.User}/${id}`);
  }

  /**
   * 刷新令牌
   */
  async refreshToken(): Promise<UserToken> {
    return this.apiClient.get<UserToken>(UserApiEndpoints.Refresh);
  }
}

// 不在这里创建实例，而是由 serviceFactory 创建
