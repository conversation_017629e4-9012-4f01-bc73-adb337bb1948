import apiClient from '../apiClient';
import { Product } from '@/features/product/data/productData';

// 定义 API 客户端接口
interface IApiClient {
  get<T>(url: string, config?: any): Promise<T>;
  post<T>(url: string, data?: any, config?: any): Promise<T>;
  put<T>(url: string, data?: any, config?: any): Promise<T>;
  delete<T>(url: string, config?: any): Promise<T>;
}

// API 端点枚举
export enum ProductApiEndpoints {
  Products = '/api/products',
  ProductById = '/api/products/:id',
  ProductsInfo = '/api/products/info',
}

/**
 * 产品服务类
 */
export class ProductService {
  constructor(private apiClient: IApiClient) {}

  /**
   * 获取所有产品
   */
  async fetchProducts(): Promise<Product[]> {
    try {
      return await this.apiClient.get<Product[]>(ProductApiEndpoints.Products);
    } catch (error) {
      console.error('获取产品数据失败:', error);
      throw new Error('无法加载产品数据，请稍后重试');
    }
  }

  /**
   * 获取单个产品详情
   * @param id 产品ID
   */
  async fetchProductById(id: number): Promise<Product> {
    try {
      const endpoint = ProductApiEndpoints.ProductById.replace(':id', id.toString());
      return await this.apiClient.get<Product>(endpoint);
    } catch (error) {
      console.error(`获取产品ID=${id}数据失败:`, error);
      throw new Error('无法加载产品详情，请稍后重试');
    }
  }

  /**
   * 从 /products/info 路径获取产品信息
   * @returns 产品信息数组
   */
  async fetchProductsInfo(): Promise<Product[]> {
    try {
      return await this.apiClient.get<Product[]>(ProductApiEndpoints.ProductsInfo);
    } catch (error) {
      console.error('从 /products/info 获取产品数据失败:', error);
      throw new Error('无法从 /products/info 加载产品数据，请稍后重试');
    }
  }
}

// 导出服务实例
export const productService = new ProductService(apiClient);