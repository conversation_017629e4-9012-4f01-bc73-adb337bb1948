import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './App';
import './index.css';
import { startMockService } from './services/api/mockService';

// 在开发环境启动模拟服务
if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
  startMockService();
}

const container = document.getElementById('react');
if (!container) throw new Error('容器元素未找到');

const root = createRoot(container);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);