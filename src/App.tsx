import React from 'react';
import { AppRoutes } from './routes';
import { ScrollToTop } from './components/utility/ScrollToTop';
import { CookieConsent } from './features/cookie-consent';
import { AppProviders } from './components/providers/AppProviders';
import { DataLoader } from './components/providers/DataLoader';

// 导入样式
import "./App.css";

// 初始化平滑滚动功能
import SmoothScroll from "smooth-scroll";
export const scroll = new SmoothScroll('a[href*="#"]', {
  speed: 800,
  speedAsDuration: true,
});

/**
 * 应用程序主组件
 * 简化后的 App 组件，职责更加单一
 */
const App: React.FC = () => {
  const handleDataLoadError = (error: Error) => {
    // 可以在这里添加错误上报逻辑
    console.error('App data load error:', error);
  };

  return (
    <AppProviders>
      <DataLoader onError={handleDataLoadError}>
        {(data) => (
          <>
            <a href="#main-content" className="skip-nav">跳到主要内容</a>
            <ScrollToTop />
            <AppRoutes data={data} />
            <CookieConsent />
          </>
        )}
      </DataLoader>
    </AppProviders>
  );
};

export default App;