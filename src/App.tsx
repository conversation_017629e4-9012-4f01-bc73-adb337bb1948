import React, { useEffect, useState } from 'react';
import { <PERSON>rows<PERSON>Router as Router } from 'react-router-dom';
import { AppRoutes } from './routes';
import { ApiProvider } from './services/api/ApiProvider';
import { QueryProvider } from './services/query/QueryProvider';
import { ThemeProvider } from './theme/theme-provider';
import TrackerProvider from "./features/analytics/TrackerProvider"
import { ScrollToTop } from './components/utility/ScrollToTop';
import { CookieConsent } from './features/cookie-consent';

// 导入数据
import JsonData from './data/data.json';

// 导入样式
import "./App.css";

// 初始化平滑滚动功能
import SmoothScroll from "smooth-scroll";
import {LoadingSpinner} from "./components/feedback/LoadingSpinner";
export const scroll = new SmoothScroll('a[href*="#"]', {
  speed: 800,
  speedAsDuration: true,
});

const App: React.FC = () => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 模拟数据加载
    const loadData = async () => {
      try {
        // 在实际应用中，这里可能是从 API 获取初始数据
        setData(JsonData);
      } catch (error) {
        console.error('Failed to load initial data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <ApiProvider>
      <QueryProvider>
        <ThemeProvider>
          <TrackerProvider>
            <Router>
              <a href="#main-content" className="skip-nav">跳到主要内容</a>
              <ScrollToTop />
              <AppRoutes data={data} />
              <CookieConsent />
            </Router>
          </TrackerProvider>
        </ThemeProvider>
      </QueryProvider>
    </ApiProvider>
  );
};

export default App;