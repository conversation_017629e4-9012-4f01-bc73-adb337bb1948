import React from 'react';
import { IconWrapperProps } from './types';

/**
 * 图标包装器组件
 * 显示带有背景的图标
 * 
 * @param props - 图标包装器组件属性
 * @returns React组件
 */
export const IconWrapper: React.FC<IconWrapperProps> = ({
    icon,
    className = '',
    iconClass = 'fs-3 text-white',
    background = 'bg-primary',
    size = 48
}) => (
    <div
        className={`${className} p-3 ${background} rounded-circle d-inline-flex`}
        style={{
            width: size,
            height: size,
            minWidth: size,
            minHeight: size
        }}
        aria-hidden="true"
    >
        <i className={`${icon} ${iconClass}`}></i>
    </div>
);
