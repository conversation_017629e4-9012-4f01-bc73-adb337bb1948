/**
 * 公共组件类型定义
 */

/**
 * 图标包装器属性接口
 */
export interface IconWrapperProps {
  /** 图标类名 */
  icon: string;
  /** 自定义类名 */
  className?: string;
  /** 图标类名 */
  iconClass?: string;
  /** 背景类名 */
  background?: string;
  /** 尺寸 */
  size?: number;
}

/**
 * 加载旋转器属性接口
 */
export interface SpinnerProps {
  /** 尺寸 */
  size?: 'sm' | 'md' | 'lg';
  /** 自定义类名 */
  className?: string;
}

/**
 * 错误提示属性接口
 */
export interface ErrorAlertProps {
  /** 错误消息 */
  message: string;
  /** 警告类型 */
  type?: 'danger' | 'warning' | 'info';
  /** 是否可关闭 */
  dismissible?: boolean;
  /** 重试回调函数 */
  onRetry?: () => void;
  /** 自定义类名 */
  className?: string;
}
