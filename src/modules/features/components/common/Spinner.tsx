import React from 'react';
import { SpinnerProps } from './types';

/**
 * 加载旋转器组件
 * 显示加载中的旋转器
 * 
 * @param props - 加载旋转器组件属性
 * @returns React组件
 */
export const Spinner: React.FC<SpinnerProps> = ({ 
    size = 'md', 
    className = '' 
}) => {
    // 尺寸映射表
    const sizeMap: Record<string, string> = {
        sm: 'spinner-border-sm',
        md: '',
        lg: 'spinner-border-lg'
    };

    return (
        <div
            className={`spinner-border text-primary ${sizeMap[size]} ${className}`}
            role="status"
            aria-label="加载中"
        >
            <span className="visually-hidden">加载中...</span>
        </div>
    );
};
