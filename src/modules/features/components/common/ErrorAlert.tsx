import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCircleExclamation } from '@fortawesome/free-solid-svg-icons';

interface ErrorAlertProps {
    /** 必填的错误提示信息 */
    message: string;
    /** 警告类型，默认 danger */
    type?: 'danger' | 'warning' | 'info';
    /** 是否可关闭，默认 false */
    dismissible?: boolean;
    /** 重试回调函数 */
    onRetry?: () => void;
    /** 自定义类名 */
    className?: string;
}

export const ErrorAlert: React.FC<ErrorAlertProps> = ({
                                                          message,
                                                          type = 'danger',
                                                          dismissible = false,
                                                          onRetry,
                                                          className = ''
                                                      }) => {
    const [isDismissed, setIsDismissed] = React.useState(false);

    const alertClasses = [
        'alert',
        `alert-${type}`,
        dismissible ? 'alert-dismissible' : '',
        className
    ].join(' ').trim();

    if (isDismissed) return null;

    return (
        <div className={alertClasses} role="alert" aria-live="assertive">
            <div className="d-flex align-items-center gap-3">
                <FontAwesomeIcon
                    icon={faCircleExclamation}
                    className="flex-shrink-0"
                />
                <div className="flex-grow-1">
                    {message}
                    {onRetry && (
                        <button
                            onClick={onRetry}
                            className="btn btn-link btn-sm p-0 ms-2"
                            aria-label="Retry"
                        >
                            (Click to retry)
                        </button>
                    )}
                </div>
                {dismissible && (
                    <button
                        type="button"
                        className="btn-close"
                        onClick={() => setIsDismissed(true)}
                        aria-label="Close"
                    />
                )}
            </div>
        </div>
    );
};