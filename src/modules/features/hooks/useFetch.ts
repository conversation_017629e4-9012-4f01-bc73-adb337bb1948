import { useState, useEffect } from 'react';
import { FetchHookResult } from './types';

/**
 * 通用数据获取钩子
 * 提供数据获取、加载状态和错误处理功能
 * 
 * @param url - 请求URL
 * @returns 包含数据、加载状态和错误信息的对象
 */
export function useFetch<T>(url: string): FetchHookResult<T> {
    const [data, setData] = useState<T | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const abortController = new AbortController();

        const fetchData = async () => {
            try {
                const response = await fetch(url, { signal: abortController.signal });
                if (!response.ok) throw new Error('请求失败');
                const json = await response.json();
                setData(json);
                setError(null);
            } catch (err) {
                if (err instanceof Error && err.name !== 'AbortError') {
                    setError(err.message);
                }
            } finally {
                setLoading(false);
            }
        };

        fetchData();
        return () => abortController.abort();
    }, [url]);

    return { data, loading, error };
}

export default useFetch;
