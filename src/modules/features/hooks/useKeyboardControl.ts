import { useEffect, DependencyList } from 'react';
import { KeyboardControlHandler } from '../../../components/display/Image/types';

/**
 * 键盘控制钩子
 * 监听键盘事件并执行相应的处理函数
 * 
 * @param handler - 键盘事件处理函数
 * @param dependencies - 依赖数组，当依赖变化时重新设置事件监听
 */
export const useKeyboardControl = (
    handler: KeyboardControlHandler, 
    dependencies: DependencyList = []
): void => {
    useEffect(() => {
        // 键盘事件处理函数
        const handleKeyDown = (e: KeyboardEvent) => handler(e);
        
        // 添加事件监听
        document.addEventListener("keydown", handleKeyDown);
        
        // 清理函数，移除事件监听
        return () => document.removeEventListener("keydown", handleKeyDown);
    }, dependencies); // 依赖数组
};
