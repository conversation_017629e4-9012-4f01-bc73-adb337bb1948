import { useState, useCallback } from 'react';
import { ImageZoomHookResult } from '../../../components/display/Image/types';

/**
 * 图片缩放钩子
 * 提供图片缩放功能，在1倍和2倍缩放之间切换
 * 
 * @returns 包含当前缩放比例和切换函数的对象
 */
export const useImageZoom = (): ImageZoomHookResult => {
    // 缩放比例状态
    const [scale, setScale] = useState<number>(1);

    // 切换缩放状态的回调函数
    const toggleZoom = useCallback(() => {
        setScale(prev => prev === 1 ? 2 : 1);
    }, []);

    return { scale, toggleZoom };
};
