/**
 * 钩子类型定义
 */

/**
 * 特性数据接口
 */
export interface EnhancedFeature {
  /** 唯一标识符 */
  id: string;
  /** 标题 */
  title: string;
  /** 描述文本 */
  text: string;
  /** 图标 */
  icon?: any;
}

/**
 * 特性钩子返回值接口
 */
export interface FeaturesHookResult {
  /** 特性数据 */
  data: EnhancedFeature[];
  /** 是否加载中 */
  loading: boolean;
  /** 错误信息 */
  error: string | null;
}

/**
 * Fetch钩子返回值接口
 */
export interface FetchHookResult<T> {
  /** 数据 */
  data: T | null;
  /** 是否加载中 */
  loading: boolean;
  /** 错误信息 */
  error: string | null;
}

/**
 * 图片缩放钩子返回值接口
 */
export interface ImageZoomHookResult {
  /** 当前缩放比例 */
  scale: number;
  /** 切换缩放状态的函数 */
  toggleZoom: () => void;
}

/**
 * 键盘控制处理函数类型
 */
export type KeyboardControlHandler = (e: KeyboardEvent) => void;
