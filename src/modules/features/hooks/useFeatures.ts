import { useState, useEffect } from 'react';
import { FeatureService } from '@/core/api/features.ts';
import { EnhancedFeature, FeaturesHookResult } from './types';

/**
 * 特性数据钩子
 * 提供特性数据获取、加载状态和错误处理功能
 *
 * @returns 包含特性数据、加载状态和错误信息的对象
 */
export const useFeatures = (): FeaturesHookResult => {
    const [data, setData] = useState<EnhancedFeature[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const features = await FeatureService.fetchFeatures();
                setData(features);
                setError(null);
            } catch (err) {
                setError(err instanceof Error ? err.message : '未知错误');
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    return { data, loading, error };
};