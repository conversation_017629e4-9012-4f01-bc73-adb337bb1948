/* 全局布局样式 */
body {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

#react {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* 确保主要内容区域自动扩展 */
main {
  flex: 1 0 auto;
}

/* 确保页脚始终在底部 */
footer {
  flex-shrink: 0;
}

/* 现有的媒体查询保持不变 */
@media screen and (max-width: 400px) {
  #features {
    padding: 20px;
    width: 111%;
  }
  #about,
  #services,
  #testimonials,
  #team,
  #contact,
  #footer {
    width: 111%;
  }

  #portfolio {
    width: 110%;
  }

  .no-bg-color {
    background-color: transparent !important;
  }
}