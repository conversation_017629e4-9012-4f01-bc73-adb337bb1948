import type { BaseLayoutConfig } from '../BaseLayout';

/**
 * 预设的布局配置
 */

// 标准页面布局配置（原 TeamLayout）
export const standardPageConfig: BaseLayoutConfig = {
    showNavigation: true,
    showFooter: true,
    showContactFloatBar: true,
    showErrorBoundary: true,
    autoScrollToTop: true,
    mainClassName: "flex-grow-1 mt-5 pt-4",
    containerClassName: "d-flex flex-column min-vh-100",
    footerConfig: {
        recordNumbers: ["沪ICP备2025123281号", "沪ICP备2025123281号-1"]
    },
    contactFloatBarConfig: {
        themeColor: "#1890ff",
        top: "50%"
    }
};

// 产品页面布局配置
export const productPageConfig: BaseLayoutConfig = {
    ...standardPageConfig,
    contactFloatBarConfig: {
        ...standardPageConfig.contactFloatBarConfig,
        themeColor: "#52c41a", // 产品页面使用绿色主题
        top: "45%"
    }
};

// 案例展示页面布局配置
export const casePageConfig: BaseLayoutConfig = {
    ...standardPageConfig,
    contactFloatBarConfig: {
        ...standardPageConfig.contactFloatBarConfig,
        themeColor: "#722ed1", // 案例页面使用紫色主题
        top: "55%"
    }
};

// 联系页面布局配置
export const contactPageConfig: BaseLayoutConfig = {
    ...standardPageConfig,
    showContactFloatBar: false, // 联系页面不显示浮动联系条
    footerConfig: {
        ...standardPageConfig.footerConfig,
        // 联系页面可以有特殊的页脚配置
    }
};

// 团队页面布局配置
export const teamPageConfig: BaseLayoutConfig = {
    ...standardPageConfig,
    contactFloatBarConfig: {
        ...standardPageConfig.contactFloatBarConfig,
        themeColor: "#1677ff", // 团队页面使用蓝色主题
        top: "50%"
    }
};

// 简洁布局配置（无浮动条和页脚）
export const minimalPageConfig: BaseLayoutConfig = {
    showNavigation: true,
    showFooter: false,
    showContactFloatBar: false,
    showErrorBoundary: true,
    autoScrollToTop: true,
    mainClassName: "flex-grow-1 mt-5 pt-4",
    containerClassName: "d-flex flex-column min-vh-100"
};

// 全屏布局配置（无导航和页脚）
export const fullscreenPageConfig: BaseLayoutConfig = {
    showNavigation: false,
    showFooter: false,
    showContactFloatBar: false,
    showErrorBoundary: true,
    autoScrollToTop: false,
    mainClassName: "flex-grow-1",
    containerClassName: "d-flex flex-column min-vh-100"
};
