import Link from 'next/link';

// 产品数据
const productData = [
  {
    id: 'industrial-ice',
    title: '工业冰',
    image: '/img/portfolio/01-large.jpg',
    description: '专为工业应用设计的高质量冰块，适用于建筑、混凝土养护、食品加工等领域。我们的工业冰具有更长的融化时间和更高的冷却效率，帮助您降低成本并提高生产效率。',
    features: ['高冷却效率', '长时间保持低温', '定制尺寸', '大批量供应']
  },
  {
    id: 'edible-ice',
    title: '食用冰',
    image: '/img/portfolio/02-large.jpg',
    description: '符合食品安全标准的高品质食用冰，适用于餐饮、酒店、超市等场所。我们的食用冰采用纯净水制作，确保安全卫生，为您的饮品和食品提供完美的冷却解决方案。',
    features: ['食品级安全标准', '晶莹剔透', '多种形状可选', '快速配送服务']
  },
  {
    id: 'cooling-ice',
    title: '降温冰',
    image: '/img/portfolio/03-large.jpg',
    description: '专为环境降温设计的特殊冰块，适用于户外活动、工地降温、仓库温控等场景。我们的降温冰融化速度适中，能够持续释放冷量，有效改善高温环境。',
    features: ['高效降温', '环保无污染', '适用多种场景', '成本效益高']
  }
];

// 轮播数据
const heroData = {
  title: "上海寒链实业有限公司",
  paragraph: "专业冷链解决方案提供商，为您提供全方位的冷链物流服务"
};

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero 区域 */}
      <section className="relative h-96 bg-gradient-to-r from-blue-600 to-blue-800 flex items-center justify-center text-white">
        <div className="absolute inset-0 bg-black opacity-30"></div>
        <div className="relative z-10 text-center px-4">
          <h1 className="text-4xl md:text-6xl font-bold mb-4">
            {heroData.title}
          </h1>
          <p className="text-lg md:text-xl mb-8 max-w-2xl mx-auto">
            {heroData.paragraph}
          </p>
          <Link
            href="#products"
            className="inline-block bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            了解更多
          </Link>
        </div>
      </section>

      {/* 产品展示区域 */}
      <section id="products" className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">我们的产品系列</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              上海寒链实业有限公司提供多种高品质冰产品，满足不同行业和场景的需求。
              从工业应用到食品服务，我们都能提供专业的冷链解决方案。
            </p>
            <div className="w-20 h-1 bg-blue-600 mx-auto mt-6"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {productData.map(product => (
              <div key={product.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                {/* 产品图片占位符 */}
                <div className="h-48 bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center">
                  <div className="text-center text-blue-600">
                    <div className="text-4xl mb-2">
                      {product.id === 'industrial-ice' && '🏭'}
                      {product.id === 'edible-ice' && '🧊'}
                      {product.id === 'cooling-ice' && '❄️'}
                    </div>
                    <div className="text-lg font-medium">{product.title}</div>
                  </div>
                </div>

                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-3">{product.title}</h3>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {product.description}
                  </p>

                  <div className="mb-4">
                    {product.features.map((feature, index) => (
                      <span
                        key={index}
                        className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-2 mb-2"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>

                  <Link
                    href={`/products?category=${product.id}`}
                    className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium"
                  >
                    查看详情
                    <span className="ml-1">→</span>
                  </Link>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              href="/products"
              className="inline-block bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            >
              查看更多产品 →
            </Link>
          </div>
        </div>
      </section>

      {/* 特色服务区域 */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">为什么选择我们</h2>
            <p className="text-lg text-gray-600">
              专业的团队，优质的产品，完善的服务
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl text-blue-600">🏆</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">品质保证</h3>
              <p className="text-gray-600">
                严格的质量控制体系，确保每一个产品都达到最高标准
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl text-green-600">🚚</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">快速配送</h3>
              <p className="text-gray-600">
                覆盖全国的配送网络，确保产品及时送达
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl text-purple-600">🛠️</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">专业服务</h3>
              <p className="text-gray-600">
                专业的技术团队，提供全方位的技术支持和售后服务
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA 区域 */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">准备开始合作了吗？</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto">
            联系我们，获取专业的冷链解决方案和优质的产品服务
          </p>
          <div className="space-x-4">
            <Link
              href="/contact"
              className="inline-block bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              联系我们
            </Link>
            <Link
              href="/about"
              className="inline-block border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
            >
              了解更多
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
