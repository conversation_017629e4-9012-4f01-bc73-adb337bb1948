import type { Metadata, Viewport } from 'next';
import { AntdRegistry } from '@ant-design/nextjs-registry';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';

import './globals.css';

// 元数据配置
export const metadata: Metadata = {
  title: {
    default: 'Frost Chain - 专业制冰解决方案',
    template: '%s | Frost Chain',
  },
  description: '提供工业制冰、食用冰制品和制冰服务的专业解决方案，致力于为客户提供高质量的制冰产品和服务。',
  keywords: [
    '制冰',
    '工业制冰',
    '食用冰',
    '制冰设备',
    '制冰服务',
    '冷链物流',
    '制冰解决方案',
  ],
};

// 视口配置
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: '#1890ff',
};

interface RootLayoutProps {
  children: React.ReactNode;
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="zh-CN">
      <body className="min-h-screen bg-white text-gray-900 antialiased">
        {/* Ant Design 注册表 */}
        <AntdRegistry>
          <ConfigProvider
            locale={zhCN}
            theme={{
              token: {
                colorPrimary: '#1890ff',
                borderRadius: 6,
              },
            }}
          >
            <div className="flex min-h-screen flex-col">
              {/* 主内容区域 */}
              <main id="main-content" className="flex-1">
                {children}
              </main>
            </div>
          </ConfigProvider>
        </AntdRegistry>
      </body>
    </html>
  );
}
