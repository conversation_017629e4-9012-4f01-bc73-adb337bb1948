import type { Metadata, Viewport } from 'next';
import { AntdRegistry } from '@ant-design/nextjs-registry';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';

import { Navigation } from '@/components/layout/Navigation';
import { PageFooter } from '@/components/layout/PageFooter';
import { ContactFloatBar } from '@/components/layout/ContactFloatBar';

import './globals.css';

// 元数据配置
export const metadata: Metadata = {
  title: {
    default: 'Frost Chain - 专业制冰解决方案',
    template: '%s | Frost Chain',
  },
  description: '提供工业制冰、食用冰制品和制冰服务的专业解决方案，致力于为客户提供高质量的制冰产品和服务。',
  keywords: [
    '制冰',
    '工业制冰',
    '食用冰',
    '制冰设备',
    '制冰服务',
    '冷链物流',
    '制冰解决方案',
  ],
};

// 视口配置
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: '#1890ff',
};

interface RootLayoutProps {
  children: React.ReactNode;
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="zh-CN">
      <head>
        {/* Bootstrap CSS */}
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
          rel="stylesheet"
        />
        {/* Google Fonts */}
        <link
          href="https://fonts.googleapis.com/css2?family=Raleway:wght@300;400;600;700&family=Lato:wght@300;400;700&display=swap"
          rel="stylesheet"
        />
      </head>
      <body className="min-h-screen bg-white text-gray-900 antialiased">
        {/* Ant Design 注册表 */}
        <AntdRegistry>
          <ConfigProvider
            locale={zhCN}
            theme={{
              token: {
                colorPrimary: '#1890ff',
                borderRadius: 6,
              },
            }}
          >
            <div className="d-flex flex-column min-vh-100">
              {/* 跳转到主内容的链接（无障碍） */}
              <a
                href="#main-content"
                className="skip-nav visually-hidden-focusable"
              >
                跳转到主内容
              </a>

              {/* 导航栏 */}
              <Navigation />

              {/* 主内容区域 */}
              <main id="main-content" className="flex-grow-1" tabIndex={-1}>
                {children}
              </main>

              {/* 页脚 */}
              <PageFooter />

              {/* 联系浮动栏 */}
              <ContactFloatBar
                items={[
                  {
                    id: 'wechat',
                    icon: '💬',
                    title: '微信客服',
                    description: '扫码添加微信',
                    image: '/img/wechat-qr.jpg'
                  },
                  {
                    id: 'phone',
                    icon: '📞',
                    title: '电话咨询',
                    description: '13761182299',
                    link: 'tel:13761182299'
                  },
                  {
                    id: 'email',
                    icon: '✉️',
                    title: '邮件联系',
                    description: '<EMAIL>',
                    link: 'mailto:<EMAIL>'
                  }
                ]}
                themeColor="#1890ff"
                top="50%"
              />
            </div>
          </ConfigProvider>
        </AntdRegistry>

        {/* Bootstrap JS */}
        <script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"
          async
        />
      </body>
    </html>
  );
}
