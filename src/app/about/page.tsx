import { Metadata } from 'next';

// 页面元数据
export const metadata: Metadata = {
  title: '关于我们',
  description: '了解上海寒链实业有限公司的历史、使命和价值观。专业冷链解决方案提供商，为您提供全方位的冷链物流服务。',
};

export default function AboutPage() {
  return (
    <div className="min-h-screen">
      {/* 页面头部 */}
      <div className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">关于我们</h1>
            <p className="text-lg text-gray-600">
              了解我们的历史和使命
            </p>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            {/* 图片列 */}
            <div className="w-full h-80 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg shadow-lg flex items-center justify-center">
              <div className="text-center text-blue-600">
                <div className="text-6xl mb-4">🏭</div>
                <div className="text-lg font-medium">冷链物流专业团队</div>
              </div>
            </div>

            {/* 内容列 */}
            <div>
              <h2 className="text-3xl font-bold mb-6">关于我们</h2>

              <p className="text-lg leading-relaxed mb-6">
                专业冷链解决方案提供商，为您提供全方位的冷链物流服务。我们致力于为客户提供高质量、高效率的冷链物流解决方案，确保您的产品在运输过程中保持最佳状态。
              </p>

              <h3 className="text-xl font-semibold mb-4">选择我们的理由</h3>

              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">✓</span>
                  <span>纯净品质：每一块冰都经严格质检，晶莹无瑕，融化慢、无异味</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">✓</span>
                  <span>丰富选择：食用冰、碎冰、干冰、创意造型冰一应俱全</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">✓</span>
                  <span>灵活服务：按需定制，随时调整，拒绝浪费</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">✓</span>
                  <span>智能温控系统，确保产品质量</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">✓</span>
                  <span>全网直供，覆盖全国各大城市</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">✓</span>
                  <span>专业团队，提供一站式服务</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* 我们的故事部分 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-8">我们的故事</h2>

            <div className="space-y-6">
              <p className="text-lg">
                上海寒链实业有限公司成立于2010年，是一家专注于冷链物流和食用冰生产的企业。
                多年来，我们不断创新和发展，已成为行业内的领先企业。
              </p>

              <p className="text-lg">
                我们的团队由行业专家组成，拥有丰富的经验和专业知识，致力于为客户提供最优质的产品和服务。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 企业价值观 */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">
            我们的价值观
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6 bg-white rounded-lg shadow-sm border">
              <div className="mb-4">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-2xl text-blue-500">✓</span>
                </div>
              </div>
              <h4 className="text-xl font-semibold mb-3">品质第一</h4>
              <p className="text-gray-600">
                严格的质量控制体系，确保每一个产品都达到最高标准
              </p>
            </div>

            <div className="text-center p-6 bg-white rounded-lg shadow-sm border">
              <div className="mb-4">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-2xl text-green-500">✓</span>
                </div>
              </div>
              <h4 className="text-xl font-semibold mb-3">客户至上</h4>
              <p className="text-gray-600">
                以客户需求为导向，提供个性化的解决方案和优质服务
              </p>
            </div>

            <div className="text-center p-6 bg-white rounded-lg shadow-sm border">
              <div className="mb-4">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-2xl text-purple-500">✓</span>
                </div>
              </div>
              <h4 className="text-xl font-semibold mb-3">持续创新</h4>
              <p className="text-gray-600">
                不断投入研发，采用最新技术，推动行业发展
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
