import type { StateCreator } from 'zustand';
import { ThemeColorPresets, ThemeLayout, ThemeMode } from '../../../types/enum';

// 设置状态类型
export interface SettingsState {
  themeColorPresets: ThemeColorPresets;
  themeMode: ThemeMode;
  themeLayout: ThemeLayout;
  themeStretch: boolean;
  breadCrumb: boolean;
  accordion: boolean;
  multiTab: boolean;
  darkSidebar: boolean;
  fontFamily: string;
  fontSize: number;
  direction: "ltr" | "rtl";
}

// 设置操作类型
export interface SettingsActions {
  setThemeMode: (mode: ThemeMode) => void;
  setThemeColorPresets: (presets: ThemeColorPresets) => void;
  setThemeLayout: (layout: ThemeLayout) => void;
  setThemeStretch: (stretch: boolean) => void;
  setBreadCrumb: (breadCrumb: boolean) => void;
  setAccordion: (accordion: boolean) => void;
  setMultiTab: (multiTab: boolean) => void;
  setDarkSidebar: (darkSidebar: boolean) => void;
  setFontFamily: (fontFamily: string) => void;
  setFontSize: (fontSize: number) => void;
  setDirection: (direction: "ltr" | "rtl") => void;
  resetSettings: () => void;
  updateSettings: (settings: Partial<SettingsState>) => void;
}

// 设置 slice 类型
export interface SettingsSlice {
  settings: SettingsState;
  settingsActions: SettingsActions;
}

// 默认设置
const defaultSettings: SettingsState = {
  themeColorPresets: ThemeColorPresets.Default,
  themeMode: ThemeMode.Light,
  themeLayout: ThemeLayout.Vertical,
  themeStretch: false,
  breadCrumb: true,
  accordion: true,
  multiTab: true,
  darkSidebar: false,
  fontFamily: 'Inter, system-ui, sans-serif',
  fontSize: 14,
  direction: "ltr",
};

// 创建设置 slice
export const createSettingsSlice: StateCreator<
  SettingsSlice,
  [["zustand/immer", never]],
  [],
  SettingsSlice
> = (set) => ({
  settings: defaultSettings,
  
  settingsActions: {
    setThemeMode: (mode) =>
      set((state) => {
        state.settings.themeMode = mode;
      }),
      
    setThemeColorPresets: (presets) =>
      set((state) => {
        state.settings.themeColorPresets = presets;
      }),
      
    setThemeLayout: (layout) =>
      set((state) => {
        state.settings.themeLayout = layout;
      }),
      
    setThemeStretch: (stretch) =>
      set((state) => {
        state.settings.themeStretch = stretch;
      }),
      
    setBreadCrumb: (breadCrumb) =>
      set((state) => {
        state.settings.breadCrumb = breadCrumb;
      }),
      
    setAccordion: (accordion) =>
      set((state) => {
        state.settings.accordion = accordion;
      }),
      
    setMultiTab: (multiTab) =>
      set((state) => {
        state.settings.multiTab = multiTab;
      }),
      
    setDarkSidebar: (darkSidebar) =>
      set((state) => {
        state.settings.darkSidebar = darkSidebar;
      }),
      
    setFontFamily: (fontFamily) =>
      set((state) => {
        state.settings.fontFamily = fontFamily;
      }),
      
    setFontSize: (fontSize) =>
      set((state) => {
        state.settings.fontSize = fontSize;
      }),
      
    setDirection: (direction) =>
      set((state) => {
        state.settings.direction = direction;
      }),
      
    resetSettings: () =>
      set((state) => {
        state.settings = { ...defaultSettings };
      }),
      
    updateSettings: (newSettings) =>
      set((state) => {
        Object.assign(state.settings, newSettings);
      }),
  },
});
