import { create } from 'zustand';
import { persist, createJSONStorage, devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// 导入各个 slice
import { createUserSlice, type UserSlice } from './slices/userSlice';
import { createSettingsSlice, type SettingsSlice } from './slices/settingsSlice';
import { createFeatureSlice, type FeatureSlice } from './slices/featureSlice';

// 统一的状态类型
export interface UnifiedStoreState extends 
  UserSlice,
  SettingsSlice,
  FeatureSlice {}

// 创建统一的状态存储
export const useUnifiedStore = create<UnifiedStoreState>()(
  devtools(
    persist(
      immer((...args) => ({
        ...createUserSlice(...args),
        ...createSettingsSlice(...args),
        ...createFeatureSlice(...args),
      })),
      {
        name: 'unified-app-storage',
        storage: createJSONStorage(() => localStorage),
        partialize: (state) => ({
          // 只持久化必要的状态
          user: {
            token: state.user.token,
            isAuthenticated: state.user.isAuthenticated,
          },
          settings: state.settings,
        }),
      }
    ),
    { name: 'unified-store' }
  )
);

// 便捷的选择器 Hooks
export const useUser = () => useUnifiedStore((state) => state.user);
export const useUserActions = () => useUnifiedStore((state) => state.userActions);
export const useSettings = () => useUnifiedStore((state) => state.settings);
export const useSettingsActions = () => useUnifiedStore((state) => state.settingsActions);
export const useFeatures = () => useUnifiedStore((state) => state.features);
export const useFeatureActions = () => useUnifiedStore((state) => state.featureActions);

// 组合选择器
export const useAuth = () => {
  const user = useUser();
  const userActions = useUserActions();
  
  return {
    ...user,
    ...userActions,
    isAuthenticated: user.isAuthenticated,
  };
};

export const useTheme = () => {
  const settings = useSettings();
  const settingsActions = useSettingsActions();
  
  return {
    theme: settings.themeMode,
    isDarkMode: settings.themeMode === 'dark' ||
      (settings.themeMode === 'system' && 
       window.matchMedia('(prefers-color-scheme: dark)').matches),
    toggleTheme: () => {
      const currentTheme = settings.themeMode;
      if (currentTheme === 'light') {
        settingsActions.setThemeMode('dark');
      } else if (currentTheme === 'dark') {
        settingsActions.setThemeMode('system');
      } else {
        settingsActions.setThemeMode('light');
      }
    },
    setTheme: settingsActions.setThemeMode,
  };
};

// 导出类型
export type { UserSlice, SettingsSlice, FeatureSlice };
