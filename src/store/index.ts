import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { createUserSlice, UserSlice } from './slices/userSlice';
import { createSettingsSlice, SettingsSlice } from './slices/settingsSlice';
import { createFeatureSlice, FeatureSlice } from './slices/featureSlice';

// 全局状态类型
export interface StoreState extends 
  UserSlice,
  SettingsSlice,
  FeatureSlice {}

// 创建全局状态存储
export const useStore = create<StoreState>()(
  devtools(
    persist(
      immer((...a) => ({
        ...createUserSlice(...a),
        ...createSettingsSlice(...a),
        ...createFeatureSlice(...a),
      })),
      {
        name: 'app-storage',
        storage: createJSONStorage(() => localStorage),
        partialize: (state) => ({
          user: { token: state.user.token },
          settings: state.settings,
        }),
      }
    ),
    { name: 'global-store' }
  )
);

// 导出选择器
export const useUser = () => useStore((state) => state.user);
export const useUserActions = () => useStore((state) => state.userActions);

export const useSettings = () => useStore((state) => state.settings);
export const useSettingsActions = () => useStore((state) => state.settingsActions);

export const useFeatures = () => useStore((state) => state.features);
export const useFeaturesActions = () => useStore((state) => state.featuresActions);

// 导出统一的状态访问接口
export const useAppStore = () => {
  const user = useUser();
  const userActions = useUserActions();
  const settings = useSettings();
  const settingsActions = useSettingsActions();
  const features = useFeatures();
  const featuresActions = useFeaturesActions();

  return {
    user,
    userActions,
    settings,
    settingsActions,
    features,
    featuresActions,

    // 便捷访问方法
    isAuthenticated: user.isAuthenticated,
    isDarkMode: settings.theme === 'dark' ||
      (settings.theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches),

    // 便捷操作方法
    logout: userActions.logout,
    toggleTheme: () => {
      const currentTheme = settings.theme;
      if (currentTheme === 'light') {
        settingsActions.setTheme('dark');
      } else if (currentTheme === 'dark') {
        settingsActions.setTheme('system');
      } else {
        settingsActions.setTheme('light');
      }
    },
  };
};