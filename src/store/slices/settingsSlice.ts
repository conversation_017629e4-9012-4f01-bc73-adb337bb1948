import { StateCreator } from 'zustand';

// 设置状态
export interface SettingsState {
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: boolean;
}

// 设置操作
export interface SettingsActions {
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  setLanguage: (language: string) => void;
  toggleNotifications: () => void;
}

// 设置状态切片
export interface SettingsSlice {
  settings: SettingsState;
  settingsActions: SettingsActions;
}

export const createSettingsSlice: StateCreator<
  SettingsSlice,
  [["zustand/immer", never]],
  [],
  SettingsSlice
> = (set) => ({
  settings: {
    theme: 'system',
    language: 'zh_CN',
    notifications: true,
  },
  settingsActions: {
    setTheme: (theme) => set((state) => {
      state.settings.theme = theme;
    }),
    
    setLanguage: (language) => set((state) => {
      state.settings.language = language;
    }),
    
    toggleNotifications: () => set((state) => {
      state.settings.notifications = !state.settings.notifications;
    }),
  },
});