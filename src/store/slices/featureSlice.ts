import { StateCreator } from 'zustand';

// 功能状态
export interface FeatureState {
  enabledFeatures: string[];
  featureFlags: Record<string, boolean>;
}

// 功能操作
export interface FeatureActions {
  enableFeature: (feature: string) => void;
  disableFeature: (feature: string) => void;
  setFeatureFlag: (feature: string, enabled: boolean) => void;
}

// 功能状态切片
export interface FeatureSlice {
  features: FeatureState;
  featuresActions: FeatureActions;
}

export const createFeatureSlice: StateCreator<
  FeatureSlice,
  [["zustand/immer", never]],
  [],
  FeatureSlice
> = (set) => ({
  features: {
    enabledFeatures: [],
    featureFlags: {},
  },
  featuresActions: {
    enableFeature: (feature) => set((state) => {
      if (!state.features.enabledFeatures.includes(feature)) {
        state.features.enabledFeatures.push(feature);
      }
    }),
    
    disableFeature: (feature) => set((state) => {
      state.features.enabledFeatures = state.features.enabledFeatures.filter(
        (f: string) => f !== feature
      );
    }),
    
    setFeatureFlag: (feature, enabled) => set((state) => {
      state.features.featureFlags[feature] = enabled;
    }),
  },
});