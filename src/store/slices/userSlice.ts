import { StateCreator } from 'zustand';
import type { UserInfo } from '@/types/user';

// 用户状态
export interface UserState {
  token: string | null;
  user: UserInfo | null;
  isAuthenticated: boolean;
}

// 用户操作
export interface UserActions {
  setUserInfo: (data: { token: string; user: UserInfo }) => void;
  updateUser: (user: Partial<UserInfo>) => void;
  logout: () => void;
}

// 用户状态切片
export interface UserSlice {
  user: UserState;
  userActions: UserActions;
}

export const createUserSlice: StateCreator<
  UserSlice,
  [["zustand/immer", never]],
  [],
  UserSlice
> = (set) => ({
  user: {
    token: null,
    user: null,
    isAuthenticated: false,
  },
  userActions: {
    setUserInfo: (data) => set((state) => {
      state.user.token = data.token;
      state.user.user = data.user;
      state.user.isAuthenticated = true;
    }),
    
    updateUser: (userData) => set((state) => {
      if (state.user.user) {
        state.user.user = { ...state.user.user, ...userData };
      }
    }),
    
    logout: () => set((state) => {
      state.user.token = null;
      state.user.user = null;
      state.user.isAuthenticated = false;
    }),
  },
});