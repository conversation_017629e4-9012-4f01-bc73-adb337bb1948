import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { UserInfo } from '@/types/user';

// 用户状态类型
interface UserState {
  token: string | null;
  user: UserInfo | null;
  isAuthenticated: boolean;

  actions: {
    setUserInfo: (data: { token: string; user: UserInfo }) => void;
    updateUser: (user: Partial<UserInfo>) => void;
    logout: () => void;
  };
}

// 创建用户状态存储
export const userStore = create<UserState>()(
  persist(
    (set) => ({
      token: null,
      user: null,
      isAuthenticated: false,

      actions: {
        setUserInfo: (data: { token: string; user: UserInfo }) => set({
          token: data.token,
          user: data.user,
          isAuthenticated: true,
        }),

        updateUser: (userData: Partial<UserInfo>) => set((state: UserState) => ({
          user: state.user ? { ...state.user, ...userData } : null,
        })),

        logout: () => set({
          token: null,
          user: null,
          isAuthenticated: false,
        }),
      },
    }),
    {
      name: 'user-storage',
      partialize: (state) => ({ token: state.token }),
    }
  )
);

export default userStore;