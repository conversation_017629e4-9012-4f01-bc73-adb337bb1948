/* 团队页面样式 */
.team-img {
  height: 280px;
  object-fit: cover;
}

.hover-effect {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-effect:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:hover .overlay {
  opacity: 1;
}

.overlay-content {
  text-align: center;
  color: white;
  padding: 0 15px;
}

.social-links a {
  display: inline-block;
  width: 36px;
  height: 36px;
  line-height: 36px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  margin: 0 5px;
  transition: background 0.3s ease;
}

.social-links a:hover {
  background: rgba(255, 255, 255, 0.4);
}

.page-header {
  margin-top: 76px; /* 调整顶部间距，考虑固定导航栏的高度 */
}

/* 响应式调整 */
@media (max-width: 767px) {
  .team-img {
    height: 240px;
  }
}