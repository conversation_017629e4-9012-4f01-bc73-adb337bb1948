import { faRocket, faShieldAlt, faClock, faCogs } from '@fortawesome/free-solid-svg-icons';

// 图标映射配置
const FEATURE_ICONS = [
    faRocket,   // 高性能
    faShieldAlt,// 安全可靠
    faClock,    // 24/7 服务
    faCogs      // 智能配置
];

// API 客户端基础配置
interface ApiClient {
    baseURL: string;
    get(endpoint: string): Promise<any>;
}

const apiClient: ApiClient = {
    baseURL: 'http://localhost:8080',

    async get(endpoint: string) {
        const response = await fetch(`${this.baseURL}${endpoint}`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.json();
    }
};

// 特性服务模块
export const FeatureService = {
    async fetchFeatures() {
        try {
            const response = await apiClient.get('/api/feature');
            const result = await response; // 确保获取解析后的 JSON 数据

            return result.data.map((item: any, index: number) => ({
                ...item,
                id: `feature-${index}-${Date.now()}`,
                icon: FEATURE_ICONS[index % FEATURE_ICONS.length] || faCogs
            }));

        } catch (error) {
            console.error('获取特性数据失败:', error);
            throw new Error('无法加载产品特性，请稍后重试');
        }
    }
};


export interface EnhancedFeature {
    // 定义 EnhancedFeature 的结构

    title: string;
    text: string;
}