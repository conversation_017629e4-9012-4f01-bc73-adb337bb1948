// 注释: 为JSON模块添加类型声明
declare module '*.json' {
  const value: any;
  export default value;
}

// 注释: 为没有类型定义的第三方库添加声明
declare module 'smooth-scroll' {
  interface SmoothScrollOptions {
    speed?: number;
    speedAsDuration?: boolean;
    offset?: number | Function;
    easing?: string;
    updateURL?: boolean;
    popstate?: boolean;
    [key: string]: any;
  }

  class SmoothScroll {
    constructor(selector: string, options?: SmoothScrollOptions);
    init(): void;
    destroy(): void;
    animateScroll(
      anchor: HTMLElement | number,
      toggle?: HTMLElement,
      options?: SmoothScrollOptions
    ): void;
  }

  export default SmoothScroll;
}

// 注释: 为serviceWorker添加类型声明
declare module './serviceWorker' {
  export function register(config?: any): void;
  export function unregister(): void;
}

// 注释: 为CSS模块添加类型声明
declare module '*.css' {
  const classes: { [key: string]: string };
  export default classes;
}

// 注释: 为图片文件添加类型声明
declare module '*.png';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.svg' {
  import React from 'react';
  const SVG: React.FC<React.SVGProps<SVGSVGElement>>;
  export default SVG;
}