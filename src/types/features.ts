import { FeatureItemType, GalleryItemType, ProductItemType, ContactInfoType } from './common';

// 头部组件数据类型
export interface HeaderData {
  title: string;
  paragraph: string;
  buttonText?: string;
  buttonLink?: string;
  backgroundImage?: string;
}

// 特性组件数据类型
export interface FeaturesData {
  title?: string;
  description?: string;
  items: FeatureItemType[];
}

// 关于我们组件数据类型
export interface AboutData {
  title?: string;
  paragraph: string;
  why?: string[];
  image?: string;
}

// 产品组件数据类型
export interface ProductsData {
  title?: string;
  description?: string;
  products: ProductItemType[];
}

// 画廊组件数据类型
export interface GalleryData {
  title?: string;
  description?: string;
  gallery: GalleryItemType[];
}

// 联系我们组件数据类型
export interface ContactData extends ContactInfoType {
  title?: string;
  description?: string;
}

// 页面数据类型
export interface PageData {
  brandName?: string;
  Header?: HeaderData;
  Features?: FeaturesData;
  About?: AboutData;
  Products?: ProductsData;
  Gallery?: GalleryData;
  Contact?: ContactData;
}