import { useQuery } from '@tanstack/react-query';
import { useProductService } from '../services/api/ApiProvider';
import type { Product } from '../features/product/data/productData';
import { extendedProducts as fallbackProducts } from '../features/product/data/productCategories';

/**
 * 获取产品信息的 Hook
 * 从 /products/info 路径获取数据，失败时回退到静态数据
 */
export const useProductsInfo = () => {
  const productService = useProductService();

  return useQuery<Product[], Error>({
    queryKey: ['productsInfo'],
    queryFn: async () => {
      try {
        // 尝试从 API 获取数据
        return await productService.fetchProductsInfo();
      } catch (error) {
        // 如果 API 请求失败，使用静态数据作为备用
        console.warn('使用本地数据作为备用:', error);
        return fallbackProducts;
      }
    },
    staleTime: 5 * 60 * 1000, // 5分钟缓存
    retry: 1,
  });
};

export default useProductsInfo;
