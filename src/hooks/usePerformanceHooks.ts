import { useState, useEffect, useRef, useCallback, useMemo, DependencyList } from 'react';

/**
 * 使用防抖函数
 * 
 * @param fn - 要防抖的函数
 * @param delay - 延迟时间（毫秒）
 * @param deps - 依赖项数组
 * @returns 防抖后的函数
 */
export function useDebounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number,
  deps: DependencyList = []
): T {
  const callback = useCallback(fn, deps);
  
  const debounced = useRef<any>(null);
  
  useEffect(() => {
    return () => {
      if (debounced.current) {
        clearTimeout(debounced.current);
      }
    };
  }, []);
  
  return useMemo(() => {
    const debouncedFn = (...args: any[]) => {
      if (debounced.current) {
        clearTimeout(debounced.current);
      }
      
      debounced.current = setTimeout(() => {
        callback(...args);
      }, delay);
    };
    
    return debouncedFn as T;
  }, [callback, delay]);
}

/**
 * 使用节流函数
 * 
 * @param fn - 要节流的函数
 * @param limit - 时间限制（毫秒）
 * @param deps - 依赖项数组
 * @returns 节流后的函数
 */
export function useThrottle<T extends (...args: any[]) => any>(
  fn: T,
  limit: number,
  deps: DependencyList = []
): T {
  const callback = useCallback(fn, deps);
  const lastRun = useRef(0);
  const lastCall = useRef<any>(null);
  
  useEffect(() => {
    return () => {
      if (lastCall.current) {
        clearTimeout(lastCall.current);
      }
    };
  }, []);
  
  return useMemo(() => {
    const throttled = (...args: any[]) => {
      const now = Date.now();
      
      if (now - lastRun.current >= limit) {
        lastRun.current = now;
        return callback(...args);
      }
      
      if (lastCall.current) {
        clearTimeout(lastCall.current);
      }
      
      lastCall.current = setTimeout(() => {
        lastRun.current = Date.now();
        callback(...args);
      }, limit - (now - lastRun.current));
    };
    
    return throttled as T;
  }, [callback, limit]);
}

/**
 * 使用延迟值
 * 
 * @param value - 要延迟的值
 * @param delay - 延迟时间（毫秒）
 * @returns 延迟后的值
 */
export function useDeferredValue<T>(value: T, delay: number): T {
  const [deferredValue, setDeferredValue] = useState<T>(value);
  
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDeferredValue(value);
    }, delay);
    
    return () => clearTimeout(timeoutId);
  }, [value, delay]);
  
  return deferredValue;
}

/**
 * 使用异步计算
 * 
 * @param computeFunc - 计算函数
 * @param deps - 依赖项数组
 * @returns [计算结果, 是否正在计算, 错误]
 */
export function useAsyncComputation<T>(
  computeFunc: () => Promise<T>,
  deps: DependencyList = []
): [T | null, boolean, Error | null] {
  const [result, setResult] = useState<T | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  
  useEffect(() => {
    let isMounted = true;
    setLoading(true);
    
    computeFunc()
      .then((value) => {
        if (isMounted) {
          setResult(value);
          setLoading(false);
        }
      })
      .catch((err) => {
        if (isMounted) {
          setError(err);
          setLoading(false);
        }
      });
    
    return () => {
      isMounted = false;
    };
  }, deps);
  
  return [result, loading, error];
}

/**
 * 使用可见性检测
 * 
 * @param options - Intersection Observer 选项
 * @returns [ref, isVisible]
 */
export function useVisibility<T extends HTMLElement>(
  options: IntersectionObserverInit = { threshold: 0.1 }
): [React.RefObject<T>, boolean] {
  const ref = useRef<T>(null);
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    const element = ref.current;
    if (!element) return;
    
    const observer = new IntersectionObserver((entries) => {
      const [entry] = entries;
      setIsVisible(entry.isIntersecting);
    }, options);
    
    observer.observe(element);
    
    return () => {
      observer.disconnect();
    };
  }, [options]);
  
  return [ref, isVisible];
}

/**
 * 使用请求空闲回调
 * 
 * @param callback - 回调函数
 * @param deps - 依赖项数组
 */
export function useIdleCallback(
  callback: () => void,
  deps: DependencyList = []
): void {
  const savedCallback = useRef(callback);
  
  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);
  
  useEffect(() => {
    if (typeof window === 'undefined' || !('requestIdleCallback' in window)) {
      // 如果不支持 requestIdleCallback，则使用 setTimeout 作为后备
      const timeoutId = setTimeout(() => savedCallback.current(), 1);
      return () => clearTimeout(timeoutId);
    }
    
    const id = window.requestIdleCallback(() => savedCallback.current());
    return () => window.cancelIdleCallback(id);
  }, deps);
}
