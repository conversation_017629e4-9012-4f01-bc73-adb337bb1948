import { useEffect, useRef, useCallback, useState } from 'react';

/**
 * 性能指标接口
 */
interface PerformanceMetrics {
  /** 组件渲染时间 */
  renderTime: number;
  /** 组件挂载时间 */
  mountTime: number;
  /** 组件更新次数 */
  updateCount: number;
  /** 最后更新时间 */
  lastUpdateTime: number;
  /** 平均渲染时间 */
  averageRenderTime: number;
  /** 内存使用情况 */
  memoryUsage?: MemoryInfo;
}

/**
 * 性能监控配置
 */
interface PerformanceConfig {
  /** 组件名称 */
  componentName?: string;
  /** 是否启用监控 */
  enabled?: boolean;
  /** 是否记录详细日志 */
  verbose?: boolean;
  /** 性能阈值（毫秒） */
  threshold?: number;
  /** 是否监控内存使用 */
  monitorMemory?: boolean;
}

/**
 * 性能监控 Hook
 * 监控组件的渲染性能和生命周期
 */
export const usePerformanceMonitor = (config: PerformanceConfig = {}) => {
  const {
    componentName = 'Unknown Component',
    enabled = process.env.NODE_ENV === 'development',
    verbose = false,
    threshold = 16, // 16ms (60fps)
    monitorMemory = false,
  } = config;

  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    mountTime: 0,
    updateCount: 0,
    lastUpdateTime: 0,
    averageRenderTime: 0,
  });

  const mountTimeRef = useRef<number>(0);
  const renderStartTimeRef = useRef<number>(0);
  const renderTimesRef = useRef<number[]>([]);
  const updateCountRef = useRef<number>(0);
  const isFirstRenderRef = useRef<boolean>(true);

  /**
   * 开始性能测量
   */
  const startMeasure = useCallback(() => {
    if (!enabled) return;
    renderStartTimeRef.current = performance.now();
  }, [enabled]);

  /**
   * 结束性能测量
   */
  const endMeasure = useCallback(() => {
    if (!enabled || renderStartTimeRef.current === 0) return;

    const renderTime = performance.now() - renderStartTimeRef.current;
    renderTimesRef.current.push(renderTime);

    // 保持最近100次渲染的记录
    if (renderTimesRef.current.length > 100) {
      renderTimesRef.current.shift();
    }

    const averageRenderTime = 
      renderTimesRef.current.reduce((sum, time) => sum + time, 0) / 
      renderTimesRef.current.length;

    const now = Date.now();

    setMetrics(prev => ({
      ...prev,
      renderTime,
      lastUpdateTime: now,
      averageRenderTime,
      updateCount: updateCountRef.current,
      memoryUsage: monitorMemory ? getMemoryUsage() : undefined,
    }));

    // 性能警告
    if (renderTime > threshold) {
      console.warn(
        `⚠️ Performance Warning: ${componentName} render took ${renderTime.toFixed(2)}ms (threshold: ${threshold}ms)`
      );
    }

    // 详细日志
    if (verbose) {
      console.log(`📊 ${componentName} Performance:`, {
        renderTime: `${renderTime.toFixed(2)}ms`,
        averageRenderTime: `${averageRenderTime.toFixed(2)}ms`,
        updateCount: updateCountRef.current,
        memoryUsage: monitorMemory ? getMemoryUsage() : 'disabled',
      });
    }

    renderStartTimeRef.current = 0;
  }, [enabled, componentName, threshold, verbose, monitorMemory]);

  /**
   * 获取内存使用情况
   */
  const getMemoryUsage = useCallback((): MemoryInfo | undefined => {
    if ('memory' in performance) {
      return (performance as any).memory;
    }
    return undefined;
  }, []);

  /**
   * 记录组件挂载
   */
  const recordMount = useCallback(() => {
    if (!enabled) return;
    
    const mountTime = performance.now() - mountTimeRef.current;
    setMetrics(prev => ({ ...prev, mountTime }));

    if (verbose) {
      console.log(`🚀 ${componentName} mounted in ${mountTime.toFixed(2)}ms`);
    }
  }, [enabled, componentName, verbose]);

  /**
   * 记录组件更新
   */
  const recordUpdate = useCallback(() => {
    if (!enabled) return;
    
    updateCountRef.current += 1;
  }, [enabled]);

  /**
   * 重置性能指标
   */
  const resetMetrics = useCallback(() => {
    renderTimesRef.current = [];
    updateCountRef.current = 0;
    setMetrics({
      renderTime: 0,
      mountTime: 0,
      updateCount: 0,
      lastUpdateTime: 0,
      averageRenderTime: 0,
    });
  }, []);

  /**
   * 获取性能报告
   */
  const getPerformanceReport = useCallback(() => {
    const report = {
      componentName,
      metrics,
      recommendations: [],
    };

    // 性能建议
    const recommendations: string[] = [];
    
    if (metrics.averageRenderTime > threshold) {
      recommendations.push('考虑使用 React.memo 或 useMemo 优化渲染性能');
    }
    
    if (metrics.updateCount > 50) {
      recommendations.push('组件更新频繁，检查是否有不必要的重新渲染');
    }
    
    if (metrics.memoryUsage && metrics.memoryUsage.usedJSHeapSize > 50 * 1024 * 1024) {
      recommendations.push('内存使用较高，检查是否有内存泄漏');
    }

    return { ...report, recommendations };
  }, [componentName, metrics, threshold]);

  // 组件挂载时开始计时
  useEffect(() => {
    if (!enabled) return;
    
    mountTimeRef.current = performance.now();
    
    return () => {
      // 组件卸载时输出最终报告
      if (verbose) {
        console.log(`📋 ${componentName} Final Performance Report:`, getPerformanceReport());
      }
    };
  }, [enabled, componentName, verbose, getPerformanceReport]);

  // 每次渲染时记录性能
  useEffect(() => {
    if (!enabled) return;

    if (isFirstRenderRef.current) {
      isFirstRenderRef.current = false;
      recordMount();
    } else {
      recordUpdate();
    }

    endMeasure();
  });

  // 渲染开始时开始计时
  if (enabled) {
    startMeasure();
  }

  return {
    metrics,
    resetMetrics,
    getPerformanceReport,
    startMeasure,
    endMeasure,
  };
};

/**
 * 性能监控装饰器 HOC
 */
export const withPerformanceMonitor = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  config?: PerformanceConfig
) => {
  const ComponentWithPerformanceMonitor = (props: P) => {
    const componentName = config?.componentName || WrappedComponent.displayName || WrappedComponent.name;
    
    usePerformanceMonitor({
      ...config,
      componentName,
    });

    return <WrappedComponent {...props} />;
  };

  ComponentWithPerformanceMonitor.displayName = `withPerformanceMonitor(${componentName})`;
  
  return ComponentWithPerformanceMonitor;
};
