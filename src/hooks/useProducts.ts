import { useQuery } from '@tanstack/react-query';
import { useProductService } from '@/services/api/ApiProvider';
import type { Product } from '@/features/product/data/productData';
import { products as fallbackProducts } from '@/features/product/data/productData';

/**
 * 获取所有产品的 Hook
 */
export const useProducts = () => {
  const productService = useProductService();
  
  return useQuery<Product[], Error>({
    queryKey: ['products'],
    queryFn: async () => {
      try {
        return await productService.fetchProducts();
      } catch (error) {
        console.warn('使用本地数据作为备用:', error);
        return fallbackProducts;
      }
    },
    staleTime: 5 * 60 * 1000, // 5分钟缓存
    retry: 1,
  });
};

/**
 * 获取单个产品的 Hook
 * @param id 产品ID
 */
export const useProduct = (id: number) => {
  const productService = useProductService();
  
  return useQuery<Product, Error>({
    queryKey: ['product', id],
    queryFn: async () => {
      try {
        return await productService.fetchProductById(id);
      } catch (error) {
        console.warn(`使用本地数据作为备用 (ID=${id}):`, error);
        const product = fallbackProducts.find(p => p.id === id);
        if (!product) {
          throw new Error(`产品不存在 (ID=${id})`);
        }
        return product;
      }
    },
    staleTime: 5 * 60 * 1000, // 5分钟缓存
    retry: 1,
  });
};