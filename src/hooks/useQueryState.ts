import { useQuery, QueryKey } from '@tanstack/react-query';
import { useAppState } from './useAppState';

/**
 * 查询状态钩子
 * 集成 React Query 和 Zustand 状态
 */
export const useQueryState = <TData = unknown>(
  queryKey: QueryKey,
  queryFn: () => Promise<TData>,
  options: any = {}
) => {
  const { isAuthenticated } = useAppState();
  
  // 使用 React Query 获取数据
  const query = useQuery({
    queryKey,
    queryFn,
    enabled: options.enabled !== undefined ? options.enabled : isAuthenticated,
    ...options,
  });
  
  // 提供刷新方法
  const refresh = () => {
    query.refetch();
  };
  
  return {
    ...query,
    refresh,
  };
};