import { useCallback, useEffect } from 'react';
import { toast } from 'sonner';
import { ApiError, ErrorType, ErrorDetail } from '../services/api/types';

/**
 * 错误处理配置
 */
interface ErrorHandlerConfig {
  /** 是否显示错误提示 */
  showToast?: boolean;
  /** 是否上报错误 */
  reportError?: boolean;
  /** 是否记录错误日志 */
  logError?: boolean;
  /** 自定义错误处理函数 */
  onError?: (error: ErrorDetail) => void;
}

/**
 * 错误处理结果
 */
interface ErrorHandlerResult {
  /** 处理错误的函数 */
  handleError: (error: Error | ApiError | string) => void;
  /** 处理异步错误的函数 */
  handleAsyncError: (promise: Promise<any>) => Promise<any>;
  /** 清除错误状态 */
  clearError: () => void;
  /** 当前错误状态 */
  hasError: boolean;
  /** 最后一个错误 */
  lastError: ErrorDetail | null;
}

/**
 * 全局错误处理 Hook
 * 提供统一的错误处理机制
 */
export const useErrorHandler = (config: ErrorHandlerConfig = {}): ErrorHandlerResult => {
  const {
    showToast = true,
    reportError = true,
    logError = true,
    onError,
  } = config;

  /**
   * 创建错误详情对象
   */
  const createErrorDetail = useCallback((error: Error | ApiError | string): ErrorDetail => {
    if (typeof error === 'string') {
      return {
        type: ErrorType.UNKNOWN_ERROR,
        code: 500,
        message: error,
        timestamp: Date.now(),
      };
    }

    if (error instanceof ApiError) {
      return {
        type: (error as any).type || ErrorType.SERVER_ERROR,
        code: error.code,
        message: error.message,
        details: error.data,
        timestamp: Date.now(),
        traceId: error.traceId,
      };
    }

    // 普通 Error 对象
    return {
      type: ErrorType.UNKNOWN_ERROR,
      code: 500,
      message: error.message || '发生未知错误',
      details: {
        name: error.name,
        stack: error.stack,
      },
      timestamp: Date.now(),
    };
  }, []);

  /**
   * 获取用户友好的错误消息
   */
  const getUserFriendlyMessage = useCallback((errorDetail: ErrorDetail): string => {
    const { type, code, message } = errorDetail;

    // 根据错误类型返回用户友好的消息
    switch (type) {
      case ErrorType.NETWORK_ERROR:
        return '网络连接失败，请检查网络设置后重试';
      case ErrorType.TIMEOUT_ERROR:
        return '请求超时，请稍后重试';
      case ErrorType.AUTH_ERROR:
        return '登录已过期，请重新登录';
      case ErrorType.PERMISSION_ERROR:
        return '权限不足，无法执行此操作';
      case ErrorType.SERVER_ERROR:
        return '服务器暂时不可用，请稍后重试';
      case ErrorType.CLIENT_ERROR:
        if (code === 404) return '请求的资源不存在';
        if (code === 400) return '请求参数错误';
        return message || '请求失败，请检查输入信息';
      case ErrorType.BUSINESS_ERROR:
        return message || '操作失败，请稍后重试';
      default:
        return message || '发生未知错误，请稍后重试';
    }
  }, []);

  /**
   * 上报错误到监控系统
   */
  const reportErrorToMonitoring = useCallback((errorDetail: ErrorDetail) => {
    if (!reportError) return;

    try {
      // 这里可以集成错误监控服务，如 Sentry、Bugsnag 等
      console.group('🚨 Error Report');
      console.error('Error Detail:', errorDetail);
      console.error('User Agent:', navigator.userAgent);
      console.error('URL:', window.location.href);
      console.error('Timestamp:', new Date(errorDetail.timestamp).toISOString());
      console.groupEnd();

      // 发送到监控服务
      // errorMonitoringService.report(errorDetail);
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }, [reportError]);

  /**
   * 记录错误日志
   */
  const logErrorToConsole = useCallback((errorDetail: ErrorDetail) => {
    if (!logError) return;

    const { type, code, message, traceId } = errorDetail;
    const logMessage = `[${type}] ${code}: ${message}${traceId ? ` (Trace: ${traceId})` : ''}`;

    if (type === ErrorType.NETWORK_ERROR || type === ErrorType.SERVER_ERROR) {
      console.error(logMessage, errorDetail);
    } else if (type === ErrorType.CLIENT_ERROR) {
      console.warn(logMessage, errorDetail);
    } else {
      console.log(logMessage, errorDetail);
    }
  }, [logError]);

  /**
   * 显示错误提示
   */
  const showErrorToast = useCallback((errorDetail: ErrorDetail) => {
    if (!showToast) return;

    const message = getUserFriendlyMessage(errorDetail);
    const { type } = errorDetail;

    // 根据错误类型选择不同的提示样式
    switch (type) {
      case ErrorType.AUTH_ERROR:
      case ErrorType.PERMISSION_ERROR:
        toast.error(message, {
          duration: 5000,
          action: {
            label: '重新登录',
            onClick: () => {
              window.location.href = '/login';
            },
          },
        });
        break;
      case ErrorType.NETWORK_ERROR:
        toast.error(message, {
          duration: 4000,
          action: {
            label: '重试',
            onClick: () => {
              window.location.reload();
            },
          },
        });
        break;
      default:
        toast.error(message, { duration: 3000 });
    }
  }, [showToast, getUserFriendlyMessage]);

  /**
   * 主要的错误处理函数
   */
  const handleError = useCallback((error: Error | ApiError | string) => {
    const errorDetail = createErrorDetail(error);

    // 记录日志
    logErrorToConsole(errorDetail);

    // 上报错误
    reportErrorToMonitoring(errorDetail);

    // 显示提示
    showErrorToast(errorDetail);

    // 执行自定义错误处理
    onError?.(errorDetail);
  }, [createErrorDetail, logErrorToConsole, reportErrorToMonitoring, showErrorToast, onError]);

  /**
   * 处理异步错误
   */
  const handleAsyncError = useCallback(async (promise: Promise<any>) => {
    try {
      return await promise;
    } catch (error) {
      handleError(error as Error);
      throw error; // 重新抛出错误，让调用者可以进行进一步处理
    }
  }, [handleError]);

  /**
   * 清除错误状态
   */
  const clearError = useCallback(() => {
    // 这里可以清除全局错误状态
  }, []);

  // 全局错误监听
  useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      handleError(event.reason);
    };

    const handleGlobalError = (event: ErrorEvent) => {
      handleError(event.error || event.message);
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleGlobalError);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleGlobalError);
    };
  }, [handleError]);

  return {
    handleError,
    handleAsyncError,
    clearError,
    hasError: false, // 这里可以连接到全局错误状态
    lastError: null, // 这里可以连接到全局错误状态
  };
};
