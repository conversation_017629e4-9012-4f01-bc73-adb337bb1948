import { useQuery } from '@tanstack/react-query';
import { useFeatureService } from '@/services/api/ApiProvider';
import type { EnhancedFeature } from '@/types/feature';

export const useFeatures = () => {
  const featureService = useFeatureService();
  
  return useQuery<EnhancedFeature[], Error>({
    queryKey: ['features'],
    queryFn: () => featureService.fetchFeatures(),
    staleTime: 5 * 60 * 1000, // 5分钟缓存
    retry: 2,
  });
};

export default useFeatures;
