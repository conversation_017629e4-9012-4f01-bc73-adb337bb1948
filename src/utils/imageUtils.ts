/**
 * 图片工具函数
 */

/**
 * 获取图片的WebP版本URL
 * 如果浏览器支持WebP，返回WebP版本的URL
 * 
 * @param originalUrl - 原始图片URL
 * @returns WebP版本URL或原始URL
 */
export const getWebPUrl = (originalUrl: string): string => {
  // 检查URL是否已经是WebP
  if (originalUrl.endsWith('.webp')) {
    return originalUrl;
  }
  
  // 检查是否为常见图片格式
  const isImage = /\.(jpe?g|png|gif)$/i.test(originalUrl);
  
  if (!isImage) {
    return originalUrl;
  }
  
  // 构建WebP版本URL
  // 假设服务器支持通过修改扩展名或添加查询参数来提供WebP版本
  // 方法1: 替换扩展名
  const webpUrl = originalUrl.replace(/\.(jpe?g|png|gif)$/i, '.webp');
  
  // 方法2: 添加查询参数 (取消注释以使用)
  // const webpUrl = originalUrl + '?format=webp';
  
  return webpUrl;
};

/**
 * 生成响应式图片srcset属性
 * 
 * @param baseUrl - 基础图片URL
 * @param widths - 宽度数组
 * @param extension - 图片扩展名
 * @returns srcset字符串
 */
export const generateSrcSet = (
  baseUrl: string,
  widths: number[] = [320, 640, 960, 1280, 1920],
  extension: string = 'jpg'
): string => {
  // 移除现有扩展名
  const urlBase = baseUrl.replace(/\.\w+$/, '');
  
  // 生成srcset字符串
  return widths
    .map(width => `${urlBase}-${width}.${extension} ${width}w`)
    .join(', ');
};

/**
 * 计算图片的宽高比例
 * 
 * @param width - 宽度
 * @param height - 高度
 * @returns 宽高比例字符串 (例如: "16/9")
 */
export const calculateAspectRatio = (width: number, height: number): string => {
  const gcd = (a: number, b: number): number => (b === 0 ? a : gcd(b, a % b));
  const divisor = gcd(width, height);
  return `${width / divisor}/${height / divisor}`;
};
