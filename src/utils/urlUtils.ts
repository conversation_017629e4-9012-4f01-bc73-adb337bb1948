import env, { isDev } from './env';

/**
 * 获取基础URL
 * 根据当前环境返回适当的基础URL
 * 
 * @returns 基础URL字符串
 */
export const getBaseUrl = (): string => {
  if (isDev) {
    // 开发环境使用localhost
    return 'http://localhost:3003';
  } else {
    // 生产环境使用实际域名
    return 'https://frologi.com';
  }
};

/**
 * 生成完整URL
 * 
 * @param path 路径，如 '/product'
 * @returns 完整URL
 */
export const getFullUrl = (path: string): string => {
  const baseUrl = getBaseUrl();
  // 确保路径以/开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  return `${baseUrl}${normalizedPath}`;
};

/**
 * 生成产品详情页URL
 * 
 * @param productId 产品ID
 * @returns 产品详情页完整URL
 */
export const getProductDetailUrl = (productId: number | string): string => {
  return getFullUrl(`/product/${productId}`);
};

/**
 * 生成产品列表页URL
 * 
 * @returns 产品列表页完整URL
 */
export const getProductListUrl = (): string => {
  return getFullUrl('/product');
};

/**
 * 生成首页URL
 * 
 * @returns 首页完整URL
 */
export const getHomeUrl = (): string => {
  return getFullUrl('/');
};
