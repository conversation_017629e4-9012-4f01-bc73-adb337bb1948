// 导入 UUID 生成器，用于生成唯一标识符
import { v4 as uuidv4 } from 'uuid';
// 导入隐私检查工具函数
import { checkPrivacyConsent } from './privacy';
// 导入企业级封装的 HTTP 客户端
import apiClient from "./../services/api/apiClient";

// 配置项调整为客户端默认配置
const config = {
    enableLog: process.env.NODE_ENV !== 'production',
    batchInterval: 5000,
    maxRetry: 3,
};

// 增强型队列管理
let reportQueue = [];
let isSending = false;

// UV ID 生成器（增加存储空间检查）
const getUVId = () => {
    const storageKey = 'uv_id'; // 存储键名
    try {
        let uvId = localStorage.getItem(storageKey); // 从 localStorage 获取 UV ID
        if (!uvId) {
            uvId = uuidv4(); // 如果不存在，则生成新的 UUID
            const storage = localStorage.length >= 4900 ? sessionStorage : localStorage; // 判断存储空间是否接近 5MB 阈值
            storage.setItem(storageKey, uvId); // 存储 UV ID
        }
        return uvId;
    } catch (e) {
        // 如果 localStorage 不可用，则使用 sessionStorage 或生成新的 UUID
        return sessionStorage.getItem(storageKey) || uuidv4();
    }
};

// 增强型发送方法
const sendData = async (events) => {
    if (!checkPrivacyConsent()) return false; // 检查用户隐私同意状态

    try {
        // 构建符合后端要求的数据结构
        const payload = {
            events, // 事件数组
            metadata: { // 元数据
                timestamp: Date.now(),
                uvId: getUVId(),
                sdkVersion: '1.2.0',
                userAgent: navigator.userAgent,
                currentPath: window.location.pathname
            }
        };

        // 发送数据到后端
        const response = await apiClient.post('/analytics/collect', payload, {
            timeout: 5000, // 超时时间
            headers: { 'X-Data-Type': 'batch' }, // 自定义请求头
            signal: AbortSignal.timeout(5000), // 请求超时控制
        });

        // 判断响应状态是否成功
        return response.status >= 200 && response.status < 300;
    } catch (error) {
        if (config.enableLog) {
            console.warn('[Tracker] 上报失败:', error); // 打印错误日志
        }
        return false;
    }
};

// 队列处理（支持并发控制）
const flushQueue = async () => {
    if (isSending || reportQueue.length === 0) return; // 如果正在发送或队列为空，则直接返回

    isSending = true; // 设置发送标志
    const currentBatch = [...reportQueue]; // 复制当前队列
    reportQueue = []; // 清空队列

    try {
        const success = await sendData(currentBatch); // 尝试发送数据

        if (!success) {
            // 如果发送失败，筛选需要重试的事件
            const retryItems = currentBatch.filter(item =>
                (item.retryCount || 0) < config.maxRetry // 检查重试次数是否未超限
            ).map(item => ({
                ...item,
                retryCount: (item.retryCount || 0) + 1, // 增加重试次数
                lastRetry: Date.now() // 记录最后一次重试时间
            }));

            reportQueue.push(...retryItems); // 将需要重试的事件重新加入队列
        }
    } finally {
        isSending = false; // 重置发送标志
    }
};

// 增强型埋点方法
export const trackEvent = (eventType, eventData = {}) => {
    if (config.enableLog) {
        console.log('[Tracker]', eventType, eventData);
    }

    const eventPayload = {
        uvId: getUVId(),
        timestamp: Date.now(),
        path: window.location.pathname,
        userAgent: navigator.userAgent,
        ...eventData,
        eventType,
        sdkVersion: '1.2.0'
    };

    reportQueue.push(eventPayload);

    // 关键事件立即发送 + 节流控制
    if (['pageview', 'error', 'purchase'].includes(eventType)) {
        const forceFlush = () => {
            flushQueue().catch(() => {
                setTimeout(forceFlush, 1000);
            });
        };
        forceFlush();
    }
};

// 性能监控集成
export const trackPerformance = () => {
    if (window.performance?.getEntriesByType) {
        const [navigationEntry] = performance.getEntriesByType('navigation');
        if (navigationEntry) {
            trackEvent('performance', {
                dns: navigationEntry.domainLookupEnd - navigationEntry.domainLookupStart,
                tcp: navigationEntry.connectEnd - navigationEntry.connectStart,
                ttfb: navigationEntry.responseStart - navigationEntry.requestStart,
                domLoad: navigationEntry.domContentLoadedEventEnd - navigationEntry.startTime,
                fullLoad: navigationEntry.loadEventEnd - navigationEntry.startTime,
            });
        }
    }

    // 资源性能监控
    if (window.PerformanceObserver) {
        const resourceObserver = new PerformanceObserver(list => {
            list.getEntries().forEach(entry => {
                trackEvent('resource', {
                    name: entry.name,
                    type: entry.initiatorType,
                    duration: entry.duration.toFixed(2),
                    size: entry.decodedBodySize || 0,
                });
            });
        });
        resourceObserver.observe({ entryTypes: ['resource'] });
    }
};

// 智能批量处理策略
let flushTimer = null;
const scheduleFlush = () => {
    if (!flushTimer) {
        flushTimer = setTimeout(() => {
            flushQueue().finally(() => {
                flushTimer = null;
            });
        }, config.batchInterval);
    }
};

// 事件驱动队列处理
window.addEventListener('scroll', scheduleFlush, { passive: true });
document.addEventListener('click', scheduleFlush, { passive: true });

// 页面生命周期管理
const handleVisibilityChange = () => {
    if (document.visibilityState === 'hidden' && reportQueue.length > 0) {
        try {
            // 构造 Beacon 数据（已包含统一数据结构）
            const payload = {
                //events: reportQueue,
                metadata: {
                    timestamp: Date.now(),
                    uvId: getUVId(),
                    sdkVersion: '1.2.0',
                    userAgent: navigator.userAgent,
                    currentPath: window.location.pathname
                }
            };

            const url = apiClient.sendBeacon('/analytics/collect');
            const data = new Blob(
                [JSON.stringify(payload)],
                { type: 'application/json' }
            );
            console.log('url', url);
            console.log('payload', payload);

            // 优先使用 Beacon API
            if (typeof url === 'string' && navigator.sendBeacon(url, data)) {
                reportQueue = [];
                return;
            }

            // Beacon 不可用时的降级方案
            apiClient.post('/analytics/collect', payload, {
                headers: { 'Content-Type': 'application/json' },
                keepalive: true,
                timeout: 2000
            }).then(() => {
                reportQueue = [];
            }).catch(() => {
                if (config.enableLog) {
                    console.warn('[Tracker] 页面隐藏时上报失败，等待下次重试');
                }
            });

        } catch (error) {
            if (config.enableLog) {
                console.error('[Tracker] 页面隐藏上报异常:', error);
            }
        }
    }
};

// 优化后的监听逻辑
const setupVisibilityListener = () => {
    if (typeof document !== 'undefined') {
        document.addEventListener('visibilitychange', handleVisibilityChange);
        window.addEventListener('beforeunload', handleVisibilityChange);
    }
};

// 在初始化时调用
setupVisibilityListener();
document.addEventListener('visibilitychange', handleVisibilityChange);
window.addEventListener('beforeunload', handleVisibilityChange);

// 自动批量处理
setInterval(scheduleFlush, config.batchInterval);