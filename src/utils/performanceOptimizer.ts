/**
 * 性能优化器
 * 提供全面的性能监控和优化功能
 */

import { unifiedApiClient } from '../services/api/unifiedApiClient';

// 性能指标接口
export interface PerformanceMetrics {
  /** 页面加载时间 */
  pageLoadTime: number;
  /** 首次内容绘制时间 */
  firstContentfulPaint: number;
  /** 最大内容绘制时间 */
  largestContentfulPaint: number;
  /** 首次输入延迟 */
  firstInputDelay: number;
  /** 累积布局偏移 */
  cumulativeLayoutShift: number;
  /** 内存使用情况 */
  memoryUsage: MemoryInfo | null;
  /** 网络连接信息 */
  connectionInfo: NetworkInformation | null;
}

// 性能报告接口
export interface PerformanceReport {
  /** 页面URL */
  url: string;
  /** 用户代理 */
  userAgent: string;
  /** 性能指标 */
  metrics: PerformanceMetrics;
  /** 时间戳 */
  timestamp: number;
  /** 会话ID */
  sessionId: string;
  /** 用户ID */
  userId?: string;
}

// 性能阈值配置
export interface PerformanceThresholds {
  /** 页面加载时间阈值（毫秒） */
  pageLoadTime: number;
  /** FCP 阈值（毫秒） */
  firstContentfulPaint: number;
  /** LCP 阈值（毫秒） */
  largestContentfulPaint: number;
  /** FID 阈值（毫秒） */
  firstInputDelay: number;
  /** CLS 阈值 */
  cumulativeLayoutShift: number;
  /** 内存使用阈值（MB） */
  memoryUsage: number;
}

// 默认性能阈值
const DEFAULT_THRESHOLDS: PerformanceThresholds = {
  pageLoadTime: 3000,           // 3秒
  firstContentfulPaint: 1800,   // 1.8秒
  largestContentfulPaint: 2500, // 2.5秒
  firstInputDelay: 100,         // 100毫秒
  cumulativeLayoutShift: 0.1,   // 0.1
  memoryUsage: 100,             // 100MB
};

/**
 * 性能优化器类
 */
class PerformanceOptimizer {
  private thresholds: PerformanceThresholds;
  private sessionId: string;
  private reportQueue: PerformanceReport[] = [];
  private observer: PerformanceObserver | null = null;
  private isEnabled: boolean = true;

  constructor(thresholds: Partial<PerformanceThresholds> = {}) {
    this.thresholds = { ...DEFAULT_THRESHOLDS, ...thresholds };
    this.sessionId = this.generateSessionId();
    this.init();
  }

  /**
   * 初始化性能监控
   */
  private init(): void {
    if (!this.isEnabled || typeof window === 'undefined') return;

    // 监听页面加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.collectInitialMetrics();
      });
    } else {
      this.collectInitialMetrics();
    }

    // 监听页面卸载
    window.addEventListener('beforeunload', () => {
      this.flushReports();
    });

    // 设置定期上报
    setInterval(() => {
      this.flushReports();
    }, 30000); // 30秒上报一次
  }

  /**
   * 收集初始性能指标
   */
  private collectInitialMetrics(): void {
    // 延迟收集，确保所有指标都可用
    setTimeout(() => {
      const metrics = this.collectMetrics();
      const report = this.createReport(metrics);
      this.addReport(report);
      this.checkThresholds(metrics);
    }, 1000);
  }

  /**
   * 收集性能指标
   */
  private collectMetrics(): PerformanceMetrics {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const paint = performance.getEntriesByType('paint');
    
    // 页面加载时间
    const pageLoadTime = navigation ? navigation.loadEventEnd - navigation.fetchStart : 0;
    
    // 首次内容绘制时间
    const fcpEntry = paint.find(entry => entry.name === 'first-contentful-paint');
    const firstContentfulPaint = fcpEntry ? fcpEntry.startTime : 0;
    
    // 最大内容绘制时间
    let largestContentfulPaint = 0;
    if ('PerformanceObserver' in window) {
      try {
        const lcpEntries = performance.getEntriesByType('largest-contentful-paint');
        if (lcpEntries.length > 0) {
          largestContentfulPaint = lcpEntries[lcpEntries.length - 1].startTime;
        }
      } catch (error) {
        console.warn('LCP measurement failed:', error);
      }
    }
    
    // 首次输入延迟（需要用户交互才能测量）
    let firstInputDelay = 0;
    
    // 累积布局偏移（需要观察器）
    let cumulativeLayoutShift = 0;
    
    // 内存使用情况
    const memoryUsage = this.getMemoryUsage();
    
    // 网络连接信息
    const connectionInfo = this.getConnectionInfo();

    return {
      pageLoadTime,
      firstContentfulPaint,
      largestContentfulPaint,
      firstInputDelay,
      cumulativeLayoutShift,
      memoryUsage,
      connectionInfo,
    };
  }

  /**
   * 获取内存使用情况
   */
  private getMemoryUsage(): MemoryInfo | null {
    if ('memory' in performance) {
      return (performance as any).memory;
    }
    return null;
  }

  /**
   * 获取网络连接信息
   */
  private getConnectionInfo(): NetworkInformation | null {
    if ('connection' in navigator) {
      return (navigator as any).connection;
    }
    return null;
  }

  /**
   * 创建性能报告
   */
  private createReport(metrics: PerformanceMetrics): PerformanceReport {
    return {
      url: window.location.href,
      userAgent: navigator.userAgent,
      metrics,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      userId: this.getCurrentUserId(),
    };
  }

  /**
   * 获取当前用户ID
   */
  private getCurrentUserId(): string | undefined {
    // 这里可以从状态管理或本地存储中获取用户ID
    try {
      const userInfo = localStorage.getItem('userInfo');
      if (userInfo) {
        const user = JSON.parse(userInfo);
        return user.id;
      }
    } catch (error) {
      console.warn('Failed to get user ID:', error);
    }
    return undefined;
  }

  /**
   * 添加报告到队列
   */
  private addReport(report: PerformanceReport): void {
    this.reportQueue.push(report);
    
    // 限制队列大小
    if (this.reportQueue.length > 50) {
      this.reportQueue.shift();
    }
  }

  /**
   * 检查性能阈值
   */
  private checkThresholds(metrics: PerformanceMetrics): void {
    const warnings: string[] = [];

    if (metrics.pageLoadTime > this.thresholds.pageLoadTime) {
      warnings.push(`页面加载时间过长: ${metrics.pageLoadTime}ms (阈值: ${this.thresholds.pageLoadTime}ms)`);
    }

    if (metrics.firstContentfulPaint > this.thresholds.firstContentfulPaint) {
      warnings.push(`首次内容绘制时间过长: ${metrics.firstContentfulPaint}ms (阈值: ${this.thresholds.firstContentfulPaint}ms)`);
    }

    if (metrics.largestContentfulPaint > this.thresholds.largestContentfulPaint) {
      warnings.push(`最大内容绘制时间过长: ${metrics.largestContentfulPaint}ms (阈值: ${this.thresholds.largestContentfulPaint}ms)`);
    }

    if (metrics.memoryUsage && metrics.memoryUsage.usedJSHeapSize > this.thresholds.memoryUsage * 1024 * 1024) {
      warnings.push(`内存使用过高: ${(metrics.memoryUsage.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB (阈值: ${this.thresholds.memoryUsage}MB)`);
    }

    if (warnings.length > 0) {
      console.group('⚠️ 性能警告');
      warnings.forEach(warning => console.warn(warning));
      console.groupEnd();
    }
  }

  /**
   * 上报性能数据
   */
  private async flushReports(): Promise<void> {
    if (this.reportQueue.length === 0) return;

    const reports = [...this.reportQueue];
    this.reportQueue = [];

    try {
      await unifiedApiClient.post('/analytics/performance', {
        reports,
      }, {
        showLoading: false,
        showError: false,
        showSuccess: false,
      });
    } catch (error) {
      console.warn('Failed to report performance data:', error);
      // 失败的报告重新加入队列
      this.reportQueue.unshift(...reports);
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 手动收集性能指标
   */
  public collectCurrentMetrics(): PerformanceMetrics {
    return this.collectMetrics();
  }

  /**
   * 获取性能报告
   */
  public getPerformanceReport(): PerformanceReport {
    const metrics = this.collectMetrics();
    return this.createReport(metrics);
  }

  /**
   * 启用/禁用性能监控
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * 更新性能阈值
   */
  public updateThresholds(thresholds: Partial<PerformanceThresholds>): void {
    this.thresholds = { ...this.thresholds, ...thresholds };
  }

  /**
   * 强制上报当前数据
   */
  public async forceReport(): Promise<void> {
    const metrics = this.collectMetrics();
    const report = this.createReport(metrics);
    this.addReport(report);
    await this.flushReports();
  }
}

// 创建全局性能优化器实例
export const performanceOptimizer = new PerformanceOptimizer();

// 导出性能监控装饰器
export const withPerformanceTracking = <T extends (...args: any[]) => any>(
  fn: T,
  name: string
): T => {
  return ((...args: any[]) => {
    const startTime = performance.now();
    
    try {
      const result = fn(...args);
      
      // 如果是 Promise，等待完成后记录
      if (result instanceof Promise) {
        return result.finally(() => {
          const endTime = performance.now();
          console.log(`🚀 ${name} 执行时间: ${(endTime - startTime).toFixed(2)}ms`);
        });
      }
      
      // 同步函数直接记录
      const endTime = performance.now();
      console.log(`🚀 ${name} 执行时间: ${(endTime - startTime).toFixed(2)}ms`);
      return result;
    } catch (error) {
      const endTime = performance.now();
      console.error(`❌ ${name} 执行失败 (${(endTime - startTime).toFixed(2)}ms):`, error);
      throw error;
    }
  }) as T;
};

// 导出性能测试工具
export const performanceTest = {
  /**
   * 测试函数执行时间
   */
  time: async <T>(fn: () => T | Promise<T>, name: string): Promise<T> => {
    const startTime = performance.now();
    try {
      const result = await fn();
      const endTime = performance.now();
      console.log(`⏱️ ${name}: ${(endTime - startTime).toFixed(2)}ms`);
      return result;
    } catch (error) {
      const endTime = performance.now();
      console.error(`❌ ${name} 失败 (${(endTime - startTime).toFixed(2)}ms):`, error);
      throw error;
    }
  },

  /**
   * 测试内存使用
   */
  memory: (name: string): void => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      console.log(`💾 ${name} 内存使用:`, {
        used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
        total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
        limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`,
      });
    }
  },
};
