/**
 * CSS 加载优化工具
 */

/**
 * 延迟加载 CSS 文件
 * 
 * @param href - CSS 文件的 URL
 * @param media - 媒体查询，默认为 'all'
 * @returns 创建的 link 元素
 */
export function loadCSS(href: string, media: string = 'all'): HTMLLinkElement {
  // 创建 link 元素
  const link = document.createElement('link');
  link.rel = 'stylesheet';
  link.href = href;
  link.media = 'print';
  
  // 添加 onload 处理程序
  link.onload = () => {
    link.media = media;
  };
  
  // 添加到文档头部
  document.head.appendChild(link);
  
  return link;
}

/**
 * 预加载 CSS 文件
 * 
 * @param href - CSS 文件的 URL
 * @returns 创建的 link 元素
 */
export function preloadCSS(href: string): HTMLLinkElement {
  // 创建 link 元素
  const link = document.createElement('link');
  link.rel = 'preload';
  link.as = 'style';
  link.href = href;
  
  // 添加到文档头部
  document.head.appendChild(link);
  
  return link;
}

/**
 * 预连接到域
 * 
 * @param href - 域的 URL
 * @param crossOrigin - 是否使用跨域
 * @returns 创建的 link 元素
 */
export function preconnect(href: string, crossOrigin: boolean = false): HTMLLinkElement {
  // 创建 link 元素
  const link = document.createElement('link');
  link.rel = 'preconnect';
  link.href = href;
  
  if (crossOrigin) {
    link.crossOrigin = 'anonymous';
  }
  
  // 添加到文档头部
  document.head.appendChild(link);
  
  return link;
}

/**
 * 加载关键 CSS 内联
 * 
 * @param css - CSS 内容
 * @returns 创建的 style 元素
 */
export function inlineCSS(css: string): HTMLStyleElement {
  // 创建 style 元素
  const style = document.createElement('style');
  style.textContent = css;
  
  // 添加到文档头部
  document.head.appendChild(style);
  
  return style;
}

/**
 * 延迟加载非关键 CSS
 * 
 * @param hrefs - CSS 文件的 URL 数组
 * @param delay - 延迟时间（毫秒），默认为 0
 */
export function loadNonCriticalCSS(hrefs: string[], delay: number = 0): void {
  // 使用 requestIdleCallback 或 setTimeout 延迟加载
  const load = () => {
    hrefs.forEach(href => loadCSS(href));
  };
  
  if (delay > 0) {
    setTimeout(load, delay);
  } else if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
    window.requestIdleCallback(load);
  } else {
    setTimeout(load, 1);
  }
}
