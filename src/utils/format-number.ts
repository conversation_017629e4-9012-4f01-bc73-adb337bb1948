// src/utils/formatNumber.ts

type InputValue = string | number | null | undefined;

/**
 * 通用数字格式化
 * @example fNumber(1234.567) => "1,234.567"
 */
export function fNumber(value: InputValue, maximumFractionDigits = 4): string {
	return formatNumber(value, { maximumFractionDigits });
}

/**
 * 货币格式化
 * @example fCurrency(1234.56) => "$1,234.56" (en-US)
 */
export function fCurrency(
	value: InputValue,
	currency: string = "USD",
	locale: string = "en-US"
): string {
	return formatNumber(value, {
		style: "currency",
		currency,
		currencyDisplay: "symbol",
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	}, locale);
}

/**
 * 百分比格式化
 * @example fPercent(0.1234) => "12.34%"
 */
export function fPercent(
	value: InputValue,
	maximumFractionDigits = 2,
	locale: string = "en-US"
): string {
	return formatNumber(value, {
		style: "percent",
		maximumFractionDigits,
	}, locale);
}

/**
 * 核心格式化函数
 */
function formatNumber(
	value: InputValue,
	options: Intl.NumberFormatOptions = {},
	locale: string = "en-US"
): string {
	if (value === null || value === undefined || value === "") return "";

	const numericValue = Number(value);
	if (isNaN(numericValue)) return "";

	return new Intl.NumberFormat(locale, options).format(numericValue);
}