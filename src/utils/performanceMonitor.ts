/**
 * 性能监控工具
 * 用于监控和报告页面性能指标
 */

// 性能指标类型
interface PerformanceMetrics {
  FCP?: number;  // First Contentful Paint
  LCP?: number;  // Largest Contentful Paint
  FID?: number;  // First Input Delay
  CLS?: number;  // Cumulative Layout Shift
  TTI?: number;  // Time to Interactive
  TBT?: number;  // Total Blocking Time
  TTFB?: number; // Time to First Byte
  [key: string]: number | undefined;
}

// 初始化性能监控
export function initPerformanceMonitoring(
  reportCallback?: (metrics: PerformanceMetrics) => void,
  debugMode = false
) {
  // 存储性能指标
  const metrics: PerformanceMetrics = {};
  
  // 检查浏览器是否支持性能API
  if (!window.performance || !window.PerformanceObserver) {
    console.warn('Performance API not supported in this browser');
    return;
  }
  
  // 记录指标
  const recordMetric = (name: string, value: number) => {
    metrics[name] = value;
    if (debugMode) {
      console.log(`Performance metric - ${name}: ${value}`);
    }
    
    // 如果提供了回调函数，则调用它
    if (reportCallback) {
      reportCallback({ ...metrics });
    }
  };
  
  // 监控 FCP (First Contentful Paint)
  try {
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      if (entries.length > 0) {
        const fcp = entries[0];
        recordMetric('FCP', fcp.startTime);
      }
    }).observe({ type: 'paint', buffered: true });
  } catch (e) {
    console.warn('FCP monitoring not supported', e);
  }
  
  // 监控 LCP (Largest Contentful Paint)
  try {
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      if (entries.length > 0) {
        // 取最后一个 LCP 事件，因为它是最大的内容绘制
        const lcp = entries[entries.length - 1];
        recordMetric('LCP', lcp.startTime);
      }
    }).observe({ type: 'largest-contentful-paint', buffered: true });
  } catch (e) {
    console.warn('LCP monitoring not supported', e);
  }
  
  // 监控 FID (First Input Delay)
  try {
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      if (entries.length > 0) {
        const firstInput = entries[0];
        // @ts-ignore - processingStart 属性在 TS 类型中可能不存在
        const fid = firstInput.processingStart - firstInput.startTime;
        recordMetric('FID', fid);
      }
    }).observe({ type: 'first-input', buffered: true });
  } catch (e) {
    console.warn('FID monitoring not supported', e);
  }
  
  // 监控 CLS (Cumulative Layout Shift)
  try {
    let clsValue = 0;
    let clsEntries: PerformanceEntry[] = [];
    
    // 创建一个新的 PerformanceObserver 实例
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      
      // 将新条目添加到现有条目中
      clsEntries = [...clsEntries, ...entries];
      
      // 计算 CLS 值
      let sessionValue = 0;
      let sessionEntries: PerformanceEntry[] = [];
      let sessionGap = 0;
      
      clsEntries.forEach((entry) => {
        // @ts-ignore - value 属性在 TS 类型中可能不存在
        const entryValue = entry.value;
        
        if (sessionValue && entry.startTime - sessionGap > 1000) {
          // 如果会话间隔超过 1 秒，则开始一个新会话
          sessionValue = 0;
          sessionEntries = [];
        }
        
        sessionValue += entryValue;
        sessionEntries.push(entry);
        sessionGap = entry.startTime;
        
        if (sessionValue > clsValue) {
          clsValue = sessionValue;
          recordMetric('CLS', clsValue);
        }
      });
    }).observe({ type: 'layout-shift', buffered: true });
  } catch (e) {
    console.warn('CLS monitoring not supported', e);
  }
  
  // 监控 TTFB (Time to First Byte)
  try {
    // 使用 Navigation Timing API
    window.addEventListener('load', () => {
      const navigationTiming = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigationTiming) {
        const ttfb = navigationTiming.responseStart;
        recordMetric('TTFB', ttfb);
      }
    });
  } catch (e) {
    console.warn('TTFB monitoring not supported', e);
  }
  
  // 监控长任务 (Long Tasks)
  try {
    let totalBlockingTime = 0;
    
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      
      entries.forEach((entry) => {
        // 计算超过 50ms 的阻塞时间
        const blockingTime = entry.duration - 50;
        if (blockingTime > 0) {
          totalBlockingTime += blockingTime;
          recordMetric('TBT', totalBlockingTime);
        }
      });
    }).observe({ type: 'longtask', buffered: true });
  } catch (e) {
    console.warn('Long tasks monitoring not supported', e);
  }
  
  // 返回当前收集的指标
  return {
    getMetrics: () => ({ ...metrics }),
    
    // 手动记录自定义指标
    recordCustomMetric: (name: string, value: number) => {
      recordMetric(name, value);
    }
  };
}

// 使用示例:
// const monitor = initPerformanceMonitoring(
//   (metrics) => {
//     // 发送指标到分析服务
//     console.log('Performance metrics:', metrics);
//   },
//   true // 开启调试模式
// );
//
// // 记录自定义指标
// monitor?.recordCustomMetric('AppInitTime', performance.now());
