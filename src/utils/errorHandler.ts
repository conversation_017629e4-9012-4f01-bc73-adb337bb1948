import { AxiosError } from 'axios';
import { toast } from 'react-toastify';
import { t } from '@/locales/i18n';

// API 错误类型
export interface ApiError {
  status: number;
  message: string;
  code?: string;
  details?: Record<string, any>;
}

// 错误处理函数
export const handleApiError = (error: unknown): ApiError => {
  // Axios 错误
  if (error instanceof AxiosError && error.response) {
    const { status, data } = error.response;
    
    // 处理常见 HTTP 状态码
    switch (status) {
      case 400:
        toast.error(t('errors.badRequest'));
        break;
      case 401:
        toast.error(t('errors.unauthorized'));
        break;
      case 403:
        toast.error(t('errors.forbidden'));
        break;
      case 404:
        toast.error(t('errors.notFound'));
        break;
      case 500:
        toast.error(t('errors.serverError'));
        break;
      default:
        toast.error(t('errors.unknown'));
    }
    
    return {
      status,
      message: data?.message || error.message,
      code: data?.code,
      details: data?.details,
    };
  }
  
  // 普通 Error 对象
  if (error instanceof Error) {
    toast.error(error.message);
    return {
      status: 0,
      message: error.message,
    };
  }
  
  // 未知错误
  const errorMessage = String(error) || t('errors.unknown');
  toast.error(errorMessage);
  
  return {
    status: 0,
    message: errorMessage,
  };
};

export default handleApiError;