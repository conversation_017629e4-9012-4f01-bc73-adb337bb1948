/**
 * 创建一个 Web Worker 来执行耗时操作
 * 
 * @param workerFunction - 要在 Worker 中执行的函数
 * @returns 一个包含 run 方法的对象，用于执行 Worker 函数
 */
export function createWorker<T, R>(workerFunction: (data: T) => R) {
  // 将函数转换为字符串
  const functionStr = workerFunction.toString();
  
  // 创建 Worker 代码
  const workerCode = `
    self.onmessage = function(e) {
      const workerFunction = ${functionStr};
      const result = workerFunction(e.data);
      self.postMessage(result);
    }
  `;
  
  // 创建 Blob URL
  const blob = new Blob([workerCode], { type: 'application/javascript' });
  const workerUrl = URL.createObjectURL(blob);
  
  // 创建 Worker
  const worker = new Worker(workerUrl);
  
  return {
    /**
     * 在 Worker 中执行函数
     * 
     * @param data - 传递给 Worker 的数据
     * @returns 一个 Promise，解析为 Worker 的结果
     */
    run(data: T): Promise<R> {
      return new Promise((resolve, reject) => {
        // 设置消息处理程序
        worker.onmessage = (e) => {
          resolve(e.data as R);
        };
        
        // 设置错误处理程序
        worker.onerror = (error) => {
          reject(error);
          worker.terminate();
        };
        
        // 发送数据到 Worker
        worker.postMessage(data);
      });
    },
    
    /**
     * 终止 Worker
     */
    terminate() {
      worker.terminate();
      URL.revokeObjectURL(workerUrl);
    }
  };
}

/**
 * 使用示例:
 * 
 * // 创建一个执行耗时计算的 Worker
 * const calculationWorker = createWorker((data: number[]) => {
 *   // 耗时计算
 *   return data.map(x => x * x).reduce((a, b) => a + b, 0);
 * });
 * 
 * // 使用 Worker 执行计算
 * calculationWorker.run([1, 2, 3, 4, 5])
 *   .then(result => console.log(result))
 *   .catch(error => console.error(error))
 *   .finally(() => calculationWorker.terminate());
 */
