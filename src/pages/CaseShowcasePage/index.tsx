import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './styles.module.scss';

// 案例分类枚举
enum CaseCategory {
  COOLING = 'cooling',
  EDIBLE = 'edible',
  INDUSTRIAL = 'industrial'
}

// 案例数据接口
export interface CaseItem {
  id: string;
  category: CaseCategory;
  clientName: string;
  industry: string;
  thumbnail: string;
  scenario: string;
  description?: string;
  images?: string[];
}

// 模拟案例数据
const caseData: CaseItem[] = [
  {
    id: '1',
    category: CaseCategory.COOLING,
    clientName: '某高端酒店',
    industry: '酒店行业',
    thumbnail: '/images/cases/hotel.jpg',
    scenario: '中央空调系统降温',
    description: '采用定制冰晶降温系统，节能30%',
    images: ['/images/cases/hotel-1.jpg', '/images/cases/hotel-2.jpg']
  },
  {
    id: '2',
    category: CaseCategory.EDIBLE,
    clientName: '某连锁餐厅',
    industry: '餐饮行业',
    thumbnail: '/images/cases/restaurant.jpg',
    scenario: '食品保鲜与展示',
    description: '使用食用级冰块，保证食品安全',
    images: ['/images/cases/restaurant-1.jpg']
  },
  {
    id: '3',
    category: CaseCategory.INDUSTRIAL,
    clientName: '某化工厂',
    industry: '化工行业',
    thumbnail: '/images/cases/factory.jpg',
    scenario: '生产设备冷却',
    description: '工业级冰块用于高温设备降温',
    images: ['/images/cases/factory-1.jpg', '/images/cases/factory-2.jpg']
  }
];

const CaseShowcasePage: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<CaseCategory>(CaseCategory.COOLING);
  const navigate = useNavigate();

  // 过滤当前分类的案例
  const filteredCases = caseData.filter(caseItem => caseItem.category === activeCategory);

  // 处理案例点击
  const handleCaseClick = (caseId: string) => {
    navigate(`/cases/${caseId}`);
  };

  return (
    <div className={styles.container}>
      <h1 className={styles.title}>客户案例展示</h1>

      {/* 分类切换标签 */}
      <div className={styles.tabs}>
        {Object.values(CaseCategory).map(category => (
          <button
            key={category}
            className={`${styles.tab} ${activeCategory === category ? styles.active : ''}`}
            onClick={() => setActiveCategory(category)}
          >
            {category === CaseCategory.COOLING && '降温冰'}
            {category === CaseCategory.EDIBLE && '食用冰'}
            {category === CaseCategory.INDUSTRIAL && '工业冰'}
          </button>
        ))}
      </div>

      {/* 案例列表 */}
      <div className={styles.caseList}>
        {filteredCases.map(caseItem => (
          <div
            key={caseItem.id}
            className={styles.card}
            onClick={() => handleCaseClick(caseItem.id)}
          >
            <div className={styles.imageContainer}>
              <img src={caseItem.thumbnail} alt={caseItem.clientName} className={styles.image} />
            </div>
            <div className={styles.info}>
              <h3 className={styles.clientName}>{caseItem.clientName}</h3>
              <p className={styles.industry}>{caseItem.industry}</p>
              <p className={styles.scenario}>应用场景: {caseItem.scenario}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CaseShowcasePage;
