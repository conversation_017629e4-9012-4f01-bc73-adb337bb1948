import React from 'react';
import { Helmet } from 'react-helmet-async';

interface TestimonialsProps {
  data?: any;
}

const Testimonials: React.FC<TestimonialsProps> = ({ data }) => {
  return (
    <>
      <Helmet>
        <title>客户评价 | 上海寒链实业有限公司</title>
        <meta name="description" content="查看我们客户的真实评价和反馈，了解我们的服务质量和客户满意度。" />
      </Helmet>

      <div className="page-header bg-light py-5 mt-5">
        <div className="container">
          <div className="row">
            <div className="col-12 text-center">
              <h1 className="display-4 fw-bold">客户评价</h1>
              <p className="lead">听听我们客户的真实声音</p>
            </div>
          </div>
        </div>
      </div>

      <section className="py-5">
        <div className="container">
          <div className="row mb-5">
            <div className="col-lg-8 mx-auto text-center">
              <p className="lead">
                我们致力于为每一位客户提供最优质的服务和产品。以下是一些客户的真实评价，分享他们与我们合作的经验。
              </p>
            </div>
          </div>

          <div className="row g-4">
            {data && data.length > 0 ? (
              data.map((testimonial: any, index: number) => (
                <div key={index} className="col-md-6 col-lg-4">
                  <div className="card h-100 shadow-sm">
                    <div className="card-body">
                      <div className="d-flex align-items-center mb-3">
                        {testimonial.img && (
                          <img 
                            src={testimonial.img} 
                            alt={testimonial.name} 
                            className="rounded-circle me-3"
                            width="60"
                            height="60"
                          />
                        )}
                        <div>
                          <h5 className="mb-0">{testimonial.name}</h5>
                          <p className="text-muted small mb-0">{testimonial.position}</p>
                        </div>
                      </div>
                      <p className="card-text fst-italic">"{testimonial.text}"</p>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-12 text-center">
                <p>暂无客户评价</p>
              </div>
            )}
          </div>
        </div>
      </section>
    </>
  );
};

export default Testimonials;