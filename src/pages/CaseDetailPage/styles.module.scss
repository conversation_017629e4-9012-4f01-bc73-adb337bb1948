.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.title {
  text-align: center;
  margin-bottom: 2rem;
  color: #333;
  font-size: 2rem;
}

.content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

.mainImage {
  width: 100%;
  height: 400px;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.details {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  h2 {
    margin-top: 0;
    color: #333;
    font-size: 1.5rem;
  }

  p {
    margin: 1rem 0;
    color: #555;
    line-height: 1.6;

    strong {
      color: #333;
    }
  }

  .description {
    padding: 1rem;
    background: #f9f9f9;
    border-radius: 4px;
    font-size: 0.95rem;
  }
}

.gallery {
  margin-top: 2rem;

  h3 {
    margin-bottom: 1rem;
    color: #333;
    font-size: 1.3rem;
  }
}

.imageGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.imageItem {
  height: 200px;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.notFound {
  text-align: center;
  padding: 2rem;
  font-size: 1.2rem;
  color: #666;
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .mainImage {
    height: 250px;
  }

  .imageGrid {
    grid-template-columns: 1fr;
  }
}
