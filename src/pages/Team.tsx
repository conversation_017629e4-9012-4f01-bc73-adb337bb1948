import React, { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import <PERSON>sonData from '../data/data.json';

interface TeamMember {
  name: string;
  job: string;
  img: string;
  bio?: string;
  social?: {
    platform: string;
    url: string;
  }[];
}

interface TeamProps {
  data?: TeamMember[];
}

// 团队成员卡片组件
const TeamMemberCard: React.FC<{ member: TeamMember; index: number }> = ({ member, index }) => (
  <div className="col" data-aos="fade-up" data-aos-delay={index * 50}>
    <div className="card h-100 shadow-sm hover-effect">
      <div className="position-relative overflow-hidden">
        <img
          src={member.img}
          className="card-img-top team-img"
          alt={member.name}
          loading="lazy"
          onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
            e.currentTarget.src = '/img/default-avatar.jpg';
          }}
        />
        <div className="overlay">
          <div className="overlay-content">
            <p className="mb-0">{member.bio || "专业领域专家"}</p>
            {member.social && (
              <div className="social-links mt-3">
                {member.social.map((s, idx) => (
                  <a
                    key={idx}
                    href={s.url}
                    className="text-white mx-2"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <i className={`fab fa-${s.platform}`}></i>
                  </a>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
      <div className="card-body text-center">
        <h4 className="h5 mb-2">{member.name}</h4>
        <p className="text-muted mb-0">{member.job}</p>
      </div>
    </div>
  </div>
);

// 加载状态组件
const LoadingState: React.FC = () => (
  <div className="col-12 text-center py-5">
    <div className="spinner-border text-primary" role="status">
      <span className="visually-hidden">加载中...</span>
    </div>
  </div>
);

const Team: React.FC<TeamProps> = ({ data }) => {
  const [teamData, setTeamData] = useState<TeamMember[] | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    // 如果有传入的数据，直接使用
    if (data) {
      setTeamData(data);
      setLoading(false);
      return;
    }

    // 否则从 JsonData 加载
    try {
      setTeamData(JsonData.Team as TeamMember[]);
    } catch (error) {
      console.error("加载团队数据失败:", error);
    } finally {
      setLoading(false);
    }
  }, [data]);

  return (
    <>
      <Helmet>
        <title>我们的团队 | 上海寒链实业有限公司</title>
        <meta name="description" content="了解上海寒链实业有限公司的专业团队成员，我们致力于提供最优质的服务。" />
      </Helmet>

      <div className="page-header bg-light py-5 mt-5">
        <div className="container">
          <div className="row">
            <div className="col-12 text-center">
              <h1 className="display-4 fw-bold">我们的团队</h1>
              <p className="lead">专业团队为您提供卓越服务</p>
            </div>
          </div>
        </div>
      </div>

      <section className="py-5">
        <div className="container">
          <div className="row mb-5">
            <div className="col-lg-8 mx-auto text-center">
              <p className="lead">
                我们的团队由行业专家组成，拥有丰富的冷链物流和食用冰生产经验。每位成员都致力于为客户提供最优质的产品和服务，确保您的每一个需求都能得到满足。
              </p>
            </div>
          </div>

          <div className="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 g-4">
            {loading ? (
              <LoadingState />
            ) : (
              teamData && teamData.map((member, index) => (
                <TeamMemberCard 
                  key={`${member.name}-${index}`}
                  member={member}
                  index={index}
                />
              ))
            )}
          </div>
        </div>
      </section>

      <section className="py-5 bg-light">
        <div className="container">
          <div className="row">
            <div className="col-lg-8 mx-auto text-center">
              <h2 className="h3 mb-4">加入我们的团队</h2>
              <p>我们始终在寻找有才华、有激情的专业人士加入我们的团队。如果您对冷链行业充满热情，并希望在一个充满活力的环境中工作，请查看我们的职位空缺或发送您的简历。</p>
              <a href="/#/contact" className="btn btn-primary mt-3">联系我们</a>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Team;