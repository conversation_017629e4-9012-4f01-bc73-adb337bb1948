import React from 'react';
import { createRoot } from 'react-dom/client';
import { HelmetProvider } from 'react-helmet-async';
import App from './App';
import { startMockService } from './services/api/mockService';
import * as serviceWorker from './serviceWorker';
import { initPerformanceMonitoring } from './utils/performanceMonitor';
import env, { isDev } from './utils/env';
import './index.css';
import './styles/a11y.css'; // 可访问性样式

// 确保在控制台显示当前环境配置
console.log('Environment:', {
  NODE_ENV: env.NODE_ENV,
  API_BASE_URL: env.API_BASE_URL,
  APP_BASE_PATH: env.APP_BASE_PATH,
});

// 初始化性能监控
const performanceMonitor = initPerformanceMonitoring(
  (metrics) => {
    // 在生产环境中可以将指标发送到分析服务
    if (env.NODE_ENV === 'production') {
      console.log('Performance metrics:', metrics);
      // 可以将指标发送到分析服务
      // sendToAnalyticsService(metrics);
    }
  },
  isDev // 在开发环境中启用调试模式
);

// 在开发环境启动模拟服务
if (isDev && (import.meta.env.VITE_USE_MOCK === 'true' || env.USE_MOCK === 'true')) {
  startMockService();
}

const container = document.getElementById('react');
if (!container) throw new Error('容器元素未找到');

const root = createRoot(container);
root.render(
  <React.StrictMode>
    <HelmetProvider>
      <App />
    </HelmetProvider>
  </React.StrictMode>
);

// 如果需要离线支持，可以将 unregister 改为 register
serviceWorker.unregister();