import React from 'react';
import { FeatureCardProps } from './types';

/**
 * 特性卡片组件
 * 显示单个特性项目，包含图标、标题和描述
 * 
 * @param props - 特性卡片组件属性
 * @returns React组件
 */
export const FeatureCard: React.FC<FeatureCardProps> = ({ feature }) => (
  <div className="col">
    <div className="card h-100 shadow-sm p-3">
      <div className="card-body text-center">
        <div className="icon-wrapper mb-3 p-3 bg-primary rounded-circle d-inline-flex">
          <i className={`${feature.icon} fs-3 text-white`} aria-hidden="true"></i>
        </div>
        <h3 className="h4 mb-3">{feature.title}</h3>
        <p className="text-muted mb-0">{feature.text}</p>
      </div>
    </div>
  </div>
);
