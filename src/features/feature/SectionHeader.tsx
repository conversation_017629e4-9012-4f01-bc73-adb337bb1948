import React from 'react';
import { SectionHeaderProps } from './types';

/**
 * 章节标题组件
 * 显示带有标题和描述的章节标题
 * 
 * @param props - 章节标题组件属性
 * @returns React组件
 */
export const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  description,
  headingLevel: HeadingTag = 'h2'
}) => (
  <div className="row justify-content-center mb-5">
    <div className="col-md-10 offset-md-1 text-center">
      <HeadingTag className="display-5 fw-bold mb-4">
        {title}
      </HeadingTag>
      {description && (
        <p className="lead text-muted">{description}</p>
      )}
    </div>
  </div>
);
