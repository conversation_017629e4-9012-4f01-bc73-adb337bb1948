/**
 * 特性组件类型定义
 */

/**
 * 特性项目接口
 */
export interface FeatureItem {
  /** 唯一标识符 */
  id?: string | number;
  /** 标题 */
  title: string;
  /** 描述文本 */
  text: string;
  /** 图标类名 */
  icon: string;
}

/**
 * 特性组件属性接口
 */
export interface FeaturesProps {
  /** 特性数据 */
  data?: {
    /** 标题 */
    title?: string;
    /** 描述文本 */
    description?: string;
    /** 特性项目列表 */
    features?: FeatureItem[];
  } | FeatureItem[];
}

/**
 * 特性卡片属性接口
 */
export interface FeatureCardProps {
  /** 特性项目 */
  feature: FeatureItem;
}

/**
 * 章节标题属性接口
 */
export interface SectionHeaderProps {
  /** 标题 */
  title: string;
  /** 描述文本 */
  description?: string;
  /** 标题级别 */
  headingLevel?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}
