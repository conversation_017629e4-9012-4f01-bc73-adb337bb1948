import React from 'react';
import { useFeatures } from '@/modules/features/hooks';
import { FeatureCard } from './FeatureCard';
import { ErrorAlert } from '@/modules/features/components/common';
import { EnhancedFeature } from '@/types/feature';

// LoadingSpinner 组件的 props 类型
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
}

// 修改后的组件 props 类型
interface FeaturesProps {
    // 根据实际数据结构调整类型定义
    data?: EnhancedFeature[];  // 直接定义为特性数组
}

// 内联定义 LoadingSpinner 组件
const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ size = 'md' }) => {
  const sizeClass = size === 'sm' ? 'spinner-border-sm' :
                   size === 'lg' ? 'spinner-border-lg' : '';

  return (
    <div className={`spinner-border text-primary ${sizeClass}`} role="status">
      <span className="visually-hidden">加载中...</span>
    </div>
  );
};

// 创建类型保护函数
function isError(error: unknown): error is Error {
    return error instanceof Error;
}

export const Features: React.FC<FeaturesProps> = () => {
    const { data, loading, error } = useFeatures();
    // 安全访问数据
// 修正数据访问方式
    const features = data || [];  // 直接使用 data 数组
    return (
        <section id="features" className="py-5 bg-light">
            <div className="container">
                <div className="row justify-content-center mb-5">
                    <div className="col-md-10 offset-md-1 text-center">
                        <h2 className="display-5 fw-bold mb-4">清凉一下，冰力全开</h2>
                        <p className="lead text-muted">匠心打造食品级纯净冰体，-18℃锁鲜冷链直达，7×24小时应急响应。从连锁餐饮到高端酒宴，我们提供吨袋批发、急冻碎冰、定制雕冰等全场景用冰解决方案，确保您每一杯饮品都晶莹剔透，每一场活动都冰爽到底！</p>
                    </div>
                </div>

                {loading ? (
                    <div className="text-center py-5">
                        <LoadingSpinner size="lg" />
                        <p className="mt-3 text-muted">正在加载产品特性...</p>
                    </div>
                ) : error ? (
                    <ErrorAlert
                        message={
                            isError(error)
                                ? error.message
                                : typeof error === "string"
                                    ? error
                                    : "Unknown error"
                        }
                    />              ) : (
                    <div className="row row-cols-1 row-cols-sm-2 row-cols-lg-4 g-4">
                        {features.map((feature) => (
                            <FeatureCard
                                key={feature.id || feature.title}
                                feature={feature}
                            />
                        ))}
                    </div>
                )}
            </div>
        </section>
    );
};
