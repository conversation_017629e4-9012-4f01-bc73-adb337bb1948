/* 联系悬浮栏样式 */
.contact-float-bar {
  position: fixed;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 10px;
}

.contact-float-bar.right {
  right: 30px;
}

.contact-float-bar.left {
  left: 30px;
}

.contact-float-bar-inner {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 当使用百分比定位时，添加垂直居中样式 */
.contact-float-bar[style*="top: 50%"] {
  transform: translateY(-50%);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .contact-float-bar {
    padding: 12px 6px;
  }

  .contact-float-bar.right {
    right: 10px;
  }

  .contact-float-bar.left {
    left: 10px;
  }

  .contact-info {
    width: 180px;
    padding: 12px;
  }
}
