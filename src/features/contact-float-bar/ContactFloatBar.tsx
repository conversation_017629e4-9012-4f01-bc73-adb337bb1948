import React, { useState } from 'react';
import { ContactFloatBarProps } from './types';
import { ContactItem } from './components/ContactItem';
import './ContactFloatBar.css';

/**
 * 联系悬浮栏组件
 * 显示在页面侧边的联系方式悬浮栏，鼠标悬浮时展开详细信息
 *
 * @param props - 联系悬浮栏组件属性
 * @returns React组件
 */
export const ContactFloatBar: React.FC<ContactFloatBarProps> = ({
  items,
  position = 'right',
  top = '50%',
  bottom,
  className = '',
  themeColor = '#1890ff'
}) => {
  // 状态管理
  const [expanded, setExpanded] = useState(false);
  const [activeId, setActiveId] = useState<string | null>(null);

  // 处理鼠标进入事件
  const handleMouseEnter = () => {
    setExpanded(true);
  };

  // 处理鼠标离开事件
  const handleMouseLeave = () => {
    setExpanded(false);
    setActiveId(null);
  };

  // 处理项目点击事件
  const handleItemClick = (id: string) => {
    setActiveId(activeId === id ? null : id);
  };

  // 计算样式
  const barStyle: React.CSSProperties = {
    [position]: 0,
    ...(top !== undefined ? { top } : { bottom }),
  };

  return (
    <div
      className={`contact-float-bar ${position} ${className}`}
      style={barStyle}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="contact-float-bar-inner">
        {items.map((item) => (
          <ContactItem
            key={item.id}
            item={item}
            expanded={expanded}
            onClick={handleItemClick}
            activeId={activeId}
            themeColor={themeColor}
          />
        ))}
      </div>
    </div>
  );
};
