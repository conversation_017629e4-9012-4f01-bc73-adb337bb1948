import React from 'react';
import { But<PERSON>, Tooltip, Image } from 'antd';
import { ContactItemProps } from '../types';
import * as Icons from '@ant-design/icons';
import './ContactItem.css';

/**
 * 动态获取Ant Design图标组件
 */
const getIconComponent = (iconName: string) => {
  const IconComponent = (Icons as any)[iconName];
  return IconComponent ? <IconComponent /> : null;
};

/**
 * 联系项目组件
 * 显示单个联系方式，包含图标和展开信息
 *
 * @param props - 联系项目组件属性
 * @returns React组件
 */
export const ContactItem: React.FC<ContactItemProps> = ({
  item,
  expanded,
  onClick,
  activeId,
  themeColor = '#1890ff'
}) => {
  const isActive = expanded && (activeId === item.id || activeId === null);

  // 处理鼠标悬停事件
  const handleMouseEnter = () => {
    onClick(item.id);
  };

  // 处理点击事件
  const handleClick = () => {
    if (item.onClick) {
      item.onClick();
    }
  };

  return (
    <div
      className={`contact-item ${isActive ? 'active' : ''}`}
      onMouseEnter={handleMouseEnter}
    >
      <Button
        type="primary"
        shape="circle"
        icon={getIconComponent(item.icon)}
        onClick={handleClick}
        className="contact-button"
        style={{
          backgroundColor: isActive ? themeColor : undefined,
          borderColor: isActive ? themeColor : undefined,
          fontSize: '18px',
          width: '48px',
          height: '48px'
        }}
        aria-label={item.title}
      />

      {isActive && (
        <div className="contact-info">
          <div className="contact-info-header">
            <span className="contact-info-title">{item.title}</span>
          </div>

          {item.description && (
            <div className="contact-info-description">
              {item.description}
            </div>
          )}

          {item.qrCode && (
            <div className="contact-info-qrcode">
              <Image
                src={item.qrCode}
                alt={`${item.title} QR Code`}
                preview={false}
                width={120}
              />
              <div className="contact-info-scan-text">扫描二维码添加微信</div>
            </div>
          )}

          {item.link && !item.qrCode && (
            <a
              href={item.link}
              target="_blank"
              rel="noopener noreferrer"
              className="contact-info-link"
            >
              {item.link}
            </a>
          )}
        </div>
      )}
    </div>
  );
};
