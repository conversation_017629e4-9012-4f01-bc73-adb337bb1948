import { useState, useEffect } from 'react';
import { TestimonialsData } from '../types';
import { fetchTestimonials } from '../api/testimonialsService';

export const useTestimonials = () => {
  const [data, setData] = useState<TestimonialsData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const loadTestimonials = async () => {
      try {
        setLoading(true);
        const testimonials = await fetchTestimonials();
        setData(testimonials);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Unknown error occurred'));
        console.error('Failed to load testimonials:', err);
      } finally {
        setLoading(false);
      }
    };

    loadTestimonials();
  }, []);

  return { data, loading, error };
};