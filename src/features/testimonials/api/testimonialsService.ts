import { TestimonialsData } from '../types';

// 默认数据，当API请求失败时使用
const defaultTestimonials: TestimonialsData = {
  description: "我们的客户对我们的服务和产品的评价",
  testimonials: [
    {
      name: "张先生",
      position: "技术总监",
      company: "某科技有限公司",
      text: "产品质量非常好，服务也很到位，我们公司非常满意。",
      img: "/img/testimonials/person1.jpg"
    },
    {
      name: "李女士",
      position: "采购经理",
      company: "某制造企业",
      text: "合作多年，一直保持着良好的合作关系，值得信赖的供应商。",
      img: "/img/testimonials/person2.jpg"
    }
  ]
};

export const fetchTestimonials = async (): Promise<TestimonialsData> => {
  try {
    // 这里应该使用apiClient获取数据
    // const response = await apiClient.get<TestimonialsData>('/api/testimonials');
    // return response;
    
    // 由于没有实际API，返回默认数据
    return Promise.resolve(defaultTestimonials);
  } catch (error) {
    console.error("获取客户评价数据失败:", error);
    return defaultTestimonials;
  }
};