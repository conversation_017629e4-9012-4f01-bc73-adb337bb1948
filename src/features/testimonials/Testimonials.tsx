import React from "react";
import { TestimonialsProps, Testimonial } from "./types";

export const Testimonials: React.FC<TestimonialsProps> = ({ data }) => {
    // Handle both data formats (array or object with testimonials property)
    const testimonials = Array.isArray(data) ? data : data?.testimonials;
    const description = Array.isArray(data) ? undefined : data?.description;

    return (
        <section id="testimonials" className="py-5 bg-light">
            <div className="container">
                {/* 标题区域 */}
                <div className="text-center mb-5">
                    <h2 className="display-5 fw-bold mb-3">客户评价</h2>
                    <p className="lead text-muted">
                        {description || "聆听客户真实声音"}
                    </p>
                </div>

                {/* 评价卡片布局 */}
                <div className="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                    {testimonials ? (
                        testimonials.map((d: Testimonial, i: number) => (
                            <div
                                key={`${d.name}-${i}`}
                                className="col"
                                data-aos="fade-up"
                                data-aos-delay={i * 50}
                            >
                                <div className="card h-100 shadow-sm hover-lift">
                                    <div className="card-body text-center p-4">
                                        {/* 客户头像 */}
                                        <div className="avatar-wrapper mb-4">
                                            <img
                                                src={d.img}
                                                alt={d.name}
                                                className="img-fluid rounded-circle shadow-sm"
                                                width="100"
                                                height="100"
                                                loading="lazy"
                                            />
                                        </div>

                                        {/* 评价内容 */}
                                        <blockquote className="mb-4">
                                            <p className="fst-italic text-dark mb-0">
                                                "{d.text}"
                                            </p>
                                        </blockquote>

                                        {/* 客户信息 */}
                                        <div className="mt-auto">
                                            <h5 className="mb-1">{d.name}</h5>
                                            {d.position && (
                                                <small className="text-muted d-block">{d.position}</small>
                                            )}
                                            {d.company && (
                                                <small className="text-muted">{d.company}</small>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))
                    ) : (
                        // 加载状态
                        <div className="col-12 text-center py-5">
                            <div className="spinner-border text-primary" role="status">
                                <span className="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </section>
    );
};