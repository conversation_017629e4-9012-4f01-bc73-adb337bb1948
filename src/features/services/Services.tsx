import React from "react";
import { ServicesProps, ServiceItem } from './types';

/**
 * 服务组件
 * 显示公司提供的服务列表
 * 
 * @param props - 服务组件属性
 * @returns React组件
 */
export const Services: React.FC<ServicesProps> = ({ data }) => {
  // 处理不同的数据结构
  const description = Array.isArray(data) 
    ? "专业领域解决方案" 
    : data?.description || "专业领域解决方案";
  
  const serviceItems = Array.isArray(data) 
    ? data 
    : data?.services || [];

  return (
    <section id="services" className="py-5 bg-light">
      <div className="container">
        {/* 标题区域 - 现代居中布局 */}
        <div className="text-center mb-5">
          <h2 className="display-5 fw-bold mb-3">我们的服务</h2>
          <p className="lead text-muted mx-auto" style={{ maxWidth: "800px" }}>
            {description}
          </p>
        </div>

        {/* 服务项布局 - 响应式列控制 */}
        <div className="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
          {serviceItems.length > 0 ? (
            serviceItems.map((d: ServiceItem, i: number) => (
              <div
                key={`${d.name}-${i}`}
                className="col"
                data-aos="fade-up"
                data-aos-delay={i * 50}
              >
                <div className="card h-100 shadow-sm p-4 transition-hover">
                  <div className="text-center">
                    {/* 图标容器 */}
                    <div className="icon-wrapper bg-primary bg-gradient rounded-circle d-inline-flex mb-4">
                      <i className={`${d.icon} fs-3 text-white p-3`} aria-hidden="true"></i>
                    </div>
                    <h3 className="h4 mb-3">{d.name}</h3>
                    <p className="text-muted mb-0">{d.text}</p>
                  </div>
                </div>
              </div>
            ))
          ) : (
            // 加载状态
            <div className="col-12 text-center py-5">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">加载中...</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};
