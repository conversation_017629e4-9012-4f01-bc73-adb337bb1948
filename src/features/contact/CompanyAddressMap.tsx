import React from 'react';
import { Card, Row, Col, Typography } from 'antd';
import { EnvironmentOutlined, PhoneOutlined } from '@ant-design/icons';
import './CompanyAddressMap.css';

const { Title, Text } = Typography;

interface CompanyAddressMapProps {
  address: string;
  phone: string;
  mapImageUrl?: string;
}

/**
 * 公司地址地图组件
 * 显示公司地址信息和百度地图
 *
 * @param props - 公司地址地图组件属性
 * @returns React组件
 */
const CompanyAddressMap: React.FC<CompanyAddressMapProps> = ({
  address = '上海市宝山区长江西路2311号1-2层',
  phone = '13761182299',
  mapImageUrl = '/img/map/company-map.jpg' // 默认地图图片
}) => {
  return (
    <div className="company-address-map">
      <Card className="map-card">
        <Row gutter={[24, 24]}>
          <Col xs={24} md={12}>
            <div className="map-info">
              <Title level={3} className="map-title">公司地址</Title>
              <div className="address-item">
                <EnvironmentOutlined className="address-icon" />
                <div className="address-content">
                  <Text strong>公司地址：</Text>
                  <Text>{address}</Text>
                </div>
              </div>
              <div className="address-item">
                <PhoneOutlined className="address-icon" />
                <div className="address-content">
                  <Text strong>联系电话：</Text>
                  <Text>{phone}</Text>
                </div>
              </div>
            </div>
          </Col>
          <Col xs={24} md={12}>
            <div className="map-container">
              {/* 如果有地图图片，则显示图片，否则显示地图卡片 */}
            {mapImageUrl ? (
              <img
                src={mapImageUrl}
                alt="公司地址地图"
                className="map-image"
                onError={(e) => {
                  // 图片加载失败时，显示地图卡片
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.parentElement?.classList.add('map-fallback');
                }}
              />
            ) : (
              <div className="map-fallback">
                <div className="map-fallback-content">
                  <EnvironmentOutlined style={{ fontSize: '32px', color: '#1890ff' }} />
                  <Text strong style={{ marginTop: '10px' }}>{address}</Text>
                </div>
              </div>
            )}
              <div className="map-actions">
                <a
                  href={`https://map.baidu.com/search/${encodeURIComponent(address)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="map-action-btn"
                >
                  在百度地图中查看
                </a>
                <a
                  href={`https://uri.amap.com/marker?position=121.422751,31.405372&name=${encodeURIComponent('上海寒链实业有限公司')}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="map-action-btn"
                >
                  导航到这里
                </a>
              </div>
            </div>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default CompanyAddressMap;
