import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faWeixin, faWeibo, faTwitter, faYoutube } from '@fortawesome/free-brands-svg-icons';
import { SocialLinksProps } from './types';
import './social-links.scss';

/**
 * 图标映射表
 */
const iconMap = {
    wechat: faWeixin,
    weibo: faWeibo,
    twitter: faTwitter,
    youtube: faYoutube
};

/**
 * 社交链接组件
 * 显示社交媒体图标和链接
 *
 * @param props - 社交链接组件属性
 * @returns React组件
 */
export const SocialLinks: React.FC<SocialLinksProps> = ({
    platforms = ['wechat', 'weibo', 'twitter', 'youtube'],
    links,
    className
}) => (
    <div className={`social-links ${className || ''}`}>
        <ul className="list-inline d-flex justify-content-center gap-4">
            {platforms.map((platform) => (
                <li className="list-inline-item" key={platform}>
                    <a
                        href={links?.[platform] || "#"}
                        className="text-decoration-none"
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label={`访问我们的${platform}主页`}
                    >
                        <FontAwesomeIcon
                            icon={iconMap[platform as keyof typeof iconMap]}
                            className="fs-3 text-secondary"
                        />
                    </a>
                </li>
            ))}
        </ul>
    </div>
);
