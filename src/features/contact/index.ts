/**
 * 联系组件模块
 * 导出所有联系相关组件和类型
 */

// 导出组件
export { Contact } from './Contact';
export { ContactUs } from './ContactUs';
export { ContactForm } from './ContactForm';
export { ContactInfoCard } from './ContactInfoCard';
export { SocialLinks } from './SocialLinks';
export { PageFooter } from './PageFooter';
export { FormField } from './FormField';

// 导出钩子
export { useContactForm } from './hooks/useContactForm';

// 导出类型
export type {
  ContactProps,
  ContactUsProps,
  ContactFormProps,
  ContactInfoCardProps,
  SocialLinksProps,
  PageFooterProps,
  FormFieldProps,
  FormState,
  FormErrors,
  ContactItemProps,
  QRCodeItem
} from './types';
