/* 自定义卡片样式覆盖 bootstrap 默认样式 */
.contact-info-custom {
    background-color: transparent !important;
    border: none !important;
}

/* 可选：如果保留 shadow-sm 需要添加的基础样式 */
.contact-info-custom {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
}

/* 确保图标和文本垂直对齐 */
.contact-item .d-flex {
    align-items: center !important;
}

.contact-item {
    margin-bottom: 10px;
}

.contact-item span {
    line-height: 1.5;
    display: inline-block;
    vertical-align: middle;
}

/* 调整图标和文本的间距 */
.contact-item .d-flex {
    gap: 0 !important;
}
