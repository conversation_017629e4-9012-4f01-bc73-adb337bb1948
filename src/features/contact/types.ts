/**
 * 联系我们组件类型定义
 */

/**
 * 联系信息卡片项目接口
 */
export interface ContactItemProps {
  /** 图标名称 */
  icon: string;
  /** 标签文本 */
  label: string;
  /** 值 */
  value?: string;
}

/**
 * 联系信息卡片属性接口
 */
export interface ContactInfoCardProps {
  /** 标题 */
  title?: string;
  /** 地址 */
  address?: string;
  /** 电话 */
  phone?: string;
  /** 邮箱 */
  email?: string;
  /** 联系人 */
  contactPerson?: string;
}

/**
 * 社交链接属性接口
 */
export interface SocialLinksProps {
  /** 平台列表 */
  platforms?: Array<'wechat' | 'weibo' | 'twitter' | 'youtube'>;
  /** 链接映射 */
  links?: Record<string, string>;
  /** 自定义类名 */
  className?: string;
}

/**
 * 页脚属性接口
 */
export interface PageFooterProps {
  /** 备案号列表 */
  recordNumbers?: string[];
}

/**
 * 表单字段属性接口
 */
export interface FormFieldProps {
  /** 输入类型 */
  type: 'text' | 'email';
  /** 字段ID */
  id: string;
  /** 标签文本 */
  label: string;
  /** 字段值 */
  value: string;
  /** 错误信息 */
  error?: string;
  /** 变更处理函数 */
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  /** 失焦处理函数 */
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * 表单状态接口
 */
export interface FormState {
  /** 姓名 */
  name: string;
  /** 邮箱 */
  email: string;
  /** 留言内容 */
  message: string;
}

/**
 * 表单错误接口
 */
export interface FormErrors {
  /** 姓名错误 */
  name?: string;
  /** 邮箱错误 */
  email?: string;
  /** 留言内容错误 */
  message?: string;
}

/**
 * 联系表单属性接口
 */
export interface ContactFormProps {
  /** 表单状态 */
  formState: FormState;
  /** 表单错误 */
  errors: FormErrors;
  /** 是否提交中 */
  submitting: boolean;
  /** 提交状态 */
  submitStatus: 'success' | 'error' | null;
  /** 变更处理函数 */
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  /** 失焦处理函数 */
  onBlur: (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  /** 提交处理函数 */
  onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  /** 自定义样式 */
  style?: React.CSSProperties;
}

/**
 * 二维码项目接口
 */
export interface QRCodeItem {
  /** 图片URL */
  image: string;
  /** 标题 */
  title: string;
  /** 描述 */
  description: string;
}

/**
 * 联系我们页面组件属性接口
 */
export interface ContactUsProps {
  /** 地址 */
  address?: string;
  /** 电话 */
  phone?: string;
  /** 邮箱 */
  email?: string;
  /** 联系人 */
  contactPerson?: string;
  /** 二维码列表 */
  qrCodes?: QRCodeItem[];
  /** 社交链接 */
  social?: Record<string, string>;
}

/**
 * 联系组件属性接口
 */
export interface ContactProps {
  /** 联系数据 */
  data?: {
    /** 地址 */
    address?: string;
    /** 电话 */
    phone?: string;
    /** 邮箱 */
    email?: string;
    /** 社交链接 */
    social?: Record<string, string>;
  };
}
