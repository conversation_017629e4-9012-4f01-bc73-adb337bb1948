.company-address-map {
  margin: 2rem 0;
}

.map-card {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.map-info {
  padding: 1rem;
}

.map-title {
  margin-bottom: 1.5rem !important;
  position: relative;
}

.map-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: #1890ff;
}

.address-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.address-icon {
  font-size: 18px;
  color: #1890ff;
  margin-right: 10px;
  margin-top: 4px;
}

.address-content {
  display: flex;
  flex-direction: column;
}

.map-container {
  position: relative;
  height: 100%;
  min-height: 250px;
  overflow: hidden;
}

.map-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.map-actions {
  position: absolute;
  bottom: 10px;
  left: 10px;
  display: flex;
  gap: 10px;
}

.map-action-btn {
  background-color: rgba(255, 255, 255, 0.9);
  color: #1890ff;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  text-decoration: none;
  transition: all 0.3s;
}

.map-action-btn:hover {
  background-color: #1890ff;
  color: white;
}

/* 地图回退样式 */
.map-fallback {
  width: 100%;
  height: 100%;
  background-color: #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  border: 1px dashed #d9d9d9;
}

.map-fallback-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
}

@media (max-width: 768px) {
  .map-container {
    min-height: 200px;
  }

  .map-actions {
    flex-direction: column;
    gap: 5px;
  }
}
