import React from 'react';
import { useNavigate } from 'react-router-dom';
import { NavBrandProps } from './types';

/**
 * 导航品牌组件
 * 显示网站品牌名称，点击后导航到首页
 *
 * @param props - 导航品牌组件属性
 * @returns React组件
 */
export const NavBrand: React.FC<NavBrandProps> = ({ brandName, href = '/' }) => {
    // 声明 navigate 变量类型为函数或 undefined
    let navigate: ((path: string) => void) | undefined;

    try {
        navigate = useNavigate();
    } catch (error) {
        console.warn('Router context not available in NavBrand');
    }

    const handleClick = (e: React.MouseEvent<HTMLAnchorElement>): void => {
        e.preventDefault();
        if (typeof navigate === 'function') {
            navigate('/');
        } else {
            window.location.href = '/';
        }
    };

    return (
        <a
            className="navbar-brand"
            href={href}
            onClick={handleClick}
        >
            {brandName}
        </a>
    );
};
