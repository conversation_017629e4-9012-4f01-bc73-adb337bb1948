import React from 'react';
import { Row, Col, Card, Skeleton, Alert } from 'antd';
import { useQuery } from '@tanstack/react-query';

interface ProductItem {
  id: string;
  title: string;
  image: string;
  description?: string;
}

const productService = {
  getProducts: async (): Promise<ProductItem[]> => {
    // Mock API call
    return PRODUCT_ITEMS;
  }
};

const PRODUCT_ITEMS: ProductItem[] = [
  {
    id: '1',
    title: '降温冰块配送',
    image: '/images/products/ice-block-1.jpg',
    description: '专业的降温冰块配送服务，保证新鲜和卫生'
  },
  {
    id: '2',
    title: '工业冰块配送',
    image: '/images/products/ice-block-2.jpg',
    description: '工业级冰块配送，满足各类工业降温需求'
  },
  {
    id: '3',
    title: '降温用冰配送',
    image: '/images/products/ice-block-3.jpg',
    description: '专业的降温用冰配送服务'
  },
  {
    id: '4',
    title: '工业干冰配送',
    image: '/images/products/ice-block-4.jpg',
    description: '工业干冰配送服务'
  },
  {
    id: '5',
    title: '食用冰块',
    image: '/images/products/ice-cube-1.jpg',
    description: '食品级冰块，适用于餐饮行业'
  },
  {
    id: '6',
    title: '食用冰',
    image: '/images/products/ice-cube-2.jpg',
    description: '优质食用冰供应'
  },
  {
    id: '7',
    title: '冰桶租赁',
    image: '/images/products/ice-container.jpg',
    description: '专业冰桶租赁服务'
  },
  {
    id: '8',
    title: '车间降温冰水配送',
    image: '/images/products/ice-delivery.jpg',
    description: '工业车间降温冰水配送服务'
  }
];

const ProductsPage: React.FC = () => {
  const { data: products, isLoading, error } = useQuery<ProductItem[]>(
    'products',
    productService.getProducts
  );

  if (isLoading) {
    return (
      <div className="products-page" style={{ padding: '40px 20px', marginTop: '80px' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          <h1 style={{ textAlign: 'center', marginBottom: '40px' }}>产品信息中心</h1>
          <Row gutter={[16, 16]}>
            {[...Array(8)].map((_, i) => (
              <Col key={i} xs={12} sm={6} md={8} lg={6}>
                <Card style={{ height: '100%' }}>
                  <Skeleton.Image
                    style={{ width: '100%', height: '200px' }}
                    active
                  />
                  <div style={{ padding: '16px' }}>
                    <Skeleton
                      paragraph={{ rows: 2 }}
                      active
                    />
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="products-page" style={{ padding: '40px 20px', marginTop: '80px' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          <Alert
            type="error"
            message="加载产品数据失败，请稍后重试"
            showIcon
          />
        </div>
      </div>
    );
  }

  return (
    <div className="products-page" style={{ padding: '40px 20px', marginTop: '80px' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <h1 style={{ textAlign: 'center', marginBottom: '40px' }}>产品信息中心</h1>
        <Row gutter={[16, 16]}>
          {products?.map((item: ProductItem) => (
            <Col key={item.id} xs={12} sm={6} md={8} lg={6}>
              <Card
                style={{ height: '100%' }}
                hoverable
                cover={
                  <div style={{ height: '200px', overflow: 'hidden' }}>
                    <img
                      alt={item.title}
                      src={item.image}
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover'
                      }}
                    />
                  </div>
                }
              >
                <Card.Meta
                  title={<div style={{ textAlign: 'center' }}>{item.title}</div>}
                  description={
                    item.description && (
                      <div style={{ textAlign: 'center', color: '#666' }}>
                        {item.description}
                      </div>
                    )
                  }
                />
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    </div>
  );
};

export default ProductsPage;