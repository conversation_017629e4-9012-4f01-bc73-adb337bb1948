import React from 'react';
import { Container, Row, Col, Card } from 'react-bootstrap';

interface ProductItem {
  id: string;
  title: string;
  image: string;
  description?: string;
}

const PRODUCT_ITEMS: ProductItem[] = [
  {
    id: '1',
    title: '降温冰块配送',
    image: '/images/products/ice-block-1.jpg',
    description: '专业的降温冰块配送服务，保证新鲜和卫生'
  },
  {
    id: '2',
    title: '工业冰块配送',
    image: '/images/products/ice-block-2.jpg',
    description: '工业级冰块配送，满足各类工业降温需求'
  },
  {
    id: '3',
    title: '降温用冰配送',
    image: '/images/products/ice-block-3.jpg',
    description: '专业的降温用冰配送服务'
  },
  {
    id: '4',
    title: '工业干冰配送',
    image: '/images/products/ice-block-4.jpg',
    description: '工业干冰配送服务'
  },
  {
    id: '5',
    title: '食用冰块',
    image: '/images/products/ice-cube-1.jpg',
    description: '食品级冰块，适用于餐饮行业'
  },
  {
    id: '6',
    title: '食用冰',
    image: '/images/products/ice-cube-2.jpg',
    description: '优质食用冰供应'
  },
  {
    id: '7',
    title: '冰桶租赁',
    image: '/images/products/ice-container.jpg',
    description: '专业冰桶租赁服务'
  },
  {
    id: '8',
    title: '车间降温冰水配送',
    image: '/images/products/ice-delivery.jpg',
    description: '工业车间降温冰水配送服务'
  }
];

const ProductsPage: React.FC = () => {
  const { data: products, isLoading, error } = useQuery<ProductItem[]>(
    'products',
    productService.getProducts
  );

  if (isLoading) {
    return (
      <div className="products-page py-5 mt-5">
        <Container>
          <h1 className="text-center mb-5">产品信息中心</h1>
          <Row className="g-4">
            {[...Array(8)].map((_, i) => (
              <Col key={i} xs={12} sm={6} md={4} lg={3}>
                <Card className="h-100 product-card">
                  <Skeleton.Image 
                    style={{ width: '100%', height: '200px' }} 
                    active 
                  />
                  <Card.Body>
                    <Skeleton 
                      paragraph={{ rows: 2 }} 
                      active 
                    />
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        </Container>
      </div>
    );
  }

  if (error) {
    return (
      <div className="products-page py-5 mt-5">
        <Container>
          <Alert variant="danger">
            加载产品数据失败，请稍后重试
          </Alert>
        </Container>
      </div>
    );
  }

  return (
    <div className="products-page py-5 mt-5">
      <Container>
        <h1 className="text-center mb-5">产品信息中心</h1>
        <Row className="g-4">
          {products?.map((item) => (
            <Col key={item.id} xs={12} sm={6} md={4} lg={3}>
              <Card className="h-100 product-card hover-shadow">
                <div className="card-img-wrapper" style={{ height: '200px', overflow: 'hidden' }}>
                  <Card.Img
                    variant="top"
                    src={item.image}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                  />
                </div>
                <Card.Body>
                  <Card.Title className="text-center">{item.title}</Card.Title>
                  {item.description && (
                    <Card.Text className="text-center text-muted">
                      {item.description}
                    </Card.Text>
                  )}
                </Card.Body>
              </Card>
            </Col>
          ))}
        </Row>
      </Container>
    </div>
  );
};

export default ProductsPage; 