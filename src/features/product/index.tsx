import * as React from 'react';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { SEO, CompanySchema, BreadcrumbSchema } from '../../components/seo';
import { Layout, Typography, Tabs, Divider, Badge, Spin } from 'antd';
import { AppstoreOutlined, ToolOutlined, CoffeeOutlined, CustomerServiceOutlined } from '@ant-design/icons';
import ProductList from './components/ProductList';
import { productCategories, ProductDataSource } from './data/productCategories';
import { filterProductsByCategory, logProductDataSource } from './utils/productUtils';
import useProductsInfo from '../../hooks/useProductsInfo';
import { getHomeUrl, getProductListUrl } from '@/utils/urlUtils';
import './product.css';

const { Content } = Layout;
const { Title, Paragraph } = Typography;

/**
 * 产品页面组件
 * 显示产品信息，支持按分类查看不同产品
 *
 * @returns React组件
 */
const ProductPage: React.FC = () => {
  // 当前选中的分类ID
  const [activeCategory, setActiveCategory] = useState('all');

  // 使用 Hook 从 API 获取产品数据
  const { data: products, isLoading, error } = useProductsInfo();

  // 获取指定分类的产品列表
  const getProductsByCategory = (categoryId: string) => {
    return products ? filterProductsByCategory(products, categoryId) : [];
  };

  // 当前选中分类的产品列表
  const currentProducts = getProductsByCategory(activeCategory);

  // 当前选中的分类信息
  const currentCategory = productCategories.find(category => category.id === activeCategory);

  // 获取指定分类的信息
  const getCategoryInfo = (categoryId: string) => {
    return productCategories.find(category => category.id === categoryId);
  };

  // 记录数据来源和当前分类信息
  React.useEffect(() => {
    if (products) {
      // 判断数据来源，如果是从 API 获取的，则记录为 API 数据源
      const dataSource = error ? ProductDataSource.STATIC : ProductDataSource.API;
      logProductDataSource(dataSource, products);

      // 在控制台输出当前选中的分类信息和产品数量，便于调试
      if (currentCategory) {
        console.log(`当前选中分类: ${currentCategory.id} - ${currentCategory.name}`);
        console.log(`当前分类产品数量: ${currentProducts.length}`);
      }
    }
  }, [products, error, currentCategory, activeCategory, currentProducts]);

  /**
   * 处理标签页切换事件
   *
   * @param key 选中的标签页key
   */
  const handleTabChange = (key: string) => {
    setActiveCategory(key);

    // 在切换标签页时输出产品数量，便于调试
    const categoryProducts = getProductsByCategory(key);
    console.log(`切换到分类: ${key}, 产品数量: ${categoryProducts.length}`);

    // 在切换标签页后，currentProducts 会自动更新，因为 activeCategory 变化了
  };

  return (
    <Layout className="product-page">
      {/* 使用 Helmet 设置页面元数据 */}
      <Helmet>
        <title>产品信息中心 | 上海寒链实业有限公司</title>
        <meta name="description" content="上海寒链实业有限公司提供高品质工业冰和食用冰产品，满足各种工业和商业需求。" />
        <meta name="keywords" content="工业冰产品,食用冰产品,冷链服务,上海寒链" />
        <link rel="canonical" href={getProductListUrl()} />
      </Helmet>

      {/* 使用 SEO 组件设置更详细的 SEO 信息 */}
      <SEO
        title="产品信息中心"
        description="上海寒链实业有限公司提供高品质工业冰和食用冰产品，满足各种工业和商业需求。我们的产品包括工业冰、食用冰和定制冷链服务。"
        keywords="工业冰产品,食用冰产品,冷链服务,上海寒链"
        ogType="website"
        ogImage="/img/products-og-image.jpg"
        ogImageAlt="上海寒链实业有限公司产品信息"
        canonicalUrl={getProductListUrl()}
      />

      {/* 添加公司结构化数据 */}
      <CompanySchema />

      {/* 添加面包屑导航结构化数据 */}
      <BreadcrumbSchema
        items={[
          { name: '首页', url: getHomeUrl() },
          { name: '产品信息中心', url: getProductListUrl() }
        ]}
      />

      <Content className="product-content product-content-no-hero">
        <div className="container">
          <div className="title-container">
            <Title level={1} className="text-center mb-3">产品信息中心</Title>
            <div className="title-decoration">
              <span></span>
            </div>
          </div>

          {/* 产品分类标签页 - 重新设计 */}
          {isLoading ? (
            <div className="loading-container">
              <Spin size="large" tip="正在加载产品数据..." />
            </div>
          ) : (
            <Tabs
              activeKey={activeCategory}
              onChange={handleTabChange}
              centered
              size="large"
              type="card"
              className="product-tabs custom-tabs"
              tabBarGutter={8}
              items={[
                {
                  key: 'all',
                  label: (
                    <span className="tab-label">
                      <AppstoreOutlined />
                      <span className="tab-text">全部产品</span>
                      <Badge count={getProductsByCategory('all').length} className="product-count" />
                    </span>
                  ),
                  children: (
                    <div className="product-category-content">
                      {/* 使用获取分类信息函数显示描述 */}
                      {getCategoryInfo('all')?.description && (
                        <Paragraph className="category-description">
                          {getCategoryInfo('all')?.description}
                        </Paragraph>
                      )}
                      <Divider />
                      <ProductList products={getProductsByCategory('all')} />
                    </div>
                  )
                },
                {
                  key: 'industrial',
                  label: (
                    <span className="tab-label">
                      <ToolOutlined />
                      <span className="tab-text">工业冰产品</span>
                      <Badge count={getProductsByCategory('industrial').length} className="product-count" />
                    </span>
                  ),
                  children: (
                    <div className="product-category-content">
                      {/* 使用获取分类信息函数显示描述 */}
                      {getCategoryInfo('industrial')?.description && (
                        <Paragraph className="category-description">
                          {getCategoryInfo('industrial')?.description}
                        </Paragraph>
                      )}
                      <Divider />
                      <ProductList products={getProductsByCategory('industrial')} />
                    </div>
                  )
                },
                {
                  key: 'food',
                  label: (
                    <span className="tab-label">
                      <CoffeeOutlined />
                      <span className="tab-text">食用冰产品</span>
                      <Badge count={getProductsByCategory('food').length} className="product-count" />
                    </span>
                  ),
                  children: (
                    <div className="product-category-content">
                      {/* 使用获取分类信息函数显示描述 */}
                      {getCategoryInfo('food')?.description && (
                        <Paragraph className="category-description">
                          {getCategoryInfo('food')?.description}
                        </Paragraph>
                      )}
                      <Divider />
                      <ProductList products={getProductsByCategory('food')} />
                    </div>
                  )
                },
                {
                  key: 'service',
                  label: (
                    <span className="tab-label">
                      <CustomerServiceOutlined />
                      <span className="tab-text">冰产品服务</span>
                      <Badge count={getProductsByCategory('service').length} className="product-count" />
                    </span>
                  ),
                  children: (
                    <div className="product-category-content">
                      {/* 使用获取分类信息函数显示描述 */}
                      {getCategoryInfo('service')?.description && (
                        <Paragraph className="category-description">
                          {getCategoryInfo('service')?.description}
                        </Paragraph>
                      )}
                      <Divider />
                      <ProductList products={getProductsByCategory('service')} />
                    </div>
                  )
                }
              ]}
            />
          )}
        </div>
      </Content>
    </Layout>
  );
};

export default ProductPage;
