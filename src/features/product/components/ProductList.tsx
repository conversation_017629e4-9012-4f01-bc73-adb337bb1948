import React from 'react';
import { Row, Col, Empty } from 'antd';
import ProductCard from './ProductCard';
import { Product } from '../data/productData';
import './ProductList.css';

interface ProductListProps {
  products: Product[];
}

const ProductList: React.FC<ProductListProps> = ({ products }) => {
  if (!products || products.length === 0) {
    return (
      <div className="empty-products">
        <Empty
          description="暂无相关产品"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    );
  }

  return (
    <Row gutter={[24, 32]} className="product-list">
      {products.map(product => (
        <Col xs={24} md={12} lg={8} key={product.id}>
          <ProductCard product={product} />
        </Col>
      ))}
    </Row>
  );
};

export default ProductList;