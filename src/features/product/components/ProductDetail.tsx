import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { SEO, ProductSchema, BreadcrumbSchema } from '../../../components/seo';
import {
  Row,
  Col,
  Typography,
  Carousel,
  Tabs,
  List,
  Descriptions,
  Spin,
  Alert,
  Button
} from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useProduct } from '@/hooks/useProducts';
import { getHomeUrl, getProductListUrl, getProductDetailUrl } from '@/utils/urlUtils';
import './ProductDetail.css';

const { Title, Paragraph } = Typography;

const ProductDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const productId = id ? parseInt(id, 10) : 0;

  const { data: product, isLoading, error } = useProduct(productId);

  const handleBack = () => {
    navigate('/product');
  };

  if (isLoading) {
    return (
      <div className="product-detail-loading">
        <Spin size="large" tip="加载产品详情..." />
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="product-detail-error">
        <Alert
          message="加载失败"
          description="无法加载产品详情，请稍后重试。"
          type="error"
          showIcon
        />
        <Button
          type="primary"
          icon={<ArrowLeftOutlined />}
          onClick={handleBack}
          style={{ marginTop: 16 }}
        >
          返回产品列表
        </Button>
      </div>
    );
  }

  // 产品图片URL
  const productImageUrl = product.images && product.images.length > 0 ? product.images[0].src : '';

  // 确定产品类别
  let categoryName = '产品';

  // 尝试从产品标题或应用场景推断类别
  if (product.title.includes('工业') || product.applications.some(app => app.includes('工业'))) {
    categoryName = '工业冰产品';
  } else if (product.title.includes('食用') || product.applications.some(app => app.includes('餐厅') || app.includes('饮品'))) {
    categoryName = '食用冰产品';
  } else if (product.title.includes('服务') || product.title.includes('配送') || product.title.includes('冰雕')) {
    categoryName = '冰产品服务';
  }

  return (
    <div className="product-detail">
      {/* 使用 Helmet 设置页面元数据 */}
      <Helmet>
        <title>{product.title} | 上海寒链实业有限公司</title>
        <meta name="description" content={product.description} />
        <meta name="keywords" content={`${product.title},${categoryName},上海寒链,冷链产品`} />
        <link rel="canonical" href={getProductDetailUrl(product.id)} />
      </Helmet>

      {/* 使用 SEO 组件设置更详细的 SEO 信息 */}
      <SEO
        title={product.title}
        description={product.description}
        keywords={`${product.title},${categoryName},上海寒链,冷链产品`}
        ogType="product"
        ogImage={productImageUrl}
        ogImageAlt={product.title}
        canonicalUrl={getProductDetailUrl(product.id)}
      />

      {/* 添加产品结构化数据 */}
      <ProductSchema
        name={product.title}
        description={product.description}
        image={productImageUrl}
        sku={`PROD-${product.id}`}
        category={categoryName}
      />

      {/* 添加面包屑导航结构化数据 */}
      <BreadcrumbSchema
        items={[
          { name: '首页', url: getHomeUrl() },
          { name: '产品信息中心', url: getProductListUrl() },
          { name: product.title, url: getProductDetailUrl(product.id) }
        ]}
      />
      <Button
        type="primary"
        icon={<ArrowLeftOutlined />}
        onClick={handleBack}
        className="back-button"
      >
        返回产品列表
      </Button>

      <Row gutter={[32, 32]}>
        <Col xs={24} md={12}>
          <Carousel autoplay className="product-carousel">
            {product.images.map(image => (
              <div key={image.id}>
                <img src={image.src} alt={image.alt} className="product-image" />
              </div>
            ))}
          </Carousel>
        </Col>

        <Col xs={24} md={12}>
          <Title level={2}>{product.title}</Title>
          <Paragraph>{product.description}</Paragraph>

          <Tabs
            defaultActiveKey="1"
            items={[
              {
                key: '1',
                label: '产品特点',
                children: (
                  <List
                    dataSource={product.features}
                    renderItem={item => (
                      <List.Item>
                        <Paragraph>{item}</Paragraph>
                      </List.Item>
                    )}
                  />
                )
              },
              {
                key: '2',
                label: '应用场景',
                children: (
                  <List
                    dataSource={product.applications}
                    renderItem={item => (
                      <List.Item>
                        <Paragraph>{item}</Paragraph>
                      </List.Item>
                    )}
                  />
                )
              },
              {
                key: '3',
                label: '规格参数',
                children: product.specifications ? (
                  <Descriptions bordered column={1}>
                    {Object.entries(product.specifications).map(([key, value]) => (
                      <Descriptions.Item key={key} label={key}>{value}</Descriptions.Item>
                    ))}
                  </Descriptions>
                ) : (
                  <Paragraph>暂无规格参数信息</Paragraph>
                )
              }
            ]}
          />
        </Col>
      </Row>
    </div>
  );
};

export default ProductDetail;
