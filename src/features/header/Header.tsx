import React from "react";
import { Helmet } from "react-helmet-async";
import { AntCarousel, carouselItems } from '../../components/display/Carousel';
import { HeaderProps } from './types';
import "./Header.css";

/**
 * 头部组件
 * 显示网站头部轮播图和SEO信息
 * 
 * @param props - 头部组件属性
 * @returns React组件
 */
export const Header: React.FC<HeaderProps> = ({ data }) => {
  const metaDescription = data?.paragraph || "上海寒链实业有限公司专注工业制造与创新科技";
  const pageTitle = data?.title || "上海寒链实业有限公司";

  return (
    <header id="header" role="banner">
      {/* SEO 优化 */}
      <Helmet prioritizeSeoTags>
        <html lang="zh-Hans" />
        <title>{pageTitle}</title>
        <meta name="description" content={metaDescription} />

        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:url" content={window.location.href} />
        <meta property="og:title" content={pageTitle} />
        <meta property="og:description" content={metaDescription} />
        <meta property="og:image" content="/img/og-image.jpg" />

        {/* Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={pageTitle} />
        <meta name="twitter:description" content={metaDescription} />
        <meta name="twitter:image" content="/img/twitter-image.jpg" />

        {/* 预加载关键资源 */}
        <link rel="preload" href="/img/background1.webp" as="image" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
      </Helmet>
      <AntCarousel items={carouselItems} />
    </header>
  );
};
