/* Header.css */
.hero-section {
    min-height: 100vh;
    padding-top: 80px; /* 考虑导航栏高度 */
}

.img-cover {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.highlight-bar::after {
    content: '';
    display: block;
    width: 120px;
    height: 4px;
    background: #4b6cb7;
    margin: 1.5rem auto;
    border-radius: 2px;
}

.shadow-hover {
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(75,108,183,0.3);
}

.shadow-hover:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(75,108,183,0.5);
}

.bi-arrow-down-circle {
    transition: transform 0.3s ease;
}

.shadow-hover:hover .bi-arrow-down-circle {
    transform: translateY(3px);
}

@media (max-width: 768px) {
    .hero-section {
        min-height: 80vh;
    }

    .display-3 {
        font-size: 2.5rem;
    }
}