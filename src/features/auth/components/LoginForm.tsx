import React, { useState } from 'react';
import { Form, Input, Button, message, Checkbox } from 'antd';
import { userStore } from '@/store/userStore';
import type { SignInRequest } from '@/types/user';

const LoginForm: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const setUserInfo = userStore(state => state.actions.setUserInfo);

  const handleSubmit = async (values: SignInRequest) => {
    setLoading(true);
    try {
      // TODO: 实现用户登录服务
      const response = { token: 'mock-token', user: { id: '1', username: values.username } };

      // 更新用户状态
      setUserInfo({
        token: response.token,
        user: response.user
      });

      message.success('登录成功');
    } catch (error) {
      // 错误已在 API 客户端中处理
      console.error('登录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form
      name="login"
      onFinish={handleSubmit}
      layout="vertical"
    >
      <Form.Item
        label="用户名"
        name="username"
        rules={[{ required: true, message: '请输入用户名' }]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        label="密码"
        name="password"
        rules={[
          { required: true, message: '请输入密码' },
          { min: 6, message: '密码长度至少6位' }
        ]}
      >
        <Input.Password />
      </Form.Item>

      <Form.Item name="remember" valuePropName="checked">
        <Checkbox>记住我</Checkbox>
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          disabled={loading}
          block
        >
          登录
        </Button>
      </Form.Item>
    </Form>
  );
};

export default LoginForm;