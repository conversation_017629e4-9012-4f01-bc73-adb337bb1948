import React from 'react';
import { TeamMember } from '../types';

interface TeamMemberCardProps {
  member: TeamMember;
  index: number;
}

export const TeamMemberCard: React.FC<TeamMemberCardProps> = ({ member, index }) => (
  <div
    className="col"
    data-aos="fade-up"
    data-aos-delay={index * 50}
  >
    <div className="card h-100 shadow-sm transition-hover">
      <div className="position-relative overflow-hidden">
        <img
          src={member.img}
          className="card-img-top team-img"
          alt={member.name}
          loading="lazy"
        />
        {/* 悬停覆盖层 */}
        <div className="hover-overlay w-100 h-100 position-absolute top-0 start-0 bg-dark bg-opacity-0 transition-opacity d-flex align-items-center justify-content-center">
          <div className="text-center text-white opacity-0 transition-all px-3">
            <p className="mb-0">{member.bio || "专业领域专家"}</p>
          </div>
        </div>
      </div>
      <div className="card-body text-center">
        <h5 className="card-title mb-1">{member.name}</h5>
        <p className="card-text text-muted small">{member.job}</p>
        {/* 社交媒体链接 */}
        {member.social && (
          <div className="social-icons mt-3">
            {member.social.map((social, idx) => (
              <a
                key={idx}
                href={social.url}
                className="me-2 text-decoration-none"
                target="_blank"
                rel="noopener noreferrer"
              >
                <i className={`fab fa-${social.platform.toLowerCase()}`}></i>
              </a>
            ))}
          </div>
        )}
      </div>
    </div>
  </div>
);