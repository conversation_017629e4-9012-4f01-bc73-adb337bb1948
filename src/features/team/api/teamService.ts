import { TeamData } from '../types';

// 默认数据，当API请求失败时使用
const defaultTeamData: TeamData = {
  description: "专业团队为您提供卓越服务",
  members: [
    {
      name: "张三",
      job: "首席执行官",
      img: "/img/team/1.jpg",
      bio: "拥有10年行业经验，专注于企业战略规划与管理",
      social: [
        { platform: "linkedin", url: "https://linkedin.com" },
        { platform: "twitter", url: "https://twitter.com" }
      ]
    },
    {
      name: "李四",
      job: "技术总监",
      img: "/img/team/2.jpg",
      bio: "技术专家，负责产品研发与技术创新"
    }
  ]
};

export const fetchTeamData = async (): Promise<TeamData> => {
  try {
    // 这里应该使用apiClient获取数据
    // const response = await apiClient.get<TeamData>('/api/team');
    // return response;
    
    // 由于没有实际API，返回默认数据
    return Promise.resolve(defaultTeamData);
  } catch (error) {
    console.error("获取团队数据失败:", error);
    return defaultTeamData;
  }
};