import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Alert, Button, Space } from 'antd';
import './styles/index.css';

// 注释: 定义自定义Hook的返回类型
interface CookieConsentHook {
  checkConsent: () => boolean;
  setConsent: (value: string, uuid?: string | null) => void;
}

// 注释: 使用类型定义增强代码可读性和安全性
// 自定义 Hook：处理本地存储
const useCookieConsent = (): CookieConsentHook => {
  const checkConsent = useCallback((): boolean => {
    try {
      return !!window.localStorage?.getItem('CookieConsent');
    } catch (error) {
      console.error('访问 localStorage 失败:', error);
      return false;
    }
  }, []);

  const setConsent = useCallback((value: string, uuid: string | null = null): void => {
    try {
      window.localStorage?.setItem('CookieConsent', value);
      if (uuid) {
        window.localStorage?.setItem('uuid', uuid);
      }
    } catch (error) {
      console.error('设置 localStorage 失败:', error);
    }
  }, []);

  return { checkConsent, setConsent };
};

// 注释: 使用React.FC类型定义组件
export const CookieConsent: React.FC = () => {
  // Refs
  // 注释: 使用泛型指定ref的类型
  const containerRef = useRef<HTMLDivElement | null>(null);
  const timeoutRef = useRef<number | undefined>(undefined);
  const exitingRef = useRef<boolean>(false);

  // State
  const [isVisible, setIsVisible] = useState<boolean>(false);

  // Hooks
  const { checkConsent, setConsent } = useCookieConsent();

  // 初始化逻辑
  useEffect(() => {
    if (checkConsent()) return;

    // 延迟初始化，确保 DOM 已完全加载
    const timer = setTimeout(() => {
      setIsVisible(true);
      
      // 设置自动关闭
      // 注释: 在TypeScript中，setTimeout返回number类型
      timeoutRef.current = window.setTimeout(() => {
        handleClose('auto');
      }, 5000);
    }, 500);

    return () => {
      clearTimeout(timer);
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, [checkConsent]);

  // 关闭处理
  // 注释: 使用字面量联合类型限制可能的操作类型
  const handleClose = (action: 'accept' | 'decline' | 'auto'): void => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    if (exitingRef.current) return;
    exitingRef.current = true;

    try {
      if (containerRef.current) {
        containerRef.current.classList.add('cookie-exiting');
      }

      // 设置延迟，让动画有时间执行
      setTimeout(() => {
        setIsVisible(false);
        
        // 设置同意状态
        setConsent(
          action === 'accept' ? 'accepted' : 'declined',
          action === 'accept' ? 'uuid' : null
        );
        
        exitingRef.current = false;
      }, 300);
    } catch (error) {
      console.error('关闭过程出错:', error);
      exitingRef.current = false;
    }
  };

  if (!isVisible) return null;

  return (
    <div 
      ref={containerRef}
      className="ant-cookie-consent"
      role="alert"
      aria-live="polite"
    >
      <Alert
        message="Cookie 设置"
        description="我们使用必要的 Cookie 确保网站正常运作"
        type="info"
        showIcon
        action={
          <Space>
            <Button size="small" onClick={() => handleClose('decline')}>
              拒绝
            </Button>
            <Button size="small" type="primary" onClick={() => handleClose('accept')}>
              同意
            </Button>
          </Space>
        }
      />
    </div>
  );
};