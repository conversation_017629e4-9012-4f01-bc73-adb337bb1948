import React from 'react';
import { GalleryHeader } from './GalleryHeader';
import { GalleryCard } from './GalleryCard';
import { GalleryLoadingState } from './LoadingState';
import { GalleryProps, GalleryItem } from './types';
import './Gallery.css';

/**
 * 图库组件
 * 显示产品图库，支持点击查看大图
 * 
 * @param props - 图库组件属性
 * @returns React组件
 */
export const Gallery: React.FC<GalleryProps> = ({ data }) => {
    return (
        <section id="portfolio" className="py-5">
            <div className="container">
                <GalleryHeader description={data?.length ? data[0].description : undefined} />

                <div className="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                    {data ? (
                        data.map((item: GalleryItem, index: number) => (
                            <GalleryCard
                                key={`${item.title}-${index}`}
                                item={item}
                                index={index}
                            />
                        ))
                    ) : (
                        <GalleryLoadingState />
                    )}
                </div>
            </div>
        </section>
    );
};
