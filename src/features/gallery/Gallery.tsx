import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Row, Col, Typography, Button, Divider } from 'antd';
import { RightOutlined, EyeOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import './Gallery.css';

const { Title, Paragraph } = Typography;
const { Meta } = Card;

// 产品数据 - 只保留3个产品
const productData = [
  {
    id: 'industrial-ice',
    title: '工业冰',
    image: '/img/industrial-ice-1.jpg',
    description: '专为工业应用设计的高质量冰块，适用于建筑、混凝土养护、食品加工等领域。我们的工业冰具有更长的融化时间和更高的冷却效率，帮助您降低成本并提高生产效率。',
    features: ['高冷却效率', '长时间保持低温', '定制尺寸', '大批量供应']
  },
  {
    id: 'edible-ice',
    title: '食用冰',
    image: '/img/food-ice-1.jpg',
    description: '符合食品安全标准的高品质食用冰，适用于餐饮、酒店、超市等场所。我们的食用冰采用纯净水制作，确保安全卫生，为您的饮品和食品提供完美的冷却解决方案。',
    features: ['食品级安全标准', '晶莹剔透', '多种形状可选', '快速配送服务']
  },
  {
    id: 'cooling-ice',
    title: '降温冰',
    image: '/img/dry-ice-1.jpg',
    description: '专为环境降温设计的特殊冰块，适用于户外活动、工地降温、仓库温控等场景。我们的降温冰融化速度适中，能够持续释放冷量，有效改善高温环境。',
    features: ['高效降温', '环保无污染', '适用多种场景', '成本效益高']
  }
];

// 样式组件
const GallerySection = styled.section`
  padding: 60px 0;
  background-color: #f7f9fc;
`;

const StyledCard = styled(Card)`
  height: 100%;
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    transform: translateY(-5px);
  }

  .ant-card-cover img {
    height: 240px;
    object-fit: cover;
    transition: transform 0.5s ease;
  }

  &:hover .ant-card-cover img {
    transform: scale(1.05);
  }

  .ant-card-meta-title {
    font-size: 20px;
    margin-bottom: 12px;
  }

  .ant-card-meta-description {
    height: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
  }
`;

const FeatureTag = styled.span`
  display: inline-block;
  background-color: #f0f5ff;
  color: #1890ff;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  margin-right: 8px;
  margin-bottom: 8px;
`;

const ViewMoreButton = styled(Button)`
  margin-top: 40px;
  height: 48px;
  font-size: 16px;
  padding: 0 32px;
`;

/**
 * 图库组件
 * 显示3个主要产品图片，并提供查看更多按钮跳转到产品页面
 *
 * @returns React组件
 */
export const Gallery: React.FC = () => {
  const navigate = useNavigate();

  const handleViewMore = () => {
    navigate('/product');
  };

  const handleViewDetail = (productId: string) => {
    navigate(`/product?category=${productId}`);
  };

  return (
    <GallerySection id="portfolio">
      <div className="container">
        <Row gutter={[0, 40]} justify="center">
          <Col span={24} className="text-center">
            <Title level={2}>产品图库</Title>
            <Paragraph style={{ fontSize: 16, maxWidth: 800, margin: '0 auto' }}>
              上海寒链实业有限公司提供多种高品质冰产品，满足不同行业和场景的需求。
              从工业应用到食品服务，我们都能提供专业的冷链解决方案。
            </Paragraph>
            <Divider style={{ width: 80, minWidth: 80, margin: '24px auto' }} />
          </Col>

          <Col span={24}>
            <Row gutter={[24, 24]}>
              {productData.map(product => (
                <Col xs={24} md={8} key={product.id}>
                  <StyledCard
                    hoverable
                    cover={<img alt={product.title} src={product.image} />}
                    actions={[
                      <Button
                        type="link"
                        onClick={() => handleViewDetail(product.id)}
                        icon={<EyeOutlined />}
                      >
                        查看详情
                      </Button>
                    ]}
                  >
                    <Meta
                      title={product.title}
                      description={product.description}
                    />
                    <div style={{ marginTop: 16 }}>
                      {product.features.map((feature, index) => (
                        <FeatureTag key={index}>{feature}</FeatureTag>
                      ))}
                    </div>
                  </StyledCard>
                </Col>
              ))}
            </Row>
          </Col>

          <Col span={24} className="text-center">
            <ViewMoreButton
              type="primary"
              size="large"
              onClick={handleViewMore}
              icon={<RightOutlined />}
            >
              查看更多产品信息
            </ViewMoreButton>
          </Col>
        </Row>
      </div>
    </GallerySection>
  );
};
