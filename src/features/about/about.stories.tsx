import type { Meta, StoryObj } from '@storybook/react';
import { About } from './about';

// 定义Storybook配置
const meta = {
  title: 'Sections/About',
  component: About,
  parameters: {
    layout: 'fullscreen',
  },
} satisfies Meta<typeof About>;

export default meta;
type Story = StoryObj<typeof meta>;

// 默认状态
export const Default: Story = {
  args: {
    data: {
      imageUrl: 'img/storybook-about.jpg',
      paragraph: '专业冷链解决方案提供商，为您提供全方位的冷链物流服务。我们致力于为客户提供高质量、高效率的冷链物流解决方案，确保您的产品在运输过程中保持最佳状态。',
      why: [
        "✔️ 纯净品质：每一块冰都经严格质检，晶莹无瑕，融化慢、无异味，为饮品锁住最佳口感",
        "✔️ 丰富选择：食用冰、碎冰、干冰、创意造型冰（可定制图案）一应俱全，满足冷饮制作、食材保鲜、活动装饰等多样需求",
        "✔️ 灵活服务：小到家庭用冰袋，大到企业月供，按需定制，随时调整，拒绝浪费"
      ],
      why2: [
        '✔️智能温控系统，确保产品质量',
        '✔️全网直供，覆盖全国各大城市',
        '✔️专业团队，提供一站式服务'
      ]
    }
  }
};

// 加载状态
export const Loading: Story = {
  args: {
    data: undefined
  }
};

// 部分数据状态
export const PartialData: Story = {
  args: {
    data: {
      paragraph: '专业冷链解决方案提供商，为您提供全方位的冷链物流服务。',
      why: [
        "✔️ 纯净品质：每一块冰都经严格质检",
        "✔️ 丰富选择：多种规格可选"
      ]
    }
  }
};