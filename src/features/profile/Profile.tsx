import React from 'react';
import { Card, Avatar, Button, Skeleton, message } from 'antd';
import { useAppState } from '@/hooks/useAppState';
import { useQueryState } from '@/hooks/useQueryState';
import { fetchUserProfile, UserProfile } from '@/services/api/userService';

export const Profile: React.FC = () => {
  const { user, userActions } = useAppState();

  // 使用 useQueryState 获取用户资料
  const {
    data: profile,
    isLoading,
    error,
    refresh
  } = useQueryState<UserProfile>(
    ['profile', user.user?.id],
    () => fetchUserProfile(user.user?.id),
    {
      enabled: !!user.user?.id,
      onError: (err: any) => {
        message.error('获取用户资料失败');
        console.error(err);
      }
    }
  );
  
  // 渲染内容
  const content = useMemo(() => {
    // 未登录状态
    if (!user.isAuthenticated) {
      return <p>请先登录查看您的资料</p>;
    }
    
    // 加载状态
    if (isLoading) {
      return <Skeleton avatar paragraph={{ rows: 4 }} active />;
    }

    // 错误状态
    if (error || !profile) {
      return (
        <>
          <p>加载用户资料失败</p>
          <Button onClick={refresh}>重试</Button>
        </>
      );
    }

    // 编辑模式
    if (isEditing) {
      return (
        <Form form={form} layout="vertical">
          <Form.Item name="avatar" label="头像">
            <Upload
              name="avatar"
              listType="picture-card"
              showUploadList={false}
              action="/api/upload"
              onChange={({ file }) => {
                if (file.status === 'done') {
                  form.setFieldsValue({ avatar: file.response.url });
                }
              }}
            >
              {profile.avatar ? (
                <Avatar size={64} src={profile.avatar} />
              ) : (
                <div>
                  <UploadOutlined />
                  <div style={{ marginTop: 8 }}>上传头像</div>
                </div>
              )}
            </Upload>
          </Form.Item>

          <Form.Item name="name" label="姓名" rules={[{ required: true }]}>
            <Input />
          </Form.Item>

          <Form.Item name="email" label="邮箱" rules={[{ type: 'email' }]}>
            <Input />
          </Form.Item>

          <div className="flex gap-2 mt-4">
            <Button 
              type="primary" 
              icon={<SaveOutlined />}
              onClick={async () => {
                try {
                  const values = await form.validateFields();
                  await updateUserProfile(user.user?.id, values);
                  message.success('资料更新成功');
                  setIsEditing(false);
                  refresh();
                } catch (err) {
                  message.error('更新失败');
                }
              }}
            >
              保存
            </Button>
            <Button 
              icon={<CloseOutlined />}
              onClick={() => setIsEditing(false)}
            >
              取消
            </Button>
          </div>
        </Form>
      );
    }

    // 查看模式
    return (
      <>
        <div className="flex items-center mb-4">
          <Avatar size={64} src={profile?.avatar} />
          <div className="ml-4">
            <h2 className="text-xl font-bold">{profile?.name}</h2>
            <p className="text-gray-500">{profile?.email}</p>
          </div>
        </div>
        
        <div className="mt-4">
          <p><strong>角色:</strong> {profile?.role}</p>
          <p><strong>注册时间:</strong> {dayjs(profile?.createdAt).format('YYYY-MM-DD HH:mm')}</p>
        </div>
        
        <div className="flex gap-2 mt-4">
          <Button 
            type="primary" 
            icon={<EditOutlined />}
            onClick={() => setIsEditing(true)}
          >
            编辑资料
          </Button>
          <Button 
            danger 
            onClick={userActions.logout}
          >
            退出登录
          </Button>
        </div>
      </>
    );
  }, [user, isLoading, error, profile, isEditing, form, refresh]);

  return (
    <Card 
      title="用户资料" 
      extra={<Button onClick={refresh}>刷新</Button>}
    >
      {content}
    </Card>
  );
};