import React, { createContext, useContext, useEffect } from 'react';

// 定义跟踪器接口
interface Tracker {
  trackPageView: (path: string) => void;
  trackEvent: (category: string, action: string, label?: string, value?: number) => void;
}

// 创建上下文
const TrackerContext = createContext<Tracker | null>(null);

// 创建一个简单的跟踪器实现
const createTracker = (): Tracker => {
  return {
    trackPageView: (path: string) => {
      if (process.env.NODE_ENV !== 'production') {
        console.log(`[Analytics] Page view: ${path}`);
      }
      // 在这里添加实际的分析代码，例如 Google Analytics
    },
    trackEvent: (category: string, action: string, label?: string, value?: number) => {
      if (process.env.NODE_ENV !== 'production') {
        console.log(`[Analytics] Event: ${category} / ${action} / ${label || ''} / ${value || ''}`);
      }
      // 在这里添加实际的分析代码
    }
  };
};

// 跟踪器提供者组件
interface TrackerProviderProps {
  children: React.ReactNode;
}

export const TrackerProvider: React.FC<TrackerProviderProps> = ({ children }) => {
  const tracker = createTracker();

  // 在组件挂载时跟踪初始页面视图
  useEffect(() => {
    tracker.trackPageView(window.location.pathname);
  }, []);

  return (
    <TrackerContext.Provider value={tracker}>
      {children}
    </TrackerContext.Provider>
  );
};

// 自定义钩子，用于在组件中访问跟踪器
export const useTracker = (): Tracker => {
  const tracker = useContext(TrackerContext);
  if (!tracker) {
    throw new Error('useTracker must be used within a TrackerProvider');
  }
  return tracker;
};

export default TrackerProvider;