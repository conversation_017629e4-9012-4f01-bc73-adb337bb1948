import React, { useEffect, useState } from 'react';
import { Row, Col, Card, Spin } from 'antd';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { featureService } from '@/services/api/featureService';
import type { EnhancedFeature } from '@/types/feature';

const FeatureSection: React.FC = () => {
  const [features, setFeatures] = useState<EnhancedFeature[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const loadFeatures = async () => {
      try {
        const data = await featureService.fetchFeatures();
        setFeatures(data);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载特性失败');
      } finally {
        setLoading(false);
      }
    };
    
    loadFeatures();
  }, []);
  
  if (loading) {
    return (
      <div className="text-center py-5">
        <Spin size="large" />
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="text-center py-5 text-red-500">
        {error}
      </div>
    );
  }
  
  return (
    <section className="py-12 bg-gray-50">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-center mb-8">我们的特性</h2>
        
        <Row gutter={[24, 24]}>
          {features.map((feature) => (
            <Col xs={24} sm={12} lg={8} key={feature.id}>
              <Card 
                className="h-full hover:shadow-md transition-shadow"
                bordered={false}
              >
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary-50 text-primary-600 mb-4">
                    <FontAwesomeIcon icon={feature.icon} size="2x" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                  <p className="text-gray-600">{feature.text}</p>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    </section>
  );
};

export default FeatureSection;