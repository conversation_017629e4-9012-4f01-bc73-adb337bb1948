export default {
  common: {
    loading: "Loading...",
    success: "Success",
    error: "Error",
    warning: "Warning",
    info: "Information",
  },
  api: {
    apiRequestFailed: "API request failed",
    errorMessage: "An error occurred",
    networkError: "Network error, please check your connection",
    unauthorized: "Unauthorized, please login again",
    forbidden: "Access forbidden",
    notFound: "Resource not found",
    serverError: "Server error, please try again later",
    unknownError: "Unknown error occurred",
    timeout: "Request timeout",
    retry: "Retrying...",
  },
  auth: {
    login: "Login",
    logout: "Logout",
    register: "Register",
    forgotPassword: "Forgot Password",
    resetPassword: "Reset Password",
    username: "<PERSON>rna<PERSON>",
    password: "Password",
    email: "Email",
  },
  // 其他翻译...
};