import React from 'react';
import { BrowserRouter as Router } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { ApiProvider } from '../../services/api/ApiProvider';
import { QueryProvider } from '../../services/query/QueryProvider';
import { ThemeProvider } from '../../theme/theme-provider';
import TrackerProvider from '../../features/analytics/TrackerProvider';

interface AppProvidersProps {
  children: React.ReactNode;
}

/**
 * 应用程序提供者组件
 * 统一管理所有的 Context Provider，避免在 App 组件中嵌套过深
 */
export const AppProviders: React.FC<AppProvidersProps> = ({ children }) => {
  return (
    <HelmetProvider>
      <ApiProvider>
        <QueryProvider>
          <ThemeProvider>
            <TrackerProvider>
              <Router>
                {children}
              </Router>
            </TrackerProvider>
          </ThemeProvider>
        </QueryProvider>
      </ApiProvider>
    </HelmetProvider>
  );
};
