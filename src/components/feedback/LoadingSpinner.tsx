import React from 'react';
import { LoadingSpinnerProps } from './types';

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = 'primary',
  className = ''
}) => {
  const sizeClass = size === 'sm' ? 'spinner-border-sm' :
                   size === 'lg' ? 'spinner-border-lg' : '';

  return (
    <div className={`spinner-border text-${color} ${sizeClass} ${className}`} role="status">
      <span className="visually-hidden">加载中...</span>
    </div>
  );
};

export default LoadingSpinner;