import React from 'react';
import { SimpleLoadingSpinnerProps } from './types';

/**
 * 简单加载旋转器组件
 * 显示加载中的旋转器，支持自定义尺寸和颜色
 * 
 * @param props - 简单加载旋转器组件属性
 * @returns React组件
 */
export const SimpleLoadingSpinner: React.FC<SimpleLoadingSpinnerProps> = ({ 
  size = 'md', 
  color = 'primary', 
  className = '' 
}) => {
  // 尺寸映射表
  const sizeMap: Record<string, string> = {
    sm: 'spinner-border-sm',
    md: '',
    lg: 'spinner-border-lg'
  };

  return (
    <div
      className={`spinner-border text-${color} ${sizeMap[size]} ${className}`}
      role="status"
      aria-label="加载中"
    >
      <span className="visually-hidden">加载中...</span>
    </div>
  );
};

export default SimpleLoadingSpinner;
