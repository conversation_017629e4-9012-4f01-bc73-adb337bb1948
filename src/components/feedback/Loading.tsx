import React from 'react';
import { LoadingProps } from './types';

export const Loading: React.FC<LoadingProps> = ({ placeholder = 'default' }) => {
    const getPlaceholderText = () => {
        const messages = {
            list: '正在加载优势列表...',
            paragraph: '正在加载内容描述...',
            default: '数据加载中...'
        };

        return <span className="text-muted">{messages[placeholder] || messages.default}</span>;
    };

    return getPlaceholderText();
};