import React from 'react';
import './PageFooter.css';

interface PageFooterProps {
  recordNumbers?: string[];
}

/**
 * 页脚组件
 * 显示版权信息和备案号
 */
export const PageFooter: React.FC<PageFooterProps> = ({ 
  recordNumbers = ["沪ICP备2025123281号", "沪ICP备2025123281号-1"] 
}) => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="page-footer bg-dark text-light py-4">
      <div className="container">
        <div className="row">
          <div className="col-md-6">
            <p className="mb-0">
              &copy; {currentYear} 上海寒链实业有限公司. 保留所有权利.
            </p>
          </div>
          <div className="col-md-6 text-md-end">
            <div className="footer-links">
              {recordNumbers.map((record, index) => (
                <span key={index} className="record-number">
                  {record}
                  {index < recordNumbers.length - 1 && ' | '}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
