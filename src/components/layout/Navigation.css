/* Navigation Styles */
#menu {
  padding: 15px;
  transition: all 0.8s;
}

#menu.navbar-default {
  background-color: #fff;
  border-color: rgba(231, 231, 231, 0);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
}

#menu a.navbar-brand {
  font-family: "Raleway", sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: #333;
  text-transform: uppercase;
}

#menu.navbar-default .navbar-nav > li > a {
  font-family: "Lato", sans-serif;
  text-transform: uppercase;
  color: #555;
  font-size: 15px;
  font-weight: 400;
  padding: 8px 2px;
  border-radius: 0;
  margin: 9px 20px 0;
}

#menu.navbar-default .navbar-nav > li > a:hover {
  color: #1e7a46;
}

#menu.navbar-default .navbar-nav > li > a.active {
  color: #1e7a46;
}

.navbar {
  background-color: #fff !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
  font-family: "Raleway", sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: #333 !important;
  text-transform: uppercase;
  text-decoration: none;
}

.navbar-nav .nav-link {
  font-family: "Lato", sans-serif;
  text-transform: uppercase;
  color: #555 !important;
  font-size: 15px;
  font-weight: 400;
  padding: 8px 20px;
  border-radius: 0;
  margin: 0 5px;
  transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
  color: #1e7a46 !important;
}

.navbar-toggler {
  border: none;
  padding: 4px 8px;
}

.navbar-toggler:focus {
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 991.98px) {
  .navbar-nav {
    text-align: center;
    margin-top: 1rem;
  }
  
  .navbar-nav .nav-link {
    margin: 5px 0;
    padding: 10px 20px;
  }
}
