/* 联系浮动栏样式 */
.contact-float-bar {
  position: fixed;
  right: 20px;
  transform: translateY(-50%);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.contact-item-wrapper {
  position: relative;
}

.contact-item {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  background-color: var(--theme-color, #1890ff);
}

.contact-item:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.contact-icon {
  font-size: 20px;
  color: white;
}

/* 展开的信息卡片 */
.contact-expanded {
  position: absolute;
  right: 60px;
  top: 50%;
  transform: translateY(-50%);
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 0;
  min-width: 200px;
  animation: slideInRight 0.3s ease;
}

.contact-expanded-content {
  padding: 15px;
  text-align: center;
  position: relative;
}

.contact-expanded-content h6 {
  margin: 0 0 10px 0;
  color: #333;
  font-weight: 600;
}

.contact-qr-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 4px;
  margin: 10px 0;
}

.contact-expanded-content p {
  margin: 10px 0 0 0;
  color: #666;
  font-size: 12px;
}

.close-btn {
  position: absolute;
  top: 5px;
  right: 8px;
  background: none;
  border: none;
  font-size: 18px;
  color: #999;
  cursor: pointer;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background-color: #f5f5f5;
  color: #333;
}

/* 工具提示样式 */
.contact-tooltip .ant-tooltip-inner {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 12px;
  padding: 6px 10px;
  border-radius: 4px;
}

.contact-tooltip .ant-tooltip-arrow::before {
  background-color: rgba(0, 0, 0, 0.8);
}

/* 动画 */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .contact-float-bar {
    right: 10px;
  }
  
  .contact-item {
    width: 45px;
    height: 45px;
  }
  
  .contact-icon {
    font-size: 18px;
  }
  
  .contact-expanded {
    right: 55px;
    min-width: 180px;
  }
  
  .contact-qr-image {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 480px) {
  .contact-float-bar {
    right: 5px;
  }
  
  .contact-item {
    width: 40px;
    height: 40px;
  }
  
  .contact-icon {
    font-size: 16px;
  }
  
  .contact-expanded {
    right: 50px;
    min-width: 160px;
  }
  
  .contact-expanded-content {
    padding: 12px;
  }
  
  .contact-qr-image {
    width: 80px;
    height: 80px;
  }
}
