import React from 'react';
import { Outlet } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';

interface MainLayoutProps {
    data: Record<string, any>;
}

export const MainLayout: React.FC<MainLayoutProps> = () => {
    return (
        <div className="main-layout">
            <Helmet>
                <title>上海寒链实业有限公司 | 专业冷链解决方案</title>
                <meta name="description" content="上海寒链实业有限公司提供专业的冷链物流和食用冰生产服务，满足各行业客户需求。" />
            </Helmet>

            <main>
                <Outlet />
            </main>
        </div>
    );
};