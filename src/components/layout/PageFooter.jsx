// components/layout/PageFooter.jsx
import React from 'react';
import PropTypes from 'prop-types';
// 修正导入路径 - 使用正确的路径
import { PageFooter as ContactPageFooter } from '@/features/contact/PageFooter.jsx';

// 这个组件是一个包装器，确保从 layout 目录导入时也能正确工作
export const PageFooter = (props) => {
  console.log('Layout PageFooter 包装器被调用，属性:', props);
  
  // 如果传入的是 data 属性，则提取 recordNumbers
  if (props.data && !props.recordNumbers) {
    const recordNumbers = props.data.recordNumbers || ["沪ICP备2025123281号", "沪ICP备2025123281号-1"];
    return <ContactPageFooter recordNumbers={recordNumbers} />;
  }
  
  // 否则直接传递所有属性
  return <ContactPageFooter {...props} />;
};

PageFooter.propTypes = {
  data: PropTypes.shape({
    recordNumbers: PropTypes.arrayOf(PropTypes.string)
  }),
  recordNumbers: PropTypes.arrayOf(PropTypes.string)
};

export default PageFooter;
