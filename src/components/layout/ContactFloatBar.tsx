'use client';

import React, { useState } from 'react';
import { Tooltip } from 'antd';
import './ContactFloatBar.css';

interface ContactItem {
  id: string;
  icon: string;
  title: string;
  description: string;
  link?: string;
  image?: string;
}

interface ContactFloatBarProps {
  items: ContactItem[];
  themeColor?: string;
  top?: string;
}

const defaultContactItems: ContactItem[] = [
  {
    id: 'wechat',
    icon: '💬',
    title: '微信客服',
    description: '扫码添加微信',
    image: '/img/wechat-qr.jpg'
  },
  {
    id: 'phone',
    icon: '📞',
    title: '电话咨询',
    description: '13761182299',
    link: 'tel:13761182299'
  },
  {
    id: 'email',
    icon: '✉️',
    title: '邮件联系',
    description: '<EMAIL>',
    link: 'mailto:<EMAIL>'
  },
  {
    id: 'location',
    icon: '📍',
    title: '公司地址',
    description: '上海市宝山区长江西路2311号1-2层'
  }
];

/**
 * 联系浮动栏组件
 * 显示在页面右侧的联系方式快捷入口
 */
export const ContactFloatBar: React.FC<ContactFloatBarProps> = ({
  items = defaultContactItems,
  themeColor = '#1890ff',
  top = '50%'
}) => {
  const [expandedItem, setExpandedItem] = useState<string | null>(null);

  const handleItemClick = (item: ContactItem) => {
    if (item.link) {
      window.open(item.link, '_blank');
    } else if (item.image) {
      setExpandedItem(expandedItem === item.id ? null : item.id);
    }
  };

  return (
    <div
      className="contact-float-bar"
      style={{ top, '--theme-color': themeColor } as React.CSSProperties}
    >
      {items.map((item) => (
        <div key={item.id} className="contact-item-wrapper">
          <Tooltip
            title={item.description}
            placement="left"
            classNames={{ root: "contact-tooltip" }}
          >
            <div
              className="contact-item"
              onClick={() => handleItemClick(item)}
              style={{ backgroundColor: themeColor }}
            >
              <span className="contact-icon">{item.icon}</span>
            </div>
          </Tooltip>

          {/* 展开的信息卡片 */}
          {expandedItem === item.id && item.image && (
            <div className="contact-expanded">
              <div className="contact-expanded-content">
                <h6>{item.title}</h6>
                <img src={item.image} alt={item.title} className="contact-qr-image" />
                <p>{item.description}</p>
                <button
                  className="close-btn"
                  onClick={(e) => {
                    e.stopPropagation();
                    setExpandedItem(null);
                  }}
                >
                  ×
                </button>
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};
