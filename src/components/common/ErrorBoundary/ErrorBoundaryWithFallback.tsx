import React, { Component, ReactNode } from 'react';
import { Button, Result, Typography } from 'antd';
import { ReloadOutlined, HomeOutlined } from '@ant-design/icons';

const { Paragraph, Text } = Typography;

interface ErrorInfo {
  componentStack: string;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

interface ErrorBoundaryProps {
  /** 子组件 */
  children: ReactNode;
  /** 错误回退组件 */
  fallback?: (error: Error, errorInfo: ErrorInfo, retry: () => void) => ReactNode;
  /** 错误级别 */
  level?: 'page' | 'section' | 'component';
  /** 组件名称，用于错误追踪 */
  name?: string;
  /** 是否显示错误详情 */
  showErrorDetails?: boolean;
  /** 错误回调 */
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

/**
 * 增强的错误边界组件
 * 提供更好的错误处理和用户体验
 */
export class ErrorBoundaryWithFallback extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimeoutId: number | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ errorInfo });

    // 记录错误信息
    this.logError(error, errorInfo);

    // 调用错误回调
    this.props.onError?.(error, errorInfo);
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  /**
   * 记录错误信息
   */
  private logError = (error: Error, errorInfo: ErrorInfo) => {
    const { name, level } = this.props;
    const { errorId } = this.state;

    console.group(`🚨 Error Boundary Caught Error [${level || 'component'}]`);
    console.error('Component:', name || 'Unknown');
    console.error('Error ID:', errorId);
    console.error('Error:', error);
    console.error('Error Info:', errorInfo);
    console.error('Stack:', error.stack);
    console.error('Component Stack:', errorInfo.componentStack);
    console.groupEnd();

    // 这里可以发送错误到监控服务
    // errorReportingService.report({
    //   errorId,
    //   component: name,
    //   level,
    //   error: error.message,
    //   stack: error.stack,
    //   componentStack: errorInfo.componentStack,
    //   timestamp: Date.now(),
    //   url: window.location.href,
    //   userAgent: navigator.userAgent,
    // });
  };

  /**
   * 重试函数
   */
  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    });
  };

  /**
   * 延迟重试
   */
  private handleDelayedRetry = (delay: number = 1000) => {
    this.retryTimeoutId = window.setTimeout(() => {
      this.handleRetry();
    }, delay);
  };

  /**
   * 返回首页
   */
  private handleGoHome = () => {
    window.location.href = '/';
  };

  /**
   * 刷新页面
   */
  private handleReload = () => {
    window.location.reload();
  };

  /**
   * 获取错误级别对应的标题和描述
   */
  private getErrorContent = () => {
    const { level } = this.props;
    
    switch (level) {
      case 'page':
        return {
          title: '页面加载失败',
          subTitle: '抱歉，页面遇到了一些问题，请尝试刷新页面或返回首页。',
        };
      case 'section':
        return {
          title: '模块加载失败',
          subTitle: '抱歉，该模块遇到了一些问题，请尝试重新加载。',
        };
      default:
        return {
          title: '组件加载失败',
          subTitle: '抱歉，该组件遇到了一些问题，请尝试重新加载。',
        };
    }
  };

  /**
   * 渲染错误详情
   */
  private renderErrorDetails = () => {
    const { showErrorDetails } = this.props;
    const { error, errorInfo, errorId } = this.state;

    if (!showErrorDetails || !error) return null;

    return (
      <div style={{ marginTop: 16, textAlign: 'left' }}>
        <Typography.Title level={5}>错误详情</Typography.Title>
        <Paragraph>
          <Text strong>错误ID:</Text> <Text code>{errorId}</Text>
        </Paragraph>
        <Paragraph>
          <Text strong>错误信息:</Text> <Text code>{error.message}</Text>
        </Paragraph>
        {errorInfo && (
          <Paragraph>
            <Text strong>组件堆栈:</Text>
            <pre style={{ 
              background: '#f5f5f5', 
              padding: 8, 
              borderRadius: 4, 
              fontSize: 12,
              overflow: 'auto',
              maxHeight: 200,
            }}>
              {errorInfo.componentStack}
            </pre>
          </Paragraph>
        )}
      </div>
    );
  };

  /**
   * 渲染默认错误回退UI
   */
  private renderDefaultFallback = () => {
    const { level } = this.props;
    const { title, subTitle } = this.getErrorContent();

    const actions = [];

    // 根据错误级别提供不同的操作
    if (level === 'page') {
      actions.push(
        <Button key="home" icon={<HomeOutlined />} onClick={this.handleGoHome}>
          返回首页
        </Button>,
        <Button key="reload" type="primary" icon={<ReloadOutlined />} onClick={this.handleReload}>
          刷新页面
        </Button>
      );
    } else {
      actions.push(
        <Button key="retry" type="primary" icon={<ReloadOutlined />} onClick={this.handleRetry}>
          重新加载
        </Button>
      );
    }

    return (
      <Result
        status="error"
        title={title}
        subTitle={subTitle}
        extra={actions}
      >
        {this.renderErrorDetails()}
      </Result>
    );
  };

  render() {
    const { hasError, error, errorInfo } = this.state;
    const { children, fallback } = this.props;

    if (hasError && error && errorInfo) {
      // 如果提供了自定义回退组件，使用自定义组件
      if (fallback) {
        return fallback(error, errorInfo, this.handleRetry);
      }

      // 否则使用默认回退UI
      return this.renderDefaultFallback();
    }

    return children;
  }
}

/**
 * 简化的错误边界组件
 */
export const SimpleErrorBoundary: React.FC<{
  children: ReactNode;
  fallback?: ReactNode;
}> = ({ children, fallback }) => {
  return (
    <ErrorBoundaryWithFallback
      level="component"
      fallback={fallback ? () => fallback : undefined}
    >
      {children}
    </ErrorBoundaryWithFallback>
  );
};
