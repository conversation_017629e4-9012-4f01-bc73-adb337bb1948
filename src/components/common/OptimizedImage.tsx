import React from 'react';

interface OptimizedImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  lazy?: boolean;
  className?: string;
}

/**
 * 优化的图片组件
 * - 自动添加宽高属性避免布局偏移
 * - 支持懒加载
 * - 添加合适的 loading 属性
 * 
 * @param props - 图片属性
 * @returns React组件
 */
export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  lazy = true,
  className,
  ...rest
}) => {
  // 确保有 alt 文本
  const safeAlt = alt || '';
  
  // 确定加载策略
  const loading = lazy ? 'lazy' : 'eager';
  
  // 确定解码策略
  const decoding = lazy ? 'async' : 'auto';
  
  return (
    <img
      src={src}
      alt={safeAlt}
      width={width}
      height={height}
      loading={loading}
      decoding={decoding}
      className={className}
      {...rest}
    />
  );
};

export default OptimizedImage;
