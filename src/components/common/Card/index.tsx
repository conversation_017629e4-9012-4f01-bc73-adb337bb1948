import React from 'react';
import clsx from 'clsx';

export interface CardProps {
  title?: React.ReactNode;
  subtitle?: React.ReactNode;
  children: React.ReactNode;
  footer?: React.ReactNode;
  className?: string;
  hoverable?: boolean;
  bordered?: boolean;
  loading?: boolean;
}

export const Card: React.FC<CardProps> = ({
  title,
  subtitle,
  children,
  footer,
  className,
  hoverable = false,
  bordered = true,
  loading = false,
}) => {
  return (
    <div 
      className={clsx(
        'bg-white rounded-lg shadow-sm overflow-hidden',
        bordered && 'border border-gray-200',
        hoverable && 'transition-shadow hover:shadow-md',
        loading && 'animate-pulse',
        className
      )}
    >
      {(title || subtitle) && (
        <div className="px-4 py-3 border-b border-gray-100">
          {title && <h3 className="text-lg font-medium">{title}</h3>}
          {subtitle && <p className="text-sm text-gray-500 mt-1">{subtitle}</p>}
        </div>
      )}
      
      <div className="p-4">{children}</div>
      
      {footer && (
        <div className="px-4 py-3 bg-gray-50 border-t border-gray-100">
          {footer}
        </div>
      )}
    </div>
  );
};

export default Card;