import { CarouselItem } from './types';

/**
 * 默认轮播项目数据
 */
export const defaultCarouselItems: CarouselItem[] = [
  {
    id: 'slide-1',
    webp: 'https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=1920&h=1080&fit=crop&crop=center',
    fallback: 'https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=1920&h=1080&fit=crop&crop=center',
    alt: '专业制冰解决方案',
    loading: 'eager',
    buttonText: '了解更多',
    buttonLink: '#about'
  },
  {
    id: 'slide-2',
    webp: 'https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?w=1920&h=1080&fit=crop&crop=center',
    fallback: 'https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?w=1920&h=1080&fit=crop&crop=center',
    alt: '工业制冰设备',
    loading: 'lazy',
    buttonText: '查看产品',
    buttonLink: '/products'
  },
  {
    id: 'slide-3',
    webp: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&h=1080&fit=crop&crop=center',
    fallback: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&h=1080&fit=crop&crop=center',
    alt: '食用冰制品',
    loading: 'lazy',
    buttonText: '联系我们',
    buttonLink: '/contact'
  }
];

/**
 * 轮播项目数据（导出别名）
 */
export const carouselItems = defaultCarouselItems;
