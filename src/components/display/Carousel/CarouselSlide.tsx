import * as React from 'react';
import { Carousel } from 'react-bootstrap';
import { CarouselProps } from './types';
import './Carousel.css';

/**
 * 自定义轮播组件
 * 基于React-Bootstrap的Carousel组件，支持WebP格式和响应式设计
 *
 * @param props - 轮播组件属性
 * @returns React组件
 */
export const CustomCarousel: React.FC<CarouselProps> = ({
  items,
  controls = true,
  indicators = true,
  interval = 5000,
  pause = 'hover', // 修改为 'hover'，符合 react-bootstrap Carousel 组件的类型定义
  className = "header-carousel"
}) => {
  return (
    <Carousel
      controls={controls}
      indicators={indicators}
      interval={interval}
      pause={pause}
      className={className}
    >
      {items.map((item) => (
        <Carousel.Item key={item.id} className="h-100">
          <picture className="h-100">
            {/* WebP格式优先，提供更好的压缩率和质量 */}
            <source srcSet={item.webp} type="image/webp" />
            {/* 备用图片格式，用于不支持WebP的浏览器 */}
            <img
              src={item.fallback}
              className="d-block w-100 img-cover"
              alt={item.alt}
              loading={item.loading}
              decoding="async"
            />
          </picture>
          <Carousel.Caption className="animated-caption">
            {item.buttonText && (
              <div className="slide-button" data-aos="fade-up" data-aos-delay="300">
                <a
                  className="btn btn-primary btn-lg rounded-pill"
                  href={item.buttonLink || "#about"}
                >
                  {item.buttonText}
                </a>
              </div>
            )}
          </Carousel.Caption>
        </Carousel.Item>
      ))}
    </Carousel>
  );
};
