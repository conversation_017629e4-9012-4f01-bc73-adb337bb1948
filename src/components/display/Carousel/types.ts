/**
 * 轮播图项目接口
 * 定义轮播图中每个幻灯片的数据结构
 */
export interface CarouselItem {
  /** 唯一标识符 */
  id: number;
  /** WebP格式图片路径 */
  webp: string;
  /** 备用图片路径（用于不支持WebP的浏览器） */
  fallback: string;
  /** 图片替代文本 */
  alt: string;
  /** 幻灯片标题 */
  title?: string;
  /** 幻灯片描述 */
  description?: string;
  /** 图片加载策略 */
  loading: 'eager' | 'lazy';
  /** 可选的按钮文本 */
  buttonText?: string;
  /** 可选的按钮链接 */
  buttonLink?: string;
}

/**
 * 轮播组件属性接口
 */
export interface CarouselProps {
  /** 轮播项目数组 */
  items: CarouselItem[];
  /** 是否显示控制按钮 */
  controls?: boolean;
  /** 是否显示指示器 */
  indicators?: boolean;
  /** 自动播放间隔（毫秒） */
  interval?: number;
  /** 鼠标悬停时是否暂停 */
  pause?: 'hover' | false;
  /** 自定义CSS类名 */
  className?: string;
}

/**
 * 轮播数据服务接口
 * 用于从API获取轮播数据
 */
export interface CarouselService {
  /** 获取轮播项目 */
  getCarouselItems(): Promise<CarouselItem[]>;
  /** 获取特定ID的轮播项目 */
  getCarouselItemById(id: number): Promise<CarouselItem | null>;
}
