/**
 * 轮播项目接口
 */
export interface CarouselItem {
  /** 唯一标识符 */
  id: string;
  /** WebP格式图片URL */
  webp: string;
  /** 备用图片URL */
  fallback: string;
  /** 图片alt文本 */
  alt: string;
  /** 图片加载策略 */
  loading?: 'lazy' | 'eager';
  /** 按钮文本 */
  buttonText?: string;
  /** 按钮链接 */
  buttonLink?: string;
}

/**
 * 轮播组件属性接口
 */
export interface CarouselProps {
  /** 轮播项目数组 */
  items: CarouselItem[];
  /** 是否显示控制按钮 */
  controls?: boolean;
  /** 是否显示指示器 */
  indicators?: boolean;
  /** 自动播放间隔（毫秒） */
  interval?: number;
  /** 暂停条件 */
  pause?: boolean | 'hover';
  /** 自定义CSS类名 */
  className?: string;
}

/**
 * 轮播服务接口
 */
export interface CarouselService {
  /** 获取轮播项目 */
  getCarouselItems(): Promise<CarouselItem[]>;
}
