.header-carousel {
    --caption-animation-duration: 0.6s;
}

.header-carousel .carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 8px;
    border: 2px solid rgba(255,255,255,0.5);
    background: transparent;
    transition: all 0.3s ease;
}

.header-carousel .carousel-indicators .active {
    background: #fff;
    transform: scale(1.2);
}

.animated-caption {
    bottom: 25%;
    animation-delay: 0.5s;
}

.animated-caption h3 {
    font-size: 2.5rem;
    text-shadow: 2px 2px 8px rgba(0,0,0,0.5);
    margin-bottom: 1.5rem;
}

.animated-caption p {
    font-size: 1.25rem;
    max-width: 600px;
    margin: 0 auto;
    background: rgba(0,0,0,0.6);
    padding: 0.75rem 1.5rem;
    border-radius: 30px;
}