/* Ant Design 轮播图容器 */
.ant-carousel-container {
  width: 100%;
  height: 500px;
  max-height: 500px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* 轮播图幻灯片 */
.carousel-slide {
  position: relative;
  height: 500px;
  max-height: 500px;
  overflow: hidden;
}

/* 幻灯片内容 */
.slide-content {
  position: relative;
  height: 100%;
  width: 100%;
}

/* 幻灯片图片 */
.slide-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* 幻灯片标题和描述 */
.slide-caption {
  position: absolute;
  bottom: 20%;
  left: 0;
  right: 0;
  text-align: center;
  color: white;
  padding: 0 20px;
  z-index: 2;
}

.slide-caption h3 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 8px rgba(0,0,0,0.5);
}

.slide-caption p {
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto 1.5rem;
  background: rgba(0,0,0,0.6);
  padding: 0.75rem 1.5rem;
  border-radius: 30px;
  display: inline-block;
}

.slide-button {
  margin-top: 1.5rem;
}

/* 自定义 Ant Design 轮播图样式 */
.ant-carousel .slick-dots {
  bottom: 20px;
}

.ant-carousel .slick-dots li button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
}

.ant-carousel .slick-dots li.slick-active button {
  background: white;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .ant-carousel-container,
  .carousel-slide {
    height: 400px;
    max-height: 400px;
  }
  
  .slide-caption h3 {
    font-size: 1.8rem;
  }
  
  .slide-caption p {
    font-size: 1rem;
    max-width: 90%;
  }
}

@media (max-width: 768px) {
  .ant-carousel-container,
  .carousel-slide {
    height: 350px;
    max-height: 350px;
  }
  
  .slide-caption h3 {
    font-size: 1.5rem;
  }
}