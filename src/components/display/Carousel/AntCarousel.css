/* Ant Design 轮播组件样式 */
.ant-carousel-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.carousel-slide {
  position: relative;
  width: 100%;
  height: 100vh;
}

.slide-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slide-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.slide-caption {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 2;
  color: white;
}

.slide-button {
  margin-top: 2rem;
}

.slide-button .ant-btn {
  font-size: 16px;
  height: auto;
  padding: 12px 30px;
  border-radius: 25px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

.slide-button .ant-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 轮播指示器样式 */
.ant-carousel .slick-dots {
  bottom: 30px;
}

.ant-carousel .slick-dots li button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.ant-carousel .slick-dots li.slick-active button {
  background: #1890ff;
  border-color: white;
}

/* 轮播箭头样式 */
.ant-carousel .slick-arrow {
  width: 50px;
  height: 50px;
  z-index: 3;
}

.ant-carousel .slick-arrow:before {
  font-size: 24px;
  color: white;
}

.ant-carousel .slick-prev {
  left: 30px;
}

.ant-carousel .slick-next {
  right: 30px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-carousel-container {
    height: 70vh;
  }
  
  .carousel-slide {
    height: 70vh;
  }
  
  .slide-button .ant-btn {
    font-size: 14px;
    padding: 10px 20px;
  }
  
  .ant-carousel .slick-dots {
    bottom: 20px;
  }
  
  .ant-carousel .slick-arrow {
    width: 40px;
    height: 40px;
  }
  
  .ant-carousel .slick-prev {
    left: 15px;
  }
  
  .ant-carousel .slick-next {
    right: 15px;
  }
}

/* 加载状态 */
.ant-carousel-container .spinner-border {
  width: 3rem;
  height: 3rem;
}

/* 错误状态 */
.ant-carousel-container .alert {
  margin: 2rem;
  text-align: center;
}
