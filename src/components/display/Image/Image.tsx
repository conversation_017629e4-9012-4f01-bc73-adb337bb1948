import React, { useState, useEffect } from 'react';
import { Thumbnail } from './Thumbnail';
import { ImageModal } from './Modal';
import { useImageZoom, useKeyboardControl } from '../../../modules/features/hooks';
import { ImageProps } from './types';
import './image.css';

/**
 * 图片组件
 * 显示缩略图，点击后显示大图模态框，支持键盘控制和图片缩放
 *
 * @param props - 图片组件属性
 * @returns React组件
 */
export const Image: React.FC<ImageProps> = ({ title, largeImage, smallImage }) => {
    // 模态框显示状态
    const [showModal, setShowModal] = useState<boolean>(false);

    // 使用图片缩放钩子
    const { scale, toggleZoom } = useImageZoom();

    // 图片预加载
    useEffect(() => {
        if (showModal) {
            const img = new window.Image();
            img.src = largeImage;
        }
    }, [showModal, largeImage]);

    // 键盘控制
    useKeyboardControl((e) => {
        if (e.key === "Escape" && showModal) {
            setShowModal(false);
            toggleZoom();
        }
    }, [showModal, toggleZoom]);

    // 关闭模态框
    const handleClose = (): void => {
        setShowModal(false);
        toggleZoom();
    };

    return (
        <>
            <Thumbnail
                title={title}
                imageSrc={smallImage}
                onClick={() => setShowModal(true)}
            />

            <ImageModal
                show={showModal}
                title={title}
                imageSrc={largeImage}
                scale={scale}
                onClose={handleClose}
                onZoom={toggleZoom}
            />
        </>
    );
};
