import React from 'react';
import { ImageModalProps } from './types';
import './image.css';

/**
 * 图片模态框组件
 * 显示大图并提供缩放功能
 * 
 * @param props - 图片模态框组件属性
 * @returns React组件
 */
export const ImageModal: React.FC<ImageModalProps> = ({
    show,
    title,
    imageSrc,
    scale,
    onClose,
    onZoom
}) => (
    <>
        <div
            className={`modal fade ${show ? "show" : ""}`}
            style={{ display: show ? "block" : "none" }}
            role="dialog"
            aria-modal="true"
            aria-labelledby="imageModalLabel"
        >
            <div className="modal-dialog modal-dialog-centered modal-xl">
                <div className="modal-content border-0">
                    <div className="modal-header border-0 pb-0">
                        <h5
                            id="imageModalLabel"
                            className="modal-title text-white bg-dark bg-opacity-75 px-3 py-2 rounded-pill"
                        >
                            {title}
                        </h5>
                        <button
                            type="button"
                            className="btn-close btn-close-white"
                            onClick={onClose}
                            aria-label="关闭"
                        ></button>
                    </div>
                    <div className="modal-body text-center p-0">
                        <img
                            src={imageSrc}
                            className="img-fluid rounded-3"
                            alt={title}
                            loading="eager"
                            style={{
                                transform: `scale(${scale})`,
                                transformOrigin: "center center",
                                transition: "transform 0.3s ease",
                                cursor: scale > 1 ? "zoom-out" : "zoom-in"
                            }}
                            onClick={onZoom}
                            role="button"
                            aria-label={scale === 1 ? "双击放大图片" : "双击缩小图片"}
                        />
                        <div className="text-white mt-2">
                            <small>双击图片{scale === 1 ? "放大" : "缩小"}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {show && (
            <div
                className="modal-backdrop fade show"
                onClick={onClose}
            ></div>
        )}
    </>
);
