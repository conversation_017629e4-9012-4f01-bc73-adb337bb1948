.static-hero-container {
    width: 100%;
    height: 500px;
    max-height: 500px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.hero-content {
    position: relative;
    height: 100%;
    width: 100%;
}

.hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.hero-caption {
    position: absolute;
    bottom: 20%;
    left: 0;
    right: 0;
    text-align: center;
    color: white;
    padding: 0 20px;
    z-index: 2;
}

.hero-caption h3 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
}

.hero-caption p {
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto 1.5rem;
    background: rgba(0, 0, 0, 0.6);
    padding: 0.75rem 1.5rem;
    border-radius: 30px;
    display: inline-block;
}

.hero-button {
    margin-top: 1.5rem;
}

@media (max-width: 992px) {
    .static-hero-container {
        height: 400px;
        max-height: 400px;
    }

    .hero-caption h3 {
        font-size: 1.8rem;
    }

    .hero-caption p {
        font-size: 1rem;
        max-width: 90%;
    }
}

@media (max-width: 768px) {
    .static-hero-container {
        height: 350px;
        max-height: 350px;
    }

    .hero-caption h3 {
        font-size: 1.5rem;
    }
}