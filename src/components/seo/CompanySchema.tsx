import React from 'react';
import { Helmet } from 'react-helmet-async';

interface CompanySchemaProps {
  name?: string;
  url?: string;
  logo?: string;
  telephone?: string;
  email?: string;
  address?: {
    streetAddress: string;
    addressLocality: string;
    addressRegion: string;
    postalCode: string;
    addressCountry: string;
  };
}

/**
 * 公司结构化数据组件
 * 添加符合Schema.org标准的公司信息，提升搜索引擎展示效果
 * 
 * @param props - 公司结构化数据属性
 * @returns React组件
 */
export const CompanySchema: React.FC<CompanySchemaProps> = ({
  name = '上海寒链实业有限公司',
  url = 'https://www.hanchain.com',
  logo = 'https://www.hanchain.com/img/logo.png',
  telephone = '+86-13761182299',
  email = '<EMAIL>',
  address = {
    streetAddress: '上海市宝山区长江西路2311号1-2层',
    addressLocality: '上海',
    addressRegion: '上海市',
    postalCode: '200000',
    addressCountry: 'CN'
  }
}) => {
  const schemaData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name,
    url,
    logo,
    contactPoint: {
      '@type': 'ContactPoint',
      telephone,
      contactType: 'customer service',
      email
    },
    address: {
      '@type': 'PostalAddress',
      streetAddress: address.streetAddress,
      addressLocality: address.addressLocality,
      addressRegion: address.addressRegion,
      postalCode: address.postalCode,
      addressCountry: address.addressCountry
    }
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(schemaData)}
      </script>
    </Helmet>
  );
};

export default CompanySchema;
