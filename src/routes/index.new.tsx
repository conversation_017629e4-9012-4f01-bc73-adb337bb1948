import React, { Suspense } from 'react';
import { Routes, Route } from 'react-router-dom';
import { LoadingSpinner } from '../components/feedback/LoadingSpinner';
import { 
    MainLayout, 
    ProductLayout, 
    CaseLayout, 
    ContactLayout, 
    TeamLayoutNew 
} from '@/layouts';
import { lazyLoad } from '../utils/lazyLoad';

// 懒加载页面组件
const ProductPage = lazyLoad(() => import('../features/product'));
const ProductDetail = lazyLoad(() => import('../features/product/components/ProductDetail'));
const Features = lazyLoad(() => import('../features/feature').then(module => ({ default: module.Features })));
const About = lazyLoad(() => import('../features/about/about').then(module => ({ default: module.About })));
const Services = lazyLoad(() => import('../features/services').then(module => ({ default: module.Services })));
const Gallery = lazyLoad(() => import('../features/gallery').then(module => ({ default: module.Gallery })));
const Testimonials = lazyLoad(() => import('../features/testimonials').then(module => ({ default: module.Testimonials })));
const Team = lazyLoad(() => import('../features/team').then(module => ({ default: module.Team })));
const CaseShowcasePage = lazyLoad(() => import('../pages/CaseShowcasePage'));
const CaseDetailPage = lazyLoad(() => import('../pages/CaseDetailPage'));
const ContactPage = lazyLoad(() => import('../pages/ContactPage'));

export const AppRoutesNew = ({ data }: { data: Record<string, any> }) => {
    return (
        <Suspense fallback={<LoadingSpinner />}>
            <Routes>
                {/* 主布局路由 - 用于首页和其他需要完整布局的页面 */}
                <Route path="/" element={<MainLayout data={data} />}>
                    <Route index element={
                        <>
                            <Gallery data={data.Gallery} />
                            <Features data={data.Features} />
                            <About data={data.About} />
                            <Services data={data.Services} />
                        </>
                    } />
                    <Route path="about" element={<About data={data.About} />} />
                    <Route path="services" element={<Services data={data.Services} />} />
                    <Route path="gallery" element={<Gallery data={data.Gallery} />} />
                    <Route path="testimonials" element={<Testimonials data={data.Testimonials} />} />
                </Route>

                {/* 团队页面使用专用布局 */}
                <Route path="/team" element={<TeamLayoutNew />}>
                    <Route index element={<Team data={data.Team} />} />
                </Route>

                {/* 产品页面使用专用布局 */}
                <Route path="/product" element={<ProductLayout />}>
                    <Route index element={<ProductPage />} />
                    <Route path=":id" element={<ProductDetail />} />
                </Route>

                {/* 案例展示页面使用专用布局 */}
                <Route path="/cases" element={<CaseLayout />}>
                    <Route index element={<CaseShowcasePage />} />
                    <Route path=":id" element={<CaseDetailPage />} />
                </Route>

                {/* 联系页面使用专用布局 */}
                <Route path="/contact" element={<ContactLayout />}>
                    <Route index element={<ContactPage />} />
                </Route>
            </Routes>
        </Suspense>
    );
};

// 使用自定义配置的示例
export const AppRoutesWithCustomConfig = ({ data }: { data: Record<string, any> }) => {
    return (
        <Suspense fallback={<LoadingSpinner />}>
            <Routes>
                {/* 使用自定义配置的产品页面 */}
                <Route path="/product" element={
                    <ProductLayout 
                        config={{
                            contactFloatBarConfig: {
                                themeColor: "#ff6b35", // 自定义橙色主题
                                top: "40%"
                            },
                            footerConfig: {
                                recordNumbers: ["自定义备案号1", "自定义备案号2"]
                            }
                        }}
                    />
                }>
                    <Route index element={<ProductPage />} />
                    <Route path=":id" element={<ProductDetail />} />
                </Route>

                {/* 使用最小化配置的特殊页面 */}
                <Route path="/special" element={
                    <ProductLayout 
                        config={{
                            showContactFloatBar: false,
                            showFooter: false,
                            mainClassName: "flex-grow-1 mt-3"
                        }}
                    />
                }>
                    <Route index element={<div>特殊页面内容</div>} />
                </Route>
            </Routes>
        </Suspense>
    );
};
