const fs = require('fs');
const path = require('path');
const glob = require('glob');
const chalk = require('chalk');

// 查找所有 JS/TS 文件
const files = glob.sync('src/**/*.{js,jsx,ts,tsx}');

console.log(chalk.blue(`找到 ${files.length} 个文件需要检查`));

// 需要替换的导入路径
const importPatterns = [
  {
    from: /import\s+apiClient\s+from\s+['"]@\/core\/api\/apiClient['"]/g,
    to: 'import apiClient from \'@/services/api/apiClient\''
  },
  {
    from: /import\s+apiClient\s+from\s+['"]\.\.\/\.\.\/core\/api\/apiClient['"]/g,
    to: 'import apiClient from \'@/services/api/apiClient\''
  },
  {
    from: /import\s+apiClient\s+from\s+['"]\.\.\/core\/api\/apiClient['"]/g,
    to: 'import apiClient from \'@/services/api/apiClient\''
  },
  {
    from: /import\s+apiClient\s+from\s+['"]\.\/core\/api\/apiClient['"]/g,
    to: 'import apiClient from \'@/services/api/apiClient\''
  }
];

// 处理每个文件
let updatedFiles = 0;

files.forEach(file => {
  const content = fs.readFileSync(file, 'utf8');
  let newContent = content;
  let fileUpdated = false;
  
  // 检查并替换导入路径
  importPatterns.forEach(pattern => {
    if (pattern.from.test(newContent)) {
      newContent = newContent.replace(pattern.from, pattern.to);
      fileUpdated = true;
    }
  });
  
  // 如果文件有更新，保存更改
  if (fileUpdated) {
    fs.writeFileSync(file, newContent, 'utf8');
    console.log(chalk.green(`✓ 已更新 ${file}`));
    updatedFiles++;
  }
});

console.log(chalk.green.bold(`\n更新完成! 共更新 ${updatedFiles} 个文件`));