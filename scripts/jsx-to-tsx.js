const fs = require('fs');
const path = require('path');
const glob = require('glob');
const chalk = require('chalk');

// 查找所有 JSX 文件
const jsxFiles = glob.sync('src/**/*.jsx');

console.log(chalk.blue(`找到 ${jsxFiles.length} 个 JSX 文件需要迁移`));

// 基本类型映射
const basicPropTypes = {
  'PropTypes.string': 'string',
  'PropTypes.number': 'number',
  'PropTypes.bool': 'boolean',
  'PropTypes.func': 'Function',
  'PropTypes.object': 'object',
  'PropTypes.array': 'any[]',
  'PropTypes.node': 'React.ReactNode',
  'PropTypes.element': 'React.ReactElement',
  'PropTypes.any': 'any',
};

// 处理每个文件
jsxFiles.forEach(file => {
  const content = fs.readFileSync(file, 'utf8');
  const newFilePath = file.replace('.jsx', '.tsx');
  
  console.log(chalk.yellow(`处理文件: ${file}`));
  
  // 1. 添加 React 导入 (如果没有)
  let newContent = content;
  if (!content.includes('import React')) {
    newContent = `import React from 'react';\n${newContent}`;
  }
  
  // 2. 查找 PropTypes 定义
  const propTypesRegex = /([A-Za-z0-9_]+)\.propTypes\s*=\s*{([^}]*)}/g;
  const propTypesMatches = [...newContent.matchAll(propTypesRegex)];
  
  if (propTypesMatches.length > 0) {
    // 有 PropTypes 定义，需要转换为 TypeScript 接口
    propTypesMatches.forEach(match => {
      const componentName = match[1];
      const propTypesBlock = match[2];
      
      // 解析 PropTypes
      const propsLines = propTypesBlock.split('\n')
        .map(line => line.trim())
        .filter(line => line && !line.startsWith('//'));
      
      // 创建 TypeScript 接口
      let interfaceContent = `interface ${componentName}Props {\n`;
      
      propsLines.forEach(line => {
        // 处理每一行 PropType
        const propMatch = line.match(/([A-Za-z0-9_]+):\s*([^,]*),?/);
        if (propMatch) {
          const propName = propMatch[1];
          let propType = propMatch[2].trim();
          let isRequired = false;
          let tsType = 'any';
          
          // 检查是否必需
          if (propType.includes('.isRequired')) {
            isRequired = true;
            propType = propType.replace('.isRequired', '');
          }
          
          // 映射基本类型
          Object.entries(basicPropTypes).forEach(([ptType, tsTypeValue]) => {
            if (propType.includes(ptType)) {
              tsType = tsTypeValue;
            }
          });
          
          // 处理数组类型
          if (propType.includes('PropTypes.arrayOf')) {
            const arrayTypeMatch = propType.match(/arrayOf\(([^)]*)\)/);
            if (arrayTypeMatch) {
              const arrayType = arrayTypeMatch[1];
              if (basicPropTypes[arrayType]) {
                tsType = `${basicPropTypes[arrayType]}[]`;
              } else {
                tsType = 'any[]';
              }
            }
          }
          
          // 处理对象类型
          if (propType.includes('PropTypes.shape')) {
            tsType = 'any'; // 复杂对象需要手动处理
          }
          
          // 处理枚举类型
          if (propType.includes('PropTypes.oneOf')) {
            const enumMatch = propType.match(/oneOf\(\[([^\]]*)\]\)/);
            if (enumMatch) {
              const enumValues = enumMatch[1].split(',').map(v => v.trim());
              tsType = enumValues.join(' | ');
            }
          }
          
          // 处理联合类型
          if (propType.includes('PropTypes.oneOfType')) {
            tsType = 'any'; // 联合类型需要手动处理
          }
          
          // 添加到接口
          interfaceContent += `  ${propName}${isRequired ? '' : '?'}: ${tsType};\n`;
        }
      });
      
      interfaceContent += '}\n\n';
      
      // 替换 PropTypes 定义
      const propTypesDefinition = match[0];
      newContent = newContent.replace(propTypesDefinition, '');
      
      // 在组件定义前添加接口
      const componentRegex = new RegExp(`(function|const|class)\\s+${componentName}`);
      const componentMatch = newContent.match(componentRegex);
      
      if (componentMatch) {
        const index = componentMatch.index;
        newContent = newContent.slice(0, index) + interfaceContent + newContent.slice(index);
      } else {
        // 如果找不到组件定义，就在文件顶部添加接口
        newContent = interfaceContent + newContent;
      }
      
      // 更新函数组件定义
      if (newContent.includes(`function ${componentName}(`)) {
        newContent = newContent.replace(
          `function ${componentName}(`,
          `function ${componentName}(props: ${componentName}Props`
        );
      } else if (newContent.includes(`const ${componentName} = (`)) {
        newContent = newContent.replace(
          `const ${componentName} = (`,
          `const ${componentName}: React.FC<${componentName}Props> = (`
        );
      } else if (newContent.includes(`class ${componentName} extends React.Component`)) {
        newContent = newContent.replace(
          `class ${componentName} extends React.Component`,
          `class ${componentName} extends React.Component<${componentName}Props>`
        );
      }
    });
  } else {
    // 没有 PropTypes 定义，添加基本类型
    // 查找组件定义
    const functionComponentRegex = /function\s+([A-Za-z0-9_]+)\s*\(\s*(\{[^}]*\}|\w+)\s*\)/g;
    const arrowComponentRegex = /const\s+([A-Za-z0-9_]+)\s*=\s*\(\s*(\{[^}]*\}|\w+)\s*\)\s*=>/g;
    const classComponentRegex = /class\s+([A-Za-z0-9_]+)\s+extends\s+React\.Component/g;
    
    // 处理函数组件
    let match;
    while ((match = functionComponentRegex.exec(newContent)) !== null) {
      const componentName = match[1];
      const props = match[2];
      
      // 添加接口
      const interfaceContent = `interface ${componentName}Props {}\n\n`;
      const fullMatch = match[0];
      const replacement = `${interfaceContent}function ${componentName}(props: ${componentName}Props)`;
      
      newContent = newContent.replace(fullMatch, replacement);
    }
    
    // 处理箭头函数组件
    while ((match = arrowComponentRegex.exec(newContent)) !== null) {
      const componentName = match[1];
      const props = match[2];
      
      // 添加接口
      const interfaceContent = `interface ${componentName}Props {}\n\n`;
      const fullMatch = match[0];
      const replacement = `${interfaceContent}const ${componentName}: React.FC<${componentName}Props> = (${props})`;
      
      newContent = newContent.replace(fullMatch, replacement);
    }
    
    // 处理类组件
    while ((match = classComponentRegex.exec(newContent)) !== null) {
      const componentName = match[1];
      
      // 添加接口
      const interfaceContent = `interface ${componentName}Props {}\ninterface ${componentName}State {}\n\n`;
      const fullMatch = match[0];
      const replacement = `${interfaceContent}class ${componentName} extends React.Component<${componentName}Props, ${componentName}State>`;
      
      newContent = newContent.replace(fullMatch, replacement);
    }
  }
  
  // 3. 移除 PropTypes 导入
  newContent = newContent.replace(/import PropTypes from ['"]prop-types['"];?\n?/, '');
  
  // 4. 保存为 .tsx 文件
  fs.writeFileSync(newFilePath, newContent, 'utf8');
  console.log(chalk.green(`✓ 已创建 ${newFilePath}`));
  
  // 5. 备份原始 JSX 文件
  const backupDir = path.join(path.dirname(file), 'jsx-backup');
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  const backupFile = path.join(backupDir, path.basename(file));
  fs.writeFileSync(backupFile, content, 'utf8');
  console.log(chalk.blue(`✓ 已备份原始文件到 ${backupFile}`));
  
  // 可选：删除原始 JSX 文件
  // fs.unlinkSync(file);
  // console.log(chalk.red(`✓ 已删除原始文件 ${file}`));
});

console.log(chalk.green.bold(`\n迁移完成! 共处理 ${jsxFiles.length} 个文件`));
console.log(chalk.yellow('注意: 请手动检查生成的 TypeScript 类型，特别是复杂类型和联合类型'));