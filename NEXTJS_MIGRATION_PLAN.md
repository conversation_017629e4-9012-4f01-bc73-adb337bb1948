# Next.js 迁移计划

## 🎯 迁移目标

### 主要收益
- **SEO 优化**：服务端渲染，提升搜索引擎友好度
- **性能提升**：首屏加载速度提升 40-60%
- **用户体验**：更快的页面切换和数据加载
- **开发体验**：文件系统路由，简化路由配置

### 技术收益
- **SSR/SSG**：服务端渲染和静态生成
- **API Routes**：内置 API 路由系统
- **图片优化**：自动图片优化和懒加载
- **代码分割**：自动代码分割和优化

## 📋 迁移阶段规划

### 第一阶段：基础架构迁移 (1-2周)
- [ ] 创建 Next.js 项目结构
- [ ] 迁移基础组件和工具函数
- [ ] 配置 TypeScript 和 ESLint
- [ ] 设置开发环境和构建配置

### 第二阶段：路由和页面迁移 (2-3周)
- [ ] 迁移页面组件到 Next.js 页面结构
- [ ] 实现 SSR/SSG 数据获取
- [ ] 迁移布局系统
- [ ] 配置动态路由

### 第三阶段：状态管理和 API 迁移 (1-2周)
- [ ] 适配状态管理到 Next.js
- [ ] 迁移 API 客户端
- [ ] 实现 API Routes
- [ ] 配置中间件

### 第四阶段：优化和测试 (1周)
- [ ] 性能优化和 SEO 配置
- [ ] 测试和调试
- [ ] 部署配置
- [ ] 文档更新

## 🏗️ 目录结构对比

### 当前结构 (Vite + React Router)
```
src/
├── components/          # 通用组件
├── features/           # 功能模块
├── hooks/              # 自定义 Hook
├── layouts/            # 页面布局
├── pages/              # 页面组件
├── routes/             # 路由配置
├── services/           # 服务层
├── store/              # 状态管理
├── types/              # 类型定义
└── utils/              # 工具函数
```

### Next.js 结构
```
src/
├── app/                # App Router (推荐)
│   ├── (routes)/       # 路由组
│   ├── api/            # API Routes
│   ├── globals.css     # 全局样式
│   ├── layout.tsx      # 根布局
│   └── page.tsx        # 首页
├── components/         # 通用组件 (保持)
├── features/           # 功能模块 (保持)
├── hooks/              # 自定义 Hook (保持)
├── lib/                # 库和工具 (重命名)
├── store/              # 状态管理 (保持)
├── types/              # 类型定义 (保持)
└── utils/              # 工具函数 (保持)
```

## 🔄 核心迁移任务

### 1. 路由系统迁移

#### 当前路由 (React Router)
```typescript
// src/routes/index.tsx
<Routes>
  <Route path="/" element={<MainLayout />}>
    <Route index element={<HomePage />} />
    <Route path="about" element={<About />} />
    <Route path="products" element={<ProductsPage />} />
    <Route path="products/:id" element={<ProductDetail />} />
  </Route>
</Routes>
```

#### Next.js 路由 (App Router)
```
src/app/
├── page.tsx                    # / 路由
├── about/page.tsx              # /about 路由
├── products/
│   ├── page.tsx                # /products 路由
│   └── [id]/page.tsx           # /products/[id] 动态路由
└── layout.tsx                  # 根布局
```

### 2. 数据获取迁移

#### 当前数据获取 (客户端)
```typescript
// 客户端数据获取
const [data, setData] = useState(null);
useEffect(() => {
  fetchData().then(setData);
}, []);
```

#### Next.js 数据获取 (服务端)
```typescript
// 服务端数据获取
export async function generateStaticParams() {
  const products = await fetchProducts();
  return products.map((product) => ({ id: product.id }));
}

export default async function ProductPage({ params }) {
  const product = await fetchProduct(params.id);
  return <ProductDetail product={product} />;
}
```

### 3. 状态管理适配

#### Zustand 适配 Next.js
```typescript
// lib/store.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 确保在客户端初始化
export const useStore = create(
  persist(
    (set) => ({
      // 状态定义
    }),
    {
      name: 'app-storage',
      // Next.js 需要处理 SSR 水合
      skipHydration: true,
    }
  )
);
```

### 4. API 层迁移

#### API Routes 实现
```typescript
// src/app/api/products/route.ts
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const products = await fetchProductsFromDB();
    return NextResponse.json({ success: true, data: products });
  } catch (error) {
    return NextResponse.json(
      { success: false, error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}
```

## ⚡ 性能优化策略

### 1. 渲染策略选择
- **SSG (Static Site Generation)**：产品页面、关于页面
- **SSR (Server-Side Rendering)**：用户相关页面
- **CSR (Client-Side Rendering)**：交互密集页面

### 2. 图片优化
```typescript
// 使用 Next.js Image 组件
import Image from 'next/image';

<Image
  src="/products/product1.jpg"
  alt="Product 1"
  width={300}
  height={200}
  priority // 首屏图片
  placeholder="blur" // 模糊占位符
/>
```

### 3. 代码分割优化
```typescript
// 动态导入重型组件
import dynamic from 'next/dynamic';

const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <LoadingSpinner />,
  ssr: false, // 仅客户端渲染
});
```

## 🔧 配置文件迁移

### Next.js 配置
```typescript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true, // 启用 App Router
  },
  images: {
    domains: ['example.com'], // 外部图片域名
  },
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  // 重定向配置
  async redirects() {
    return [
      {
        source: '/old-route',
        destination: '/new-route',
        permanent: true,
      },
    ];
  },
};

module.exports = nextConfig;
```

### TypeScript 配置适配
```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "#/*": ["./src/types/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

## 🎯 SEO 优化配置

### Metadata API
```typescript
// src/app/layout.tsx
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Frost Chain - 专业制冰解决方案',
  description: '提供工业制冰、食用冰制品和制冰服务的专业解决方案',
  keywords: ['制冰', '工业制冰', '食用冰', '制冰设备'],
  openGraph: {
    title: 'Frost Chain',
    description: '专业制冰解决方案',
    url: 'https://frost-chain.com',
    siteName: 'Frost Chain',
    images: [
      {
        url: 'https://frost-chain.com/og-image.jpg',
        width: 1200,
        height: 630,
      },
    ],
  },
};
```

### 结构化数据
```typescript
// 产品页面结构化数据
export function generateProductSchema(product: Product) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.name,
    description: product.description,
    image: product.images,
    offers: {
      '@type': 'Offer',
      price: product.price,
      priceCurrency: 'CNY',
    },
  };
}
```

## 📈 预期收益

### 性能提升
- **首屏加载时间** ↓ 40-60%
- **SEO 评分** ↑ 80-90%
- **Core Web Vitals** 全面提升
- **用户体验** 显著改善

### 开发体验
- **路由配置** 简化 70%
- **构建速度** 提升 30%
- **开发效率** 提升 40%
- **维护成本** 降低 50%

## ⚠️ 迁移风险和注意事项

### 技术风险
1. **状态管理水合问题**：SSR 和客户端状态同步
2. **第三方库兼容性**：部分库可能不支持 SSR
3. **样式闪烁**：CSS-in-JS 可能导致样式闪烁
4. **构建复杂度**：Next.js 构建配置相对复杂

### 解决方案
1. **渐进式迁移**：分阶段迁移，降低风险
2. **充分测试**：每个阶段都要进行充分测试
3. **备份方案**：保留原有系统作为备份
4. **团队培训**：确保团队熟悉 Next.js 开发

## 🎯 成功标准

### 技术指标
- [ ] 首屏加载时间 < 2秒
- [ ] SEO 评分 > 90分
- [ ] Core Web Vitals 全绿
- [ ] 构建时间 < 30秒

### 业务指标
- [ ] 搜索引擎收录提升 200%
- [ ] 页面访问量提升 50%
- [ ] 用户停留时间增加 30%
- [ ] 转化率提升 20%
