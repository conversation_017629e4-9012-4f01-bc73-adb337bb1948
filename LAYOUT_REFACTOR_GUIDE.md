# TeamLayout 重构指南

## 🎯 重构目标

将硬编码的 TeamLayout 重构为可配置的独立组件，解决以下问题：
- 4个页面共享同一个布局，修改时相互影响
- 配置硬编码，无法灵活定制
- 缺乏可复用性和可维护性

## 🏗️ 新架构设计

### 1. 基础布局组件 (BaseLayout)
```typescript
// 核心可配置布局组件
<BaseLayout config={layoutConfig}>
  <YourPageContent />
</BaseLayout>
```

### 2. 预设布局组件
```typescript
// 针对不同页面类型的预设布局
<ProductLayout />      // 产品页面布局
<CaseLayout />         // 案例页面布局  
<ContactLayout />      // 联系页面布局
<TeamLayoutNew />      // 团队页面布局
<StandardLayout />     // 标准页面布局
```

### 3. 配置系统
```typescript
interface BaseLayoutConfig {
  showNavigation?: boolean;
  showFooter?: boolean;
  showContactFloatBar?: boolean;
  showErrorBoundary?: boolean;
  autoScrollToTop?: boolean;
  mainClassName?: string;
  containerClassName?: string;
  footerConfig?: { recordNumbers?: string[] };
  contactFloatBarConfig?: { 
    themeColor?: string; 
    top?: string; 
  };
}
```

## 🔄 迁移步骤

### 步骤 1: 替换路由配置

**原来的方式：**
```typescript
// 所有页面都使用相同的 TeamLayout
<Route path="/team" element={<TeamLayout />}>
<Route path="/product" element={<TeamLayout />}>
<Route path="/cases" element={<TeamLayout />}>
<Route path="/contact" element={<TeamLayout />}>
```

**新的方式：**
```typescript
// 每个页面使用专门的布局组件
<Route path="/team" element={<TeamLayoutNew />}>
<Route path="/product" element={<ProductLayout />}>
<Route path="/cases" element={<CaseLayout />}>
<Route path="/contact" element={<ContactLayout />}>
```

### 步骤 2: 自定义配置

**基础使用：**
```typescript
// 使用默认配置
<ProductLayout />
```

**自定义配置：**
```typescript
// 自定义特定配置
<ProductLayout 
  config={{
    contactFloatBarConfig: {
      themeColor: "#ff6b35",
      top: "40%"
    },
    footerConfig: {
      recordNumbers: ["自定义备案号"]
    }
  }}
/>
```

**完全自定义：**
```typescript
// 使用基础组件完全自定义
<BaseLayout 
  config={{
    showContactFloatBar: false,
    showFooter: false,
    mainClassName: "custom-main-class"
  }}
>
  <YourCustomContent />
</BaseLayout>
```

## 📊 配置对比

| 页面类型 | 联系浮动条主题色 | 浮动条位置 | 特殊配置 |
|---------|----------------|------------|----------|
| 团队页面 | #1677ff (蓝色) | 50% | 标准配置 |
| 产品页面 | #52c41a (绿色) | 45% | 标准配置 |
| 案例页面 | #722ed1 (紫色) | 55% | 标准配置 |
| 联系页面 | - | - | 不显示浮动条 |

## 🎨 使用示例

### 1. 简单替换
```typescript
// 原来
import { TeamLayout } from '@/layouts';

// 现在
import { ProductLayout } from '@/layouts';
```

### 2. 带配置的使用
```typescript
import { ProductLayout } from '@/layouts';

<ProductLayout 
  config={{
    contactFloatBarConfig: {
      themeColor: "#custom-color",
      top: "60%"
    }
  }}
/>
```

### 3. 动态配置
```typescript
const getLayoutConfig = (pageType: string) => {
  switch (pageType) {
    case 'product':
      return { contactFloatBarConfig: { themeColor: "#52c41a" } };
    case 'case':
      return { contactFloatBarConfig: { themeColor: "#722ed1" } };
    default:
      return {};
  }
};

<BaseLayout config={getLayoutConfig(currentPageType)} />
```

## ✅ 迁移检查清单

- [ ] 更新路由配置，使用新的布局组件
- [ ] 测试每个页面的布局显示
- [ ] 验证联系浮动条的主题色和位置
- [ ] 确认页脚配置正确
- [ ] 测试响应式布局
- [ ] 验证错误边界功能
- [ ] 检查自动滚动到顶部功能

## 🚀 优势

### 1. 灵活性
- 每个页面可以独立配置
- 支持运行时动态配置
- 易于扩展新的配置选项

### 2. 可维护性
- 配置集中管理
- 组件职责单一
- 易于测试和调试

### 3. 可复用性
- 基础组件可用于任何页面
- 预设组件覆盖常见场景
- 配置可以复用和组合

### 4. 向后兼容
- 保留原有的 TeamLayout
- 渐进式迁移
- 不影响现有功能

## 📝 注意事项

1. **CSS 样式**：确保新布局组件的样式与原来一致
2. **类型安全**：使用 TypeScript 确保配置的类型安全
3. **性能**：避免不必要的重新渲染
4. **测试**：为新的布局组件添加单元测试

## 🔮 未来扩展

- 支持主题切换
- 添加布局动画
- 支持更多自定义组件
- 添加布局预览功能
