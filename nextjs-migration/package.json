{"name": "frost-chain-nextjs", "version": "2.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "analyze": "cross-env ANALYZE=true next build", "export": "next export"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@next/font": "^14.0.0", "antd": "^5.12.0", "@ant-design/icons": "^5.2.0", "@ant-design/nextjs-registry": "^1.0.0", "zustand": "^4.4.0", "@tanstack/react-query": "^5.0.0", "immer": "^10.0.0", "axios": "^1.6.0", "sonner": "^1.2.0", "framer-motion": "^10.16.0", "react-intersection-observer": "^9.5.0", "sharp": "^0.32.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0", "cross-env": "^7.0.0", "@next/bundle-analyzer": "^14.0.0", "husky": "^8.0.0", "lint-staged": "^15.0.0", "@commitlint/cli": "^18.0.0", "@commitlint/config-conventional": "^18.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{css,scss,md}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}}