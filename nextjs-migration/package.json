{"name": "nextjs-migration", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "next": "^14.0.0", "antd": "^5.12.0", "@ant-design/icons": "^5.2.0", "@ant-design/nextjs-registry": "^1.0.0", "zustand": "^4.4.0", "immer": "^10.0.0", "axios": "^1.6.0", "react-helmet-async": "^2.0.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.3", "@eslint/eslintrc": "^3"}}