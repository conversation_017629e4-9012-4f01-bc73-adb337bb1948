/** @type {import('next').NextConfig} */
const nextConfig = {
  // 启用实验性功能
  experimental: {
    appDir: true, // 启用 App Router
    serverComponentsExternalPackages: ['@ant-design/icons'],
  },

  // TypeScript 配置
  typescript: {
    // 在生产构建时忽略 TypeScript 错误（可选）
    ignoreBuildErrors: false,
  },

  // ESLint 配置
  eslint: {
    // 在生产构建时忽略 ESLint 错误（可选）
    ignoreDuringBuilds: false,
  },

  // 图片优化配置
  images: {
    domains: [
      'localhost',
      'example.com',
      'cdn.example.com',
    ],
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // 环境变量
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
    API_BASE_URL: process.env.API_BASE_URL,
  },

  // 公共运行时配置
  publicRuntimeConfig: {
    // 在客户端和服务端都可用
    staticFolder: '/static',
  },

  // 服务端运行时配置
  serverRuntimeConfig: {
    // 仅在服务端可用
    PROJECT_ROOT: __dirname,
  },

  // 重定向配置
  async redirects() {
    return [
      {
        source: '/old-home',
        destination: '/',
        permanent: true,
      },
      {
        source: '/old-about',
        destination: '/about',
        permanent: true,
      },
    ];
  },

  // 重写配置
  async rewrites() {
    return [
      {
        source: '/api/v1/:path*',
        destination: `${process.env.API_BASE_URL}/api/v1/:path*`,
      },
    ];
  },

  // 头部配置
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
        ],
      },
    ];
  },

  // Webpack 配置
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // 自定义 webpack 配置
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });

    // 分析包大小
    if (process.env.ANALYZE === 'true') {
      const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
      config.plugins.push(
        new BundleAnalyzerPlugin({
          analyzerMode: 'static',
          openAnalyzer: false,
        })
      );
    }

    return config;
  },

  // 输出配置
  output: 'standalone', // 用于 Docker 部署

  // 压缩配置
  compress: true,

  // 电源配置（实验性）
  poweredByHeader: false,

  // 生成 sitemap
  async generateBuildId() {
    // 可以返回任何字符串作为构建 ID
    return 'frost-chain-build-' + Date.now();
  },

  // 国际化配置（如果需要）
  // i18n: {
  //   locales: ['zh-CN', 'en-US'],
  //   defaultLocale: 'zh-CN',
  // },

  // 性能配置
  onDemandEntries: {
    // 页面在内存中保留的时间（毫秒）
    maxInactiveAge: 25 * 1000,
    // 同时保留的页面数
    pagesBufferLength: 2,
  },

  // 静态优化配置
  staticPageGenerationTimeout: 60,

  // 开发服务器配置
  devIndicators: {
    buildActivity: true,
    buildActivityPosition: 'bottom-right',
  },
};

// 如果是开发环境，添加额外配置
if (process.env.NODE_ENV === 'development') {
  nextConfig.reactStrictMode = true;
}

module.exports = nextConfig;
