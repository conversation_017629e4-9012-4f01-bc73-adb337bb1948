import { Metadata } from 'next';

// 页面元数据
export const metadata: Metadata = {
  title: '联系我们',
  description: '联系上海寒链实业有限公司，获取更多关于我们产品和服务的信息。',
};

// 联系信息数据
const contactInfo = {
  address: "上海市宝山区长江西路2311号1-2层",
  phone: "13761182299",
  email: "<EMAIL>",
  wechatImages: [
    { title: "微信客服1", description: "扫码添加微信" },
    { title: "微信客服2", description: "扫码添加微信" }
  ]
};

export default function ContactPage() {
  return (
    <div className="min-h-screen">
      {/* 页面头部 */}
      <div className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">联系我们</h1>
            <p className="text-lg text-gray-600">
              我们期待您的来信
            </p>
          </div>
        </div>
      </div>

      {/* 公司地址地图区域 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h2 className="text-3xl font-bold text-center mb-8">公司地址</h2>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              {/* 地址信息 */}
              <div>
                <div className="space-y-6">
                  <div className="flex items-start">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4 mt-1">
                      <span className="text-blue-600">📍</span>
                    </div>
                    <div>
                      <h5 className="font-semibold text-lg mb-1">公司地址</h5>
                      <p className="text-gray-600">{contactInfo.address}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4 mt-1">
                      <span className="text-blue-600">📞</span>
                    </div>
                    <div>
                      <h5 className="font-semibold text-lg mb-1">联系电话</h5>
                      <p className="text-gray-600">{contactInfo.phone}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4 mt-1">
                      <span className="text-blue-600">✉️</span>
                    </div>
                    <div>
                      <h5 className="font-semibold text-lg mb-1">邮箱地址</h5>
                      <p className="text-gray-600">{contactInfo.email}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* 地图占位符 */}
              <div className="w-full h-80 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <div className="text-4xl mb-4">🗺️</div>
                  <div className="text-lg font-medium">公司位置地图</div>
                  <div className="text-sm mt-2">上海市宝山区长江西路2311号</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 联系表单和微信二维码 */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* 联系表单 */}
            <div>
              <h2 className="text-3xl font-bold mb-6">发送消息</h2>
              <p className="text-gray-600 mb-8">
                如果您对我们的产品和服务有任何疑问，请随时联系我们。我们的团队将很乐意为您提供帮助。
              </p>
              
              <form className="space-y-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    姓名 *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入您的姓名"
                  />
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    邮箱 *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入您的邮箱"
                  />
                </div>
                
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                    留言内容 *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    rows={6}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入您的留言内容"
                  ></textarea>
                </div>
                
                <button
                  type="submit"
                  className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  发送消息
                </button>
              </form>
            </div>

            {/* 微信二维码区域 */}
            <div>
              <h2 className="text-3xl font-bold mb-6">联系我们</h2>
              <p className="text-gray-600 mb-8">添加微信</p>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                {contactInfo.wechatImages.map((wechat, index) => (
                  <div key={index} className="text-center p-6 bg-gray-50 rounded-lg">
                    <div className="w-40 h-40 bg-gradient-to-br from-green-100 to-green-200 rounded-lg mx-auto mb-4 flex items-center justify-center">
                      <div className="text-center text-green-600">
                        <div className="text-4xl mb-2">📱</div>
                        <div className="text-sm font-medium">微信二维码</div>
                      </div>
                    </div>
                    <h4 className="font-semibold text-lg mb-2">{wechat.title}</h4>
                    <p className="text-gray-600 text-sm">{wechat.description}</p>
                  </div>
                ))}
              </div>
              
              {/* 社交链接 */}
              <div className="mt-8">
                <h3 className="text-xl font-semibold mb-4">关注我们</h3>
                <div className="flex space-x-4">
                  <a href="#" className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 hover:bg-blue-200 transition-colors">
                    <span className="text-lg">📱</span>
                  </a>
                  <a href="#" className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center text-red-600 hover:bg-red-200 transition-colors">
                    <span className="text-lg">📺</span>
                  </a>
                  <a href="#" className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 hover:bg-blue-200 transition-colors">
                    <span className="text-lg">🐦</span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 营业时间和其他信息 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-8">营业时间</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h4 className="font-semibold text-lg mb-3">工作日</h4>
                <p className="text-gray-600">周一至周五</p>
                <p className="text-gray-600">9:00 - 18:00</p>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h4 className="font-semibold text-lg mb-3">周末</h4>
                <p className="text-gray-600">周六至周日</p>
                <p className="text-gray-600">10:00 - 17:00</p>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h4 className="font-semibold text-lg mb-3">节假日</h4>
                <p className="text-gray-600">法定节假日</p>
                <p className="text-gray-600">请提前联系</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
