import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';

// 模拟产品数据
const products = [
  {
    id: '1',
    name: '工业制冰机 IIM-500',
    category: 'industrial',
    description: '高效工业制冰设备，日产冰量500公斤',
    price: 25000,
    image: '/images/products/industrial-ice-machine-500.jpg',
    features: ['高效制冰', '节能环保', '自动清洁', '智能控制'],
    specifications: {
      dailyOutput: '500kg',
      powerConsumption: '3.5kW',
      dimensions: '120x80x150cm',
      weight: '180kg',
    },
    inStock: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
  },
  {
    id: '2',
    name: '食用冰块 Premium',
    category: 'edible',
    description: '高品质食用冰块，适用于餐饮和饮品',
    price: 5,
    image: '/images/products/edible-ice-premium.jpg',
    features: ['食品级安全', '透明清澈', '快速冷却', '无异味'],
    specifications: {
      size: '2x2x2cm',
      purity: '99.9%',
      packaging: '5kg/袋',
      shelfLife: '30天',
    },
    inStock: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-10T00:00:00Z',
  },
  {
    id: '3',
    name: '制冰设备维护服务',
    category: 'service',
    description: '专业制冰设备维护和保养服务',
    price: 800,
    image: '/images/products/maintenance-service.jpg',
    features: ['定期检查', '专业维护', '故障排除', '零件更换'],
    specifications: {
      serviceType: '上门服务',
      duration: '2-4小时',
      warranty: '6个月',
      coverage: '全国范围',
    },
    inStock: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-20T00:00:00Z',
  },
];

// GET /api/products - 获取产品列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');

    // 过滤产品
    let filteredProducts = products;

    if (category && category !== 'all') {
      filteredProducts = products.filter(product => product.category === category);
    }

    if (search) {
      const searchLower = search.toLowerCase();
      filteredProducts = filteredProducts.filter(product =>
        product.name.toLowerCase().includes(searchLower) ||
        product.description.toLowerCase().includes(searchLower)
      );
    }

    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

    // 构建响应
    const response = {
      success: true,
      data: paginatedProducts,
      pagination: {
        current: page,
        pageSize: limit,
        total: filteredProducts.length,
        totalPages: Math.ceil(filteredProducts.length / limit),
      },
      message: '获取产品列表成功',
      timestamp: Date.now(),
    };

    // 设置缓存头
    const responseHeaders = new Headers();
    responseHeaders.set('Cache-Control', 'public, s-maxage=300, stale-while-revalidate=600');
    responseHeaders.set('Content-Type', 'application/json');

    return NextResponse.json(response, {
      status: 200,
      headers: responseHeaders,
    });
  } catch (error) {
    console.error('获取产品列表失败:', error);

    return NextResponse.json(
      {
        success: false,
        error: '获取产品列表失败',
        message: error instanceof Error ? error.message : '未知错误',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

// POST /api/products - 创建新产品
export async function POST(request: NextRequest) {
  try {
    // 检查认证
    const headersList = headers();
    const authorization = headersList.get('authorization');
    
    if (!authorization || !authorization.startsWith('Bearer ')) {
      return NextResponse.json(
        {
          success: false,
          error: '未授权访问',
          message: '请提供有效的认证令牌',
          timestamp: Date.now(),
        },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // 验证必需字段
    const requiredFields = ['name', 'category', 'description', 'price'];
    const missingFields = requiredFields.filter(field => !body[field]);
    
    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: '缺少必需字段',
          message: `缺少字段: ${missingFields.join(', ')}`,
          timestamp: Date.now(),
        },
        { status: 400 }
      );
    }

    // 创建新产品
    const newProduct = {
      id: (products.length + 1).toString(),
      ...body,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // 模拟保存到数据库
    products.push(newProduct);

    return NextResponse.json(
      {
        success: true,
        data: newProduct,
        message: '产品创建成功',
        timestamp: Date.now(),
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('创建产品失败:', error);

    return NextResponse.json(
      {
        success: false,
        error: '创建产品失败',
        message: error instanceof Error ? error.message : '未知错误',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

// OPTIONS /api/products - 处理 CORS 预检请求
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
