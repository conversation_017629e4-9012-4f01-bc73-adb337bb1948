import { Metadata } from 'next';
import Link from 'next/link';
import { Suspense } from 'react';

// 页面元数据
export const metadata: Metadata = {
  title: '产品信息中心',
  description: '上海寒链实业有限公司产品信息中心，提供工业冰、食用冰、降温冰等多种冰产品和相关服务。',
};

// 产品数据
const products = [
  {
    id: 1,
    title: '工业冰块',
    description: '专为工业应用设计的高质量冰块，适用于建筑、混凝土养护、食品加工等领域。具有更长的融化时间和更高的冷却效率。',
    category: 'industrial',
    features: ['高冷却效率', '长时间保持低温', '定制尺寸', '大批量供应'],
    applications: ['建筑工地', '混凝土养护', '食品加工', '工业冷却'],
    specifications: {
      '尺寸': '可定制',
      '重量': '5-50kg/块',
      '融化时间': '8-12小时',
      '纯度': '99.5%'
    }
  },
  {
    id: 2,
    title: '食用冰块',
    description: '符合食品安全标准的高品质食用冰，适用于餐饮、酒店、超市等场所。采用纯净水制作，确保安全卫生。',
    category: 'food',
    features: ['食品级安全标准', '晶莹剔透', '多种形状可选', '快速配送服务'],
    applications: ['餐厅酒吧', '酒店宾馆', '超市零售', '饮品制作'],
    specifications: {
      '尺寸': '2x2x2cm 标准',
      '重量': '1-10kg/袋',
      '保质期': '24小时',
      '安全标准': 'GB 2760-2014'
    }
  },
  {
    id: 3,
    title: '降温冰块',
    description: '专为环境降温设计的特殊冰块，适用于户外活动、工地降温、仓库温控等场景。融化速度适中，持续释放冷量。',
    category: 'cooling',
    features: ['高效降温', '环保无污染', '适用多种场景', '成本效益高'],
    applications: ['户外活动', '工地降温', '仓库温控', '应急降温'],
    specifications: {
      '尺寸': '10x10x5cm',
      '重量': '2-20kg/块',
      '降温效果': '可降低5-10°C',
      '持续时间': '4-6小时'
    }
  },
  {
    id: 4,
    title: '干冰产品',
    description: '高纯度干冰产品，适用于冷链运输、舞台效果、清洁应用等特殊需求。安全环保，使用方便。',
    category: 'industrial',
    features: ['超低温冷却', '无残留物', '环保安全', '多种规格'],
    applications: ['冷链运输', '舞台效果', '清洁应用', '科研实验'],
    specifications: {
      '温度': '-78.5°C',
      '纯度': '99.9%',
      '形状': '颗粒/块状',
      '包装': '保温箱装'
    }
  },
  {
    id: 5,
    title: '冰块配送服务',
    description: '专业的冰块配送服务，满足各类商业和活动需求，保证及时送达和产品质量。全天候配送，定时送达。',
    category: 'service',
    features: ['全天候配送', '定时送达', '批量订购优惠', '专业冷藏运输'],
    applications: ['餐饮企业', '酒店度假村', '活动庆典', '零售商店'],
    specifications: {
      '配送范围': '上海市区及周边',
      '最小订单': '10kg',
      '配送时间': '8:00-22:00',
      '紧急配送': '2小时内'
    }
  }
];

// 产品分类
const categories = [
  { id: 'all', name: '全部产品', icon: '📦' },
  { id: 'industrial', name: '工业冰产品', icon: '🏭' },
  { id: 'food', name: '食用冰产品', icon: '🧊' },
  { id: 'cooling', name: '降温冰产品', icon: '❄️' },
  { id: 'service', name: '冰产品服务', icon: '🚚' }
];

// 产品卡片组件
function ProductCard({ product }: { product: typeof products[0] }) {
  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
      {/* 产品图片占位符 */}
      <div className="h-48 bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center">
        <div className="text-center text-blue-600">
          <div className="text-4xl mb-2">
            {product.category === 'industrial' && '🏭'}
            {product.category === 'food' && '🧊'}
            {product.category === 'cooling' && '❄️'}
            {product.category === 'service' && '🚚'}
          </div>
          <div className="text-lg font-medium">{product.title}</div>
        </div>
      </div>
      
      <div className="p-6">
        <h3 className="text-xl font-semibold mb-3">{product.title}</h3>
        <p className="text-gray-600 mb-4 line-clamp-3">
          {product.description}
        </p>
        
        <div className="mb-4">
          <h4 className="font-medium mb-2">产品特点：</h4>
          <div className="flex flex-wrap gap-1">
            {product.features.slice(0, 3).map((feature, index) => (
              <span 
                key={index}
                className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded"
              >
                {feature}
              </span>
            ))}
          </div>
        </div>
        
        <Link 
          href={`/products/${product.id}`}
          className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium"
        >
          查看详情
          <span className="ml-1">→</span>
        </Link>
      </div>
    </div>
  );
}

// 产品列表组件
function ProductList({ category }: { category: string }) {
  const filteredProducts = category === 'all' 
    ? products 
    : products.filter(product => product.category === category);

  if (filteredProducts.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">📦</div>
        <h3 className="text-xl font-semibold mb-2">暂无相关产品</h3>
        <p className="text-gray-600">该分类下暂时没有产品，请查看其他分类</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {filteredProducts.map(product => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  );
}

// 分类标签组件
function CategoryTabs({ activeCategory, onCategoryChange }: { 
  activeCategory: string; 
  onCategoryChange: (category: string) => void;
}) {
  return (
    <div className="flex flex-wrap justify-center gap-2 mb-8">
      {categories.map(category => (
        <button
          key={category.id}
          onClick={() => onCategoryChange(category.id)}
          className={`px-6 py-3 rounded-lg font-medium transition-colors ${
            activeCategory === category.id
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          <span className="mr-2">{category.icon}</span>
          {category.name}
          <span className="ml-2 text-sm">
            ({category.id === 'all' ? products.length : products.filter(p => p.category === category.id).length})
          </span>
        </button>
      ))}
    </div>
  );
}

// 客户端组件包装器
function ProductsClient() {
  // 这里我们使用 URL 参数来管理状态，但为了简化，先使用默认值
  const activeCategory = 'all';
  
  const handleCategoryChange = (category: string) => {
    // 在实际应用中，这里会更新 URL 参数
    console.log('切换到分类:', category);
  };

  return (
    <>
      <CategoryTabs 
        activeCategory={activeCategory} 
        onCategoryChange={handleCategoryChange}
      />
      <ProductList category={activeCategory} />
    </>
  );
}

export default function ProductsPage() {
  return (
    <div className="min-h-screen">
      {/* 页面头部 */}
      <div className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">产品信息中心</h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              上海寒链实业有限公司提供多种高品质冰产品，满足不同行业和场景的需求
            </p>
            <div className="w-20 h-1 bg-blue-600 mx-auto mt-6"></div>
          </div>
        </div>
      </div>

      {/* 产品内容区域 */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <Suspense fallback={
            <div className="text-center py-12">
              <div className="text-2xl">加载中...</div>
            </div>
          }>
            <ProductsClient />
          </Suspense>
        </div>
      </section>

      {/* 联系我们区域 */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">需要定制产品？</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto">
            我们提供个性化的产品定制服务，满足您的特殊需求
          </p>
          <Link 
            href="/contact"
            className="inline-block bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            联系我们定制
          </Link>
        </div>
      </section>
    </div>
  );
}
