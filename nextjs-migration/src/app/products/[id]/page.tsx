import { Metadata } from 'next';
import Link from 'next/link';
import { notFound } from 'next/navigation';

// 产品数据（与产品列表页面保持一致）
const products = [
  {
    id: 1,
    title: '工业冰块',
    description: '专为工业应用设计的高质量冰块，适用于建筑、混凝土养护、食品加工等领域。具有更长的融化时间和更高的冷却效率，帮助您降低成本并提高生产效率。',
    category: 'industrial',
    features: ['高冷却效率', '长时间保持低温', '定制尺寸', '大批量供应', '环保无污染', '成本效益高'],
    applications: ['建筑工地混凝土养护', '食品加工冷却', '工业设备降温', '化工过程冷却', '医药储存运输'],
    specifications: {
      '尺寸': '可定制 (标准: 20x20x10cm)',
      '重量': '5-50kg/块',
      '融化时间': '8-12小时 (常温下)',
      '纯度': '99.5%',
      '密度': '0.92g/cm³',
      '包装': '保温箱装'
    }
  },
  {
    id: 2,
    title: '食用冰块',
    description: '符合食品安全标准的高品质食用冰，适用于餐饮、酒店、超市等场所。采用纯净水制作，确保安全卫生，为您的饮品和食品提供完美的冷却解决方案。',
    category: 'food',
    features: ['食品级安全标准', '晶莹剔透', '多种形状可选', '快速配送服务', '无异味无杂质', '符合卫生标准'],
    applications: ['餐厅酒吧饮品制作', '酒店宾馆服务', '超市零售销售', '咖啡店茶饮店', '宴会活动服务'],
    specifications: {
      '尺寸': '2x2x2cm (标准立方体)',
      '重量': '1-10kg/袋',
      '保质期': '24小时 (冷冻保存)',
      '安全标准': 'GB 2760-2014',
      '水质': '纯净水制作',
      '包装': '食品级包装袋'
    }
  },
  {
    id: 3,
    title: '降温冰块',
    description: '专为环境降温设计的特殊冰块，适用于户外活动、工地降温、仓库温控等场景。融化速度适中，能够持续释放冷量，有效改善高温环境。',
    category: 'cooling',
    features: ['高效降温', '环保无污染', '适用多种场景', '成本效益高', '持续冷却', '易于运输'],
    applications: ['户外活动降温', '建筑工地环境改善', '仓库温度控制', '应急降温措施', '体育赛事降温'],
    specifications: {
      '尺寸': '10x10x5cm',
      '重量': '2-20kg/块',
      '降温效果': '可降低环境温度5-10°C',
      '持续时间': '4-6小时',
      '覆盖面积': '10-20平方米',
      '包装': '防水包装'
    }
  },
  {
    id: 4,
    title: '干冰产品',
    description: '高纯度干冰产品，适用于冷链运输、舞台效果、清洁应用等特殊需求。安全环保，使用方便，是各种特殊应用场景的理想选择。',
    category: 'industrial',
    features: ['超低温冷却', '无残留物', '环保安全', '多种规格', '升华无液体', '使用便捷'],
    applications: ['冷链运输保鲜', '舞台烟雾效果', '精密清洁应用', '科研实验冷却', '医疗器械运输'],
    specifications: {
      '温度': '-78.5°C',
      '纯度': '99.9%',
      '形状': '颗粒状/块状可选',
      '包装': '专用保温箱',
      '升华速度': '5-10kg/24小时',
      '安全等级': '工业级'
    }
  },
  {
    id: 5,
    title: '冰块配送服务',
    description: '专业的冰块配送服务，满足各类商业和活动需求，保证及时送达和产品质量。全天候配送，定时送达，为您提供便捷可靠的冰块供应解决方案。',
    category: 'service',
    features: ['全天候配送', '定时送达', '批量订购优惠', '专业冷藏运输', '灵活配送方案', '质量保证'],
    applications: ['餐饮企业定期供应', '酒店度假村服务', '活动庆典临时需求', '零售商店补货', '医疗机构应急'],
    specifications: {
      '配送范围': '上海市区及周边50公里',
      '最小订单': '10kg起订',
      '配送时间': '8:00-22:00',
      '紧急配送': '2小时内送达',
      '配送车辆': '专业冷藏车',
      '服务热线': '24小时客服'
    }
  }
];

// 根据 ID 获取产品
function getProduct(id: string) {
  return products.find(product => product.id === parseInt(id));
}

// 生成动态元数据
export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  const product = getProduct(params.id);
  
  if (!product) {
    return {
      title: '产品未找到',
      description: '抱歉，您查找的产品不存在。'
    };
  }

  return {
    title: `${product.title} - 产品详情`,
    description: product.description,
    keywords: [product.title, ...product.features, ...product.applications].join(', '),
  };
}

// 生成静态路径
export async function generateStaticParams() {
  return products.map((product) => ({
    id: product.id.toString(),
  }));
}

export default function ProductDetailPage({ params }: { params: { id: string } }) {
  const product = getProduct(params.id);

  if (!product) {
    notFound();
  }

  return (
    <div className="min-h-screen">
      {/* 面包屑导航 */}
      <div className="bg-gray-50 py-4">
        <div className="container mx-auto px-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link href="/" className="text-blue-600 hover:text-blue-800">首页</Link>
            <span className="text-gray-500">/</span>
            <Link href="/products" className="text-blue-600 hover:text-blue-800">产品信息中心</Link>
            <span className="text-gray-500">/</span>
            <span className="text-gray-700">{product.title}</span>
          </nav>
        </div>
      </div>

      {/* 返回按钮 */}
      <div className="container mx-auto px-4 py-4">
        <Link 
          href="/products"
          className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium"
        >
          ← 返回产品列表
        </Link>
      </div>

      {/* 产品详情内容 */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* 产品图片区域 */}
            <div>
              <div className="h-96 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center mb-4">
                <div className="text-center text-blue-600">
                  <div className="text-8xl mb-4">
                    {product.category === 'industrial' && '🏭'}
                    {product.category === 'food' && '🧊'}
                    {product.category === 'cooling' && '❄️'}
                    {product.category === 'service' && '🚚'}
                  </div>
                  <div className="text-2xl font-medium">{product.title}</div>
                </div>
              </div>
              
              {/* 缩略图 */}
              <div className="grid grid-cols-4 gap-2">
                {[1, 2, 3, 4].map(i => (
                  <div key={i} className="h-20 bg-gray-200 rounded cursor-pointer hover:bg-gray-300 transition-colors"></div>
                ))}
              </div>
            </div>

            {/* 产品信息区域 */}
            <div>
              <h1 className="text-3xl font-bold mb-4">{product.title}</h1>
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                {product.description}
              </p>

              {/* 产品特点 */}
              <div className="mb-8">
                <h3 className="text-xl font-semibold mb-4">产品特点</h3>
                <div className="grid grid-cols-2 gap-3">
                  {product.features.map((feature, index) => (
                    <div key={index} className="flex items-center">
                      <span className="text-green-500 mr-2">✓</span>
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* 应用场景 */}
              <div className="mb-8">
                <h3 className="text-xl font-semibold mb-4">应用场景</h3>
                <div className="space-y-2">
                  {product.applications.map((application, index) => (
                    <div key={index} className="flex items-start">
                      <span className="text-blue-500 mr-2 mt-1">•</span>
                      <span>{application}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* 联系按钮 */}
              <div className="space-y-3">
                <Link 
                  href="/contact"
                  className="block w-full bg-blue-600 text-white text-center py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                >
                  立即咨询
                </Link>
                <button className="block w-full border-2 border-blue-600 text-blue-600 text-center py-3 px-6 rounded-lg font-semibold hover:bg-blue-50 transition-colors">
                  获取报价
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 规格参数 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">规格参数</h2>
          
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-0">
                {Object.entries(product.specifications).map(([key, value], index) => (
                  <div 
                    key={key} 
                    className={`p-4 border-b border-gray-200 ${index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}`}
                  >
                    <div className="flex justify-between items-center">
                      <span className="font-medium text-gray-700">{key}</span>
                      <span className="text-gray-900">{value}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 相关产品推荐 */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">相关产品推荐</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {products
              .filter(p => p.id !== product.id && p.category === product.category)
              .slice(0, 3)
              .map(relatedProduct => (
                <Link 
                  key={relatedProduct.id}
                  href={`/products/${relatedProduct.id}`}
                  className="block bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow"
                >
                  <div className="h-48 bg-gradient-to-br from-blue-100 to-blue-200 rounded-t-lg flex items-center justify-center">
                    <div className="text-center text-blue-600">
                      <div className="text-4xl mb-2">
                        {relatedProduct.category === 'industrial' && '🏭'}
                        {relatedProduct.category === 'food' && '🧊'}
                        {relatedProduct.category === 'cooling' && '❄️'}
                        {relatedProduct.category === 'service' && '🚚'}
                      </div>
                      <div className="text-lg font-medium">{relatedProduct.title}</div>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="font-semibold mb-2">{relatedProduct.title}</h3>
                    <p className="text-gray-600 text-sm line-clamp-2">
                      {relatedProduct.description}
                    </p>
                  </div>
                </Link>
              ))}
          </div>
        </div>
      </section>
    </div>
  );
}
