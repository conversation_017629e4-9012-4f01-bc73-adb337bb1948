import { Metadata } from 'next';
import { Typography, Row, Col, Card } from 'antd';
import { CheckCircleOutlined } from '@ant-design/icons';
import Image from 'next/image';

const { Title, Paragraph } = Typography;

// 页面元数据
export const metadata: Metadata = {
  title: '关于我们',
  description: '了解上海寒链实业有限公司的历史、使命和价值观。专业冷链解决方案提供商，为您提供全方位的冷链物流服务。',
  keywords: ['关于我们', '公司历史', '冷链物流', '企业文化', '专业团队'],
};

// 关于页面数据
const aboutData = {
  imageUrl: '/img/about.jpg',
  paragraph: '专业冷链解决方案提供商，为您提供全方位的冷链物流服务。我们致力于为客户提供高质量、高效率的冷链物流解决方案，确保您的产品在运输过程中保持最佳状态。',
  why: [
    "✔️ 纯净品质：每一块冰都经严格质检，晶莹无瑕，融化慢、无异味，为饮品锁住最佳口感",
    "✔️ 丰富选择：食用冰、碎冰、干冰、创意造型冰（可定制图案）一应俱全，满足冷饮制作、食材保鲜、活动装饰等多样需求",
    "✔️ 灵活服务：小到家庭用冰袋，大到企业月供，按需定制，随时调整，拒绝浪费"
  ],
  why2: [
    '✔️智能温控系统，确保产品质量',
    '✔️全网直供，覆盖全国各大城市',
    '✔️专业团队，提供一站式服务'
  ]
};

// 特性列表组件
const FeatureList = ({ items }: { items: string[] }) => (
  <ul className="space-y-2">
    {items.map((item, index) => (
      <li key={index} className="flex items-start">
        <CheckCircleOutlined className="text-blue-500 mt-1 mr-2 flex-shrink-0" />
        <span>{item}</span>
      </li>
    ))}
  </ul>
);

export default function AboutPage() {
  return (
    <div className="min-h-screen">
      {/* 页面头部 */}
      <div className="bg-gray-50 py-16 mt-0">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <Title level={1} className="!text-4xl !font-bold !mb-4">
              关于我们
            </Title>
            <Paragraph className="text-lg text-gray-600">
              了解我们的历史和使命
            </Paragraph>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <Row gutter={[48, 48]} align="middle">
            {/* 图片列 */}
            <Col xs={24} md={12} className="order-2 md:order-1">
              <div className="relative">
                <div className="w-full h-80 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg shadow-lg flex items-center justify-center">
                  <div className="text-center text-blue-600">
                    <div className="text-6xl mb-4">🏭</div>
                    <div className="text-lg font-medium">冷链物流专业团队</div>
                  </div>
                </div>
              </div>
            </Col>

            {/* 内容列 */}
            <Col xs={24} md={12} className="order-1 md:order-2">
              <div className="md:pl-8">
                <Title level={2} className="!mb-6">
                  关于我们
                </Title>

                <Paragraph className="text-lg leading-relaxed mb-6 max-w-2xl">
                  {aboutData.paragraph}
                </Paragraph>

                <Title level={3} className="!mt-8 !mb-6 relative pl-6">
                  <span className="absolute left-0 top-1/2 transform -translate-y-1/2 w-3 h-0.5 bg-blue-500"></span>
                  选择我们的理由
                </Title>

                <Row gutter={[24, 24]}>
                  <Col xs={24} lg={12}>
                    <FeatureList items={aboutData.why} />
                  </Col>
                  <Col xs={24} lg={12}>
                    <FeatureList items={aboutData.why2} />
                  </Col>
                </Row>
              </div>
            </Col>
          </Row>
        </div>
      </section>

      {/* 我们的故事部分 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <Row justify="center">
            <Col xs={24} lg={16}>
              <Title level={2} className="!mb-8 text-center">
                我们的故事
              </Title>

              <div className="space-y-6">
                <Paragraph className="text-lg">
                  上海寒链实业有限公司成立于2010年，是一家专注于冷链物流和食用冰生产的企业。
                  多年来，我们不断创新和发展，已成为行业内的领先企业。
                </Paragraph>

                <Paragraph className="text-lg">
                  我们的团队由行业专家组成，拥有丰富的经验和专业知识，致力于为客户提供最优质的产品和服务。
                </Paragraph>
              </div>
            </Col>
          </Row>
        </div>
      </section>

      {/* 企业价值观 */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <Title level={2} className="!mb-12 text-center">
            我们的价值观
          </Title>

          <Row gutter={[32, 32]}>
            <Col xs={24} md={8}>
              <Card className="text-center h-full" bordered={false}>
                <div className="mb-4">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                    <CheckCircleOutlined className="text-2xl text-blue-500" />
                  </div>
                </div>
                <Title level={4}>品质第一</Title>
                <Paragraph>
                  严格的质量控制体系，确保每一个产品都达到最高标准
                </Paragraph>
              </Card>
            </Col>

            <Col xs={24} md={8}>
              <Card className="text-center h-full" bordered={false}>
                <div className="mb-4">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                    <CheckCircleOutlined className="text-2xl text-green-500" />
                  </div>
                </div>
                <Title level={4}>客户至上</Title>
                <Paragraph>
                  以客户需求为导向，提供个性化的解决方案和优质服务
                </Paragraph>
              </Card>
            </Col>

            <Col xs={24} md={8}>
              <Card className="text-center h-full" bordered={false}>
                <div className="mb-4">
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                    <CheckCircleOutlined className="text-2xl text-purple-500" />
                  </div>
                </div>
                <Title level={4}>持续创新</Title>
                <Paragraph>
                  不断投入研发，采用最新技术，推动行业发展
                </Paragraph>
              </Card>
            </Col>
          </Row>
        </div>
      </section>
    </div>
  );
}
