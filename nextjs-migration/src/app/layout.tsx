import type { Metadata, Viewport } from 'next';
import { Inter } from 'next/font/google';
import { AntdRegistry } from '@ant-design/nextjs-registry';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';

import { Providers } from './providers';
import { Navigation } from '@/components/layout/Navigation';
import { PageFooter } from '@/components/layout/PageFooter';
import { CookieConsent } from '@/features/cookie-consent';
import { PerformanceMonitor } from '@/components/common/PerformanceMonitor';

import './globals.css';

// 字体配置
const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
});

// 元数据配置
export const metadata: Metadata = {
  title: {
    default: 'Frost Chain - 专业制冰解决方案',
    template: '%s | Frost Chain',
  },
  description: '提供工业制冰、食用冰制品和制冰服务的专业解决方案，致力于为客户提供高质量的制冰产品和服务。',
  keywords: [
    '制冰',
    '工业制冰',
    '食用冰',
    '制冰设备',
    '制冰服务',
    '冷链物流',
    '制冰解决方案',
  ],
  authors: [{ name: 'Frost Chain Team' }],
  creator: 'Frost Chain',
  publisher: 'Frost Chain',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://frost-chain.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: '/',
    title: 'Frost Chain - 专业制冰解决方案',
    description: '提供工业制冰、食用冰制品和制冰服务的专业解决方案',
    siteName: 'Frost Chain',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Frost Chain - 专业制冰解决方案',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Frost Chain - 专业制冰解决方案',
    description: '提供工业制冰、食用冰制品和制冰服务的专业解决方案',
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION,
    yandex: process.env.NEXT_PUBLIC_YANDEX_VERIFICATION,
    yahoo: process.env.NEXT_PUBLIC_YAHOO_VERIFICATION,
  },
};

// 视口配置
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#000000' },
  ],
};

// 结构化数据
const structuredData = {
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: 'Frost Chain',
  description: '专业制冰解决方案提供商',
  url: 'https://frost-chain.com',
  logo: 'https://frost-chain.com/logo.png',
  contactPoint: {
    '@type': 'ContactPoint',
    telephone: '+86-************',
    contactType: 'customer service',
    availableLanguage: ['Chinese', 'English'],
  },
  sameAs: [
    'https://weibo.com/frostchain',
    'https://www.linkedin.com/company/frostchain',
  ],
};

interface RootLayoutProps {
  children: React.ReactNode;
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="zh-CN" className={inter.variable}>
      <head>
        {/* 结构化数据 */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />
        
        {/* 预连接到外部域名 */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        
        {/* DNS 预取 */}
        <link rel="dns-prefetch" href="//cdn.example.com" />
        
        {/* 预加载关键资源 */}
        <link rel="preload" href="/fonts/custom-font.woff2" as="font" type="font/woff2" crossOrigin="" />
        
        {/* 网站图标 */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        
        {/* 主题色 */}
        <meta name="theme-color" content="#1890ff" />
        <meta name="msapplication-TileColor" content="#1890ff" />
        
        {/* 百度统计 */}
        {process.env.NEXT_PUBLIC_BAIDU_ANALYTICS_ID && (
          <script
            dangerouslySetInnerHTML={{
              __html: `
                var _hmt = _hmt || [];
                (function() {
                  var hm = document.createElement("script");
                  hm.src = "https://hm.baidu.com/hm.js?${process.env.NEXT_PUBLIC_BAIDU_ANALYTICS_ID}";
                  var s = document.getElementsByTagName("script")[0]; 
                  s.parentNode.insertBefore(hm, s);
                })();
              `,
            }}
          />
        )}
      </head>
      <body className="min-h-screen bg-white text-gray-900 antialiased">
        {/* 性能监控 */}
        <PerformanceMonitor />
        
        {/* Ant Design 注册表 */}
        <AntdRegistry>
          <ConfigProvider
            locale={zhCN}
            theme={{
              token: {
                colorPrimary: '#1890ff',
                borderRadius: 6,
                fontFamily: inter.style.fontFamily,
              },
            }}
          >
            {/* 全局提供者 */}
            <Providers>
              <div className="flex min-h-screen flex-col">
                {/* 跳转到主内容的链接（无障碍） */}
                <a
                  href="#main-content"
                  className="sr-only focus:not-sr-only focus:absolute focus:left-4 focus:top-4 focus:z-50 focus:rounded focus:bg-blue-600 focus:px-4 focus:py-2 focus:text-white focus:outline-none"
                >
                  跳转到主内容
                </a>
                
                {/* 导航栏 */}
                <Navigation />
                
                {/* 主内容区域 */}
                <main id="main-content" className="flex-1">
                  {children}
                </main>
                
                {/* 页脚 */}
                <PageFooter />
                
                {/* Cookie 同意 */}
                <CookieConsent />
              </div>
            </Providers>
          </ConfigProvider>
        </AntdRegistry>
        
        {/* Google Analytics */}
        {process.env.NEXT_PUBLIC_GA_ID && (
          <>
            <script
              async
              src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`}
            />
            <script
              dangerouslySetInnerHTML={{
                __html: `
                  window.dataLayer = window.dataLayer || [];
                  function gtag(){dataLayer.push(arguments);}
                  gtag('js', new Date());
                  gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}', {
                    page_title: document.title,
                    page_location: window.location.href,
                  });
                `,
              }}
            />
          </>
        )}
      </body>
    </html>
  );
}
