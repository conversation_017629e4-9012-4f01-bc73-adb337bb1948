/**
 * Next.js 适配的状态管理
 * 处理 SSR 水合和客户端状态同步
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { devtools } from 'zustand/middleware';

// 用户状态接口
interface UserState {
  user: {
    id?: string;
    name?: string;
    email?: string;
    avatar?: string;
    isAuthenticated: boolean;
    token?: string;
  };
  userActions: {
    setUser: (user: Partial<UserState['user']>) => void;
    clearUser: () => void;
    setToken: (token: string) => void;
    clearToken: () => void;
  };
}

// 设置状态接口
interface SettingsState {
  settings: {
    theme: 'light' | 'dark' | 'system';
    language: 'zh-CN' | 'en-US';
    fontSize: number;
    sidebarCollapsed: boolean;
  };
  settingsActions: {
    setTheme: (theme: SettingsState['settings']['theme']) => void;
    setLanguage: (language: SettingsState['settings']['language']) => void;
    setFontSize: (fontSize: number) => void;
    toggleSidebar: () => void;
    resetSettings: () => void;
  };
}

// 应用状态接口
interface AppState {
  app: {
    isLoading: boolean;
    error: string | null;
    isHydrated: boolean;
  };
  appActions: {
    setLoading: (loading: boolean) => void;
    setError: (error: string | null) => void;
    setHydrated: (hydrated: boolean) => void;
  };
}

// 合并状态类型
type StoreState = UserState & SettingsState & AppState;

// 默认状态
const defaultUserState: UserState['user'] = {
  isAuthenticated: false,
};

const defaultSettingsState: SettingsState['settings'] = {
  theme: 'system',
  language: 'zh-CN',
  fontSize: 14,
  sidebarCollapsed: false,
};

const defaultAppState: AppState['app'] = {
  isLoading: false,
  error: null,
  isHydrated: false,
};

// 创建存储
export const useStore = create<StoreState>()(
  devtools(
    persist(
      immer((set, get) => ({
        // 用户状态
        user: defaultUserState,
        userActions: {
          setUser: (userData) =>
            set((state) => {
              Object.assign(state.user, userData);
            }),
          clearUser: () =>
            set((state) => {
              state.user = { ...defaultUserState };
            }),
          setToken: (token) =>
            set((state) => {
              state.user.token = token;
              state.user.isAuthenticated = true;
            }),
          clearToken: () =>
            set((state) => {
              state.user.token = undefined;
              state.user.isAuthenticated = false;
            }),
        },

        // 设置状态
        settings: defaultSettingsState,
        settingsActions: {
          setTheme: (theme) =>
            set((state) => {
              state.settings.theme = theme;
            }),
          setLanguage: (language) =>
            set((state) => {
              state.settings.language = language;
            }),
          setFontSize: (fontSize) =>
            set((state) => {
              state.settings.fontSize = fontSize;
            }),
          toggleSidebar: () =>
            set((state) => {
              state.settings.sidebarCollapsed = !state.settings.sidebarCollapsed;
            }),
          resetSettings: () =>
            set((state) => {
              state.settings = { ...defaultSettingsState };
            }),
        },

        // 应用状态
        app: defaultAppState,
        appActions: {
          setLoading: (loading) =>
            set((state) => {
              state.app.isLoading = loading;
            }),
          setError: (error) =>
            set((state) => {
              state.app.error = error;
            }),
          setHydrated: (hydrated) =>
            set((state) => {
              state.app.isHydrated = hydrated;
            }),
        },
      })),
      {
        name: 'frost-chain-storage',
        storage: createJSONStorage(() => {
          // 确保在客户端环境中使用 localStorage
          if (typeof window !== 'undefined') {
            return localStorage;
          }
          // 服务端返回一个空的存储对象
          return {
            getItem: () => null,
            setItem: () => {},
            removeItem: () => {},
          };
        }),
        // 只持久化必要的状态
        partialize: (state) => ({
          user: {
            id: state.user.id,
            name: state.user.name,
            email: state.user.email,
            avatar: state.user.avatar,
            isAuthenticated: state.user.isAuthenticated,
            token: state.user.token,
          },
          settings: state.settings,
        }),
        // 处理 SSR 水合
        onRehydrateStorage: () => (state) => {
          if (state) {
            state.appActions.setHydrated(true);
          }
        },
      }
    ),
    {
      name: 'frost-chain-store',
      enabled: process.env.NODE_ENV === 'development',
    }
  )
);

// 便捷的选择器 Hooks
export const useUser = () => useStore((state) => state.user);
export const useUserActions = () => useStore((state) => state.userActions);
export const useSettings = () => useStore((state) => state.settings);
export const useSettingsActions = () => useStore((state) => state.settingsActions);
export const useApp = () => useStore((state) => state.app);
export const useAppActions = () => useStore((state) => state.appActions);

// 组合选择器
export const useAuth = () => {
  const user = useUser();
  const userActions = useUserActions();
  
  return {
    ...user,
    ...userActions,
    isLoggedIn: user.isAuthenticated && !!user.token,
  };
};

export const useTheme = () => {
  const settings = useSettings();
  const settingsActions = useSettingsActions();
  
  return {
    theme: settings.theme,
    isDarkMode: settings.theme === 'dark' ||
      (settings.theme === 'system' && 
       typeof window !== 'undefined' &&
       window.matchMedia('(prefers-color-scheme: dark)').matches),
    setTheme: settingsActions.setTheme,
  };
};

// SSR 安全的 Hook
export const useHydration = () => {
  const isHydrated = useStore((state) => state.app.isHydrated);
  
  return {
    isHydrated,
    // 只在客户端渲染时返回 true
    isClient: typeof window !== 'undefined' && isHydrated,
  };
};

// 初始化存储（仅在客户端）
export const initializeStore = () => {
  if (typeof window !== 'undefined') {
    // 客户端初始化逻辑
    const { appActions } = useStore.getState();
    appActions.setHydrated(true);
  }
};

// 重置存储
export const resetStore = () => {
  const { userActions, settingsActions, appActions } = useStore.getState();
  userActions.clearUser();
  settingsActions.resetSettings();
  appActions.setError(null);
  appActions.setLoading(false);
};

// 导出存储实例（用于在组件外部访问）
export const getStoreState = () => useStore.getState();
export const subscribeToStore = useStore.subscribe;
