#!/bin/bash

echo "=== 项目状态检查 ==="

# 检查必要文件是否存在
echo "检查必要文件..."
files=(
  "Dockerfile"
  "build.sh"
  "vite.config.ts"
  "src/index.tsx"
  "src/App.tsx"
  "package.json"
  "tsconfig.json"
)

for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 存在"
  else
    echo "❌ $file 不存在"
  fi
done

# 检查重复文件是否已删除
echo -e "\n检查重复文件是否已删除..."
duplicate_files=(
  "Dockerfile.build"
  "Dockerfile.nginx"
  "Dockerfile.simple"
  "src/main.tsx"
  "src/components/layout/MainLayout.tsx"
  "vite.config.js"
  "build-and-deploy.sh"
  "build-simple.sh"
)

for file in "${duplicate_files[@]}"; do
  if [ ! -f "$file" ]; then
    echo "✅ $file 已删除"
  else
    echo "❌ $file 仍然存在（应该删除）"
  fi
done

# 检查 TypeScript 编译
echo -e "\n检查 TypeScript 编译..."
if npx tsc --noEmit; then
  echo "✅ TypeScript 编译通过"
else
  echo "❌ TypeScript 编译失败"
fi

# 检查 ESLint
echo -e "\n检查 ESLint..."
if npx eslint src --ext .ts,.tsx --max-warnings 0; then
  echo "✅ ESLint 检查通过"
else
  echo "⚠️ ESLint 检查有警告或错误"
fi

echo -e "\n=== 检查完成 ==="
