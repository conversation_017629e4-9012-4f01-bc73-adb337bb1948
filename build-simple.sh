#!/bin/bash

# 设置错误时退出
set -e

echo "=== 开始简化构建过程 ==="

# 1. 构建 Docker 镜像
echo "=== 步骤 1: 构建 Docker 镜像 ==="
docker build -t frost-chain-frontend:latest -f Dockerfile.simple .

# 2. 推送镜像到 Docker Hub（如果需要）
if [ "$1" == "--push" ]; then
  echo "=== 步骤 2: 推送镜像到 Docker Hub ==="
  docker tag frost-chain-frontend:latest starrier/frostchain:0.0.1
  docker push starrier/frostchain:0.0.1
fi

echo "=== 构建过程完成 ==="
