# 使用 Node.js 镜像
FROM node:20-alpine AS builder

# 设置工作目录
WORKDIR /app

# 增加 Node.js 内存限制
ENV NODE_OPTIONS="--max-old-space-size=4096"

# 复制 package.json 和 yarn.lock
COPY package.json yarn.lock ./

# 安装所有依赖，包括开发依赖
RUN yarn install --network-timeout 300000 --frozen-lockfile --production=false

# 确保安装了必要的开发依赖
RUN yarn add @vitejs/plugin-react path --dev

# 复制源代码
COPY . .

# 显示环境信息
RUN echo "Node version: $(node -v)" && \
    echo "Yarn version: $(yarn -v)" && \
    echo "Environment: production"

# 直接使用 Vite CLI 构建，不依赖配置文件
RUN yarn vite build --outDir=dist --emptyOutDir --minify=terser

# 第二阶段：使用 Nginx 镜像
FROM nginx:alpine

# 删除默认的 Nginx 配置
RUN rm /etc/nginx/conf.d/default.conf

# 复制自定义 Nginx 配置
COPY nginx.conf /etc/nginx/conf.d/

# 创建环境变量注入脚本
RUN echo '#!/bin/sh' > /docker-entrypoint.d/40-inject-env.sh && \
    echo 'envsubst < /usr/share/nginx/html/env-config.template.js > /usr/share/nginx/html/env-config.js' >> /docker-entrypoint.d/40-inject-env.sh && \
    chmod +x /docker-entrypoint.d/40-inject-env.sh

# 从构建阶段复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# 启动 Nginx
CMD ["nginx", "-g", "daemon off;"]
