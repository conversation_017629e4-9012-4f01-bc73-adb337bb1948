{"name": "landingpage-react-template", "private": true, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@iconify/react": "^6.0.0", "@storybook/react": "^8.6.12", "@tailwindcss/vite": "^4.1.4", "@tanstack/react-query": "^5.74.4", "@tanstack/react-query-devtools": "^5.76.1", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "@types/node": "^22.15.2", "@vanilla-extract/css": "^1.17.1", "@vanilla-extract/vite-plugin": "^5.0.1", "antd": "^5.25.1", "apexcharts": "^4.7.0", "axios": "^1.8.4", "clsx": "^2.1.1", "color": "^5.0.0", "dayjs": "^1.11.13", "depcheck": "1.4.7", "emailjs-com": "^2.6.4", "eslint-config-react-app": "^7.0.1", "framer-motion": "^12.9.7", "highlight.js": "^11.11.1", "msw": "^2.8.2", "prop-types": "^15.8.1", "ramda": "^0.30.1", "react": "^18.2.0", "react-apexcharts": "^1.7.0", "react-bootstrap": "^2.10.9", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.5", "react-number-format": "^5.4.4", "react-router": "^7.5.2", "react-router-dom": "^7.5.3", "react-toastify": "^11.0.5", "rollup-plugin-visualizer": "^5.14.0", "smooth-scroll": "^16.1.3", "sonner": "^2.0.3", "styled-components": "^6.1.17", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.5", "uuid": "^11.1.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.3", "zustand": "^5.0.3"}, "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build --mode production", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "start": "PORT=3000 serve -s dist", "docker:build": "docker build -t frost-chain-frontend:latest .", "docker:run": "docker run -d -p 80:80 -e NODE_ENV=production -e API_BASE_URL=/api -e APP_BASE_PATH=/ frost-chain-frontend:latest", "docker:build:local": "docker build -t frost-chain-frontend:local-test --build-arg NODE_ENV=development .", "docker:run:local": "docker run -d -p 8080:80 -e NODE_ENV=development -e API_BASE_URL=http://localhost:3000/api -e APP_BASE_PATH=/ -e USE_MOCK=true --name frost-chain-local-test frost-chain-frontend:local-test", "docker:local:up": "docker-compose -f docker-compose.local.yml up -d", "docker:local:down": "docker-compose -f docker-compose.local.yml down", "docker:local:logs": "docker-compose -f docker-compose.local.yml logs -f"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/ramda": "^0.30.2", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-toastify": "^4.1.0", "@vitejs/plugin-react": "^4.4.1", "less": "^4.3.0", "sass-embedded": "^1.87.0", "terser": "^5.39.2", "typescript": "^5.8.3", "vite": "^6.3.2", "vite-plugin-svgr": "^4.3.0"}, "type": "module"}