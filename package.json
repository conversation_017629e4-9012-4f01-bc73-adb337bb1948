{"name": "frost-chain-nextjs", "version": "2.0.0", "private": true, "dependencies": {"@ant-design/icons": "^5.2.0", "@ant-design/nextjs-registry": "^1.0.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@tanstack/react-query": "^5.74.4", "antd": "^5.25.1", "autoprefixer": "^10.4.21", "axios": "^1.8.4", "clsx": "^2.1.1", "dayjs": "^1.11.13", "emailjs-com": "^2.6.4", "framer-motion": "^12.9.7", "immer": "^10.0.0", "next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.5", "styled-components": "^6.1.17", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "docker:build": "docker build -t frost-chain-nextjs:latest .", "docker:run": "docker run -d -p 80:80 -e NODE_ENV=production frost-chain-nextjs:latest", "docker:build:local": "docker build -t frost-chain-nextjs:local-test --build-arg NODE_ENV=development .", "docker:run:local": "docker run -d -p 8080:80 -e NODE_ENV=development --name frost-chain-nextjs-local-test frost-chain-nextjs:local-test"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.8", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "postcss": "^8", "tailwindcss": "^4", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}