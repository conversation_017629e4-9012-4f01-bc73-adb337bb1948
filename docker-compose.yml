version: '3.8'

services:
  # 构建服务 - 使用独立的 Dockerfile 构建前端代码
  builder:
    build:
      context: .
      dockerfile: Dockerfile.build
    image: frost-chain-builder:latest

  # 前端服务 - 使用 Nginx 提供静态文件
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.nginx
    image: frost-chain-frontend:latest
    ports:
      - "80:80"
    environment:
      # 运行时环境变量，会被注入到 env-config.js
      - NODE_ENV=production
      - API_BASE_URL=/api
      - APP_BASE_PATH=/
