/*
 * Nivo Lightbox Default Theme v1.0
 * http://dev7studios.com/nivo-lightbox
 *
 * Copyright 2013, Dev7studios
 * Free to use and abuse under the MIT license.
 * http://www.opensource.org/licenses/mit-license.php
 */
 
 .nivo-lightbox-theme-default * {
    -webkit-box-sizing: initial !important;
    -moz-box-sizing: initial !important;
    box-sizing: initial !important;
 }
.nivo-lightbox-theme-default.nivo-lightbox-overlay { 
	background: #666;
	background: rgba(0,0,0,0.6); 
}
.nivo-lightbox-theme-default .nivo-lightbox-content.nivo-lightbox-loading { background: url(loading.gif) no-repeat 50% 50%; }

.nivo-lightbox-theme-default .nivo-lightbox-nav {
	top: 10%;
	width: 8%;
	height: 80%;
	text-indent: -9999px;
	background-repeat: no-repeat;
	background-position: 50% 50%;
	opacity: 0.5;
}
.nivo-lightbox-theme-default .nivo-lightbox-nav:hover { 
	opacity: 1; 
	background-color: transparent;
}
.nivo-lightbox-theme-default .nivo-lightbox-prev { 
	background-image: url(prev.png); 
	border-radius: 0 3px 3px 0;
}
.nivo-lightbox-theme-default .nivo-lightbox-next { 
	background-image: url(next.png); 
	border-radius: 3px 0 0 3px;
}

.nivo-lightbox-theme-default .nivo-lightbox-close {
	display: block;
	background: url(close.png) no-repeat 5px 5px;
	width: 16px;
	height: 16px;
	text-indent: -9999px;
	padding: 5px;
	opacity: 0.5;
}
.nivo-lightbox-theme-default .nivo-lightbox-close:hover { opacity: 1; }

.nivo-lightbox-theme-default .nivo-lightbox-title-wrap { bottom: -7%; }
.nivo-lightbox-theme-default .nivo-lightbox-title {
	font: 14px/20px 'Helvetica Neue', Helvetica, Arial, sans-serif;
	font-style: normal;
	font-weight: normal;
	background: #000;
	color: #fff;
	padding: 7px 15px;
	border-radius: 30px;
}

.nivo-lightbox-theme-default .nivo-lightbox-image img {
	background: #fff;
	-webkit-box-shadow: 0px 1px 1px rgba(0,0,0,0.4);
	   -moz-box-shadow: 0px 1px 1px rgba(0,0,0,0.4);
	        box-shadow: 0px 1px 1px rgba(0,0,0,0.4);
}
.nivo-lightbox-theme-default .nivo-lightbox-ajax,
.nivo-lightbox-theme-default .nivo-lightbox-inline {
	background: #fff;
	padding: 40px;
	-webkit-box-shadow: 0px 1px 1px rgba(0,0,0,0.4);
	   -moz-box-shadow: 0px 1px 1px rgba(0,0,0,0.4);
	        box-shadow: 0px 1px 1px rgba(0,0,0,0.4);
}

@media (-webkit-min-device-pixel-ratio: 1.3),
       (-o-min-device-pixel-ratio: 2.6/2),
       (min--moz-device-pixel-ratio: 1.3),
       (min-device-pixel-ratio: 1.3),
       (min-resolution: 1.3dppx) {

	.nivo-lightbox-theme-default .nivo-lightbox-content.nivo-lightbox-loading { 
		background-image: url(<EMAIL>); 
		background-size: 32px 32px;
	}
	.nivo-lightbox-theme-default .nivo-lightbox-prev { 
		background-image: url(<EMAIL>); 
		background-size: 48px 48px;
	}
	.nivo-lightbox-theme-default .nivo-lightbox-next { 
		background-image: url(<EMAIL>); 
		background-size: 48px 48px;
	}
	.nivo-lightbox-theme-default .nivo-lightbox-close { 
		background-image: url(<EMAIL>); 
		background-size: 16px 16px;
	}
	
}