{"name": "nextjs-migration-v2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/nextjs-registry": "^1.0.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "antd": "^5.25.4", "axios": "^1.9.0", "bootstrap": "^5.3.6", "emailjs-com": "^3.2.0", "immer": "^10.1.1", "next": "15.3.3", "react": "^19.0.0", "react-bootstrap": "^2.10.10", "react-dom": "^19.0.0", "react-helmet-async": "^2.0.5", "react-router-dom": "^7.6.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}