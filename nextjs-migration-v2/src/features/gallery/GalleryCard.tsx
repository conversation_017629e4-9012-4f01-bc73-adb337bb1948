import React, { useState } from 'react';
import { Modal } from 'react-bootstrap';
import { GalleryCardProps } from './types';
import './Gallery.css';

/**
 * 图库卡片组件
 * 显示图片缩略图，点击后显示大图模态框
 * 
 * @param props - 图库卡片组件属性
 * @returns React组件
 */
export const GalleryCard: React.FC<GalleryCardProps> = ({ item, index }) => {
    // 模态框显示状态
    const [showModal, setShowModal] = useState<boolean>(false);

    return (
        <>
            {/* 缩略图点击区域 */}
            <div
                className="gallery-item cursor-pointer"
                onClick={() => setShowModal(true)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                    // 添加键盘可访问性
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        setShowModal(true);
                    }
                }}
            >
                <img
                    src={item.smallImage}
                    alt={item.title}
                    className="img-fluid rounded"
                    loading="lazy"
                />
            </div>

            {/* React-Bootstrap 模态框 */}
            <Modal
                show={showModal}
                onHide={() => setShowModal(false)}
                size="lg"
                centered
                className="custom-modal"
            >
                <Modal.Header closeButton className="border-0 pb-0">
                    <Modal.Title className="visually-hidden">
                        {item.title}
                    </Modal.Title>
                </Modal.Header>

                <Modal.Body className="text-center p-4">
                    <img
                        src={item.largeImage}
                        alt={item.title}
                        className="img-fluid rounded-lg"
                        style={{ maxHeight: '80vh' }}
                    />
                </Modal.Body>
            </Modal>
        </>
    );
};
