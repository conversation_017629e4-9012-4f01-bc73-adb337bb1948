/**
 * 图库组件类型定义
 */

/**
 * 图库项目接口
 */
export interface GalleryItem {
  /** 图片标题 */
  title: string;
  /** 大图URL */
  largeImage: string;
  /** 缩略图URL */
  smallImage: string;
  /** 可选的描述 */
  description?: string;
}

/**
 * 图库组件属性接口
 */
export interface GalleryProps {
  /** 图库数据 */
  data?: GalleryItem[];
}

/**
 * 图库卡片属性接口
 */
export interface GalleryCardProps {
  /** 图库项目 */
  item: GalleryItem;
  /** 索引 */
  index: number;
}

/**
 * 图库头部属性接口
 */
export interface GalleryHeaderProps {
  /** 描述文本 */
  description?: string;
}
