/* 图库组件样式 */
.gallery-image {
    transition: transform 0.3s ease !important;
}

.card:hover .gallery-image {
    transform: scale(1.05);
}

/* 容器样式 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* 文本居中 */
.text-center {
    text-align: center;
}

/* 产品卡片样式 */
.product-card {
    border-radius: 8px;
    overflow: hidden;
    height: 100%;
}

/* 产品特性标签 */
.feature-tag {
    display: inline-block;
    background-color: #f0f5ff;
    color: #1890ff;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 12px;
    margin-right: 8px;
    margin-bottom: 8px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .gallery-image {
        height: 200px !important;
    }

    .ant-card-cover img {
        height: 180px !important;
    }

    .ant-card-meta-description {
        height: auto !important;
        -webkit-line-clamp: 3 !important;
    }
}