/* cookie.css */
.cookie-consent-container {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 40px);
    max-width: 720px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    opacity: 1;
}

/* 退出动画状态 */
.cookie-consent-container.exiting {
    transform: translate(-50%, 100%) !important;
    opacity: 0 !important;
}

/* 浏览器降级支持 */
@media (prefers-reduced-motion: reduce) {
    .cookie-consent-container {
        transition: opacity 0.3s linear !important;
    }
}

/* 移除默认模态框样式 */
.cookie-consent-container .modal-content {
    border: 0 !important;
    background: transparent !important;
}

/* 无障碍焦点样式 */
button:focus-visible {
    outline: 3px solid #0066cc;
    outline-offset: 2px;
}