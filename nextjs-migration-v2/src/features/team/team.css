/* 团队页面自定义样式 */
.ant-card.h-100 {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.ant-card.h-100:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
}

.ant-avatar {
  border: 4px solid #f0f0f0;
  transition: transform 0.3s ease;
}

.ant-card:hover .ant-avatar {
  transform: scale(1.05);
}

/* 响应式调整 */
@media (max-width: 767px) {
  .ant-avatar {
    width: 100px !important;
    height: 100px !important;
    line-height: 100px !important;
  }
}