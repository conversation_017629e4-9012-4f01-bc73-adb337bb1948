import React, { useEffect, useState } from 'react';
import { Row, Col, Card, Skeleton, Typography, Avatar, Divider } from 'antd';
import { TeamOutlined } from '@ant-design/icons';
import JsonData from '../../data/data.json';
import { TeamData, TeamMember, TeamProps } from './types';
import './team.css'; // 导入自定义样式

const { Title, Paragraph, Text } = Typography;

export const Team: React.FC<TeamProps> = ({ data }) => {
  const [teamData, setTeamData] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    // 如果传入了数据，直接使用
    if (data) {
      // 处理两种可能的数据格式
      const members = Array.isArray(data) ? data : data.members;
      setTeamData(members || []);
      setLoading(false);
      return;
    }

    // 从 data.json 加载团队数据
    const loadData = async () => {
      try {
        // 确保 Team 数据存在
        if (JsonData.Team && Array.isArray(JsonData.Team)) {
          setTeamData(JsonData.Team);
        }
      } catch (error) {
        console.error("加载团队数据失败:", error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [data]);

  return (
    <section id="team" className="py-5">
      <div className="container">
        <Row justify="center" className="mb-5">
          <Col xs={24} md={16}>
            <div className="text-center">
              <Title level={2}>
                <TeamOutlined className="mr-2" /> 我们的团队
              </Title>
              <Divider />
              <Paragraph className="lead">
                我们的团队由行业专家组成，拥有丰富的冷链物流和食用冰生产经验。每位成员都致力于为客户提供最优质的产品和服务，确保您的每一个需求都能得到满足。
              </Paragraph>
            </div>
          </Col>
        </Row>

        <Row gutter={[24, 24]}>
          {loading ? (
            // 加载状态
            Array(4).fill(null).map((_, index) => (
              <Col xs={24} sm={12} md={8} lg={6} key={`skeleton-${index}`}>
                <Card hoverable loading={true} className="h-100">
                  <Skeleton avatar active />
                </Card>
              </Col>
            ))
          ) : (
            // 团队成员卡片
            teamData?.map((member, index) => (
              <Col xs={24} sm={12} md={8} lg={6} key={`${member.name}-${index}`}>
                <Card 
                  hoverable 
                  className="h-100 text-center"
                  cover={
                    <div className="p-3 pt-4">
                      <Avatar 
                        src={member.img} 
                        size={120}
                        alt={member.name}
                        className="mx-auto d-block"
                      />
                    </div>
                  }
                >
                  <Title level={5} className="mb-1">{member.name}</Title>
                  <Text type="secondary">{member.job}</Text>
                  
                  {member.bio && (
                    <Paragraph className="mt-3 text-center" ellipsis={{ rows: 3 }}>
                      {member.bio}
                    </Paragraph>
                  )}
                </Card>
              </Col>
            ))
          )}
        </Row>
      </div>
    </section>
  );
};

export default Team;
