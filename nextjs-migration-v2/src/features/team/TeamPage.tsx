import React from 'react';
import { Team } from './Team';
import { SEO, CompanySchema, BreadcrumbSchema } from '../../components/seo';
import { TeamProps } from './types';

/**
 * 团队页面组件 - 包含SEO优化
 * 
 * @param props - 团队页面组件属性
 * @returns React组件
 */
export const TeamPage: React.FC<TeamProps> = ({ data }) => {
  return (
    <>
      <SEO
        title="我们的团队"
        description="上海寒链实业有限公司拥有一支专业的团队，致力于为客户提供最优质的冷链解决方案和服务。了解我们团队的专业背景和技术实力。"
        keywords="上海寒链团队,冷链专家,专业团队,冷链技术人员"
        ogType="website"
        ogImage="/img/team/team-og-image.jpg"
        ogImageAlt="上海寒链实业有限公司专业团队"
      />
      <CompanySchema />
      <BreadcrumbSchema 
        items={[
          { name: '首页', url: 'https://www.hanchain.com/' },
          { name: '我们的团队', url: 'https://www.hanchain.com/team' }
        ]}
      />
      <Team data={data} />
    </>
  );
};

export default TeamPage;
