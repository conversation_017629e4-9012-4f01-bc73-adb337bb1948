// src/modules/features/components/TrackerProvider.jsx
import { useEffect } from 'react';
import { trackEvent } from '@/utils/tracker.jsx';

// 全局追踪器 - 不依赖于路由
const TrackerProvider = ({ children }) => {
    // 非路由相关的通用追踪逻辑
    useEffect(() => {
        // 初始化 UV（会话级跟踪）
        trackEvent('session_start');

        // 性能监控
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach(entry => {
                    trackEvent('asset_perf', {
                        name: entry.name,
                        duration: entry.duration.toFixed(2),
                        type: entry.entryType,
                    });
                });
            });
            observer.observe({ entryTypes: ['resource', 'navigation'] });
        }

        // 用户行为采样跟踪（点击事件）
        const clickHandler = (e) => {
            if (Math.random() < 0.1) { // 10% 采样率
                trackEvent('click', {
                    target: e.target.tagName,
                    x: e.clientX,
                    y: e.clientY,
                });
            }
        };

        document.addEventListener('click', clickHandler);
        return () => document.removeEventListener('click', clickHandler);
    }, []);

    return children;
};

export default TrackerProvider;
