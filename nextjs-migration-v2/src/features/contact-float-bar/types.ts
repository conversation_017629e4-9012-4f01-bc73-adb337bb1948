/**
 * 联系悬浮栏组件类型定义
 */

/**
 * 联系方式项目接口
 */
export interface ContactItem {
  /** 唯一标识符 */
  id: string;
  /** 图标类型 */
  icon: string;
  /** 标题 */
  title: string;
  /** 描述文本 */
  description?: string;
  /** 链接地址 */
  link?: string;
  /** 二维码图片URL */
  qrCode?: string;
  /** 点击回调函数 */
  onClick?: () => void;
}

/**
 * 联系悬浮栏组件属性接口
 */
export interface ContactFloatBarProps {
  /** 联系方式项目列表 */
  items: ContactItem[];
  /** 位置，默认为'right' */
  position?: 'left' | 'right';
  /** 距离顶部的位置，单位为px或百分比 */
  top?: number | string;
  /** 距离底部的位置，单位为px或百分比 */
  bottom?: number | string;
  /** 自定义类名 */
  className?: string;
  /** 主题色 */
  themeColor?: string;
}

/**
 * 联系项目组件属性接口
 */
export interface ContactItemProps {
  /** 联系方式项目 */
  item: ContactItem;
  /** 是否展开 */
  expanded: boolean;
  /** 鼠标悬停或点击回调函数 */
  onClick: (id: string) => void;
  /** 当前选中的项目ID */
  activeId: string | null;
  /** 主题色 */
  themeColor?: string;
}
