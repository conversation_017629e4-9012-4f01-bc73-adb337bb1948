/* 联系项目样式 */
.contact-item {
  position: relative;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.contact-button {
  z-index: 10;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  font-size: 18px !important;
}

.contact-button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25);
}

.contact-info {
  position: absolute;
  right: 60px;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  width: 220px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  z-index: 9;
  animation: slideIn 0.25s ease forwards;
  transform-origin: right center;
}

.contact-info::after {
  content: '';
  position: absolute;
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  border-width: 8px 0 8px 8px;
  border-style: solid;
  border-color: transparent transparent transparent #fff;
}

.contact-info-header {
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.contact-info-title {
  font-weight: 500;
  font-size: 16px;
  color: #333;
}

.contact-info-description {
  margin-bottom: 12px;
  color: #666;
  font-size: 14px;
}

.contact-info-qrcode {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 8px;
}

.contact-info-scan-text {
  margin-top: 8px;
  color: #999;
  font-size: 12px;
}

.contact-info-link {
  display: block;
  margin-top: 8px;
  color: #1890ff;
  word-break: break-all;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
