import React from 'react';
import {StaticHero} from '../../components/display/Hero/StaticHero';
import {carouselItems} from '@/components/display/Carousel/carouselData';

interface HeaderProps {
  data: any;
}

export const Header: React.FC<HeaderProps> = ({ data }) => {
  return (
    <header id="header">
      <div className="intro">
        <StaticHero items={carouselItems}/>
        <div className="overlay">
          <div className="container">
            <div className="row">
              <div className="col-md-8 col-md-offset-2 intro-text">
                <h1>
                  {data?.title}
                  <span></span>
                </h1>
                <p>{data?.paragraph}</p>
                <a
                  href="#features"
                  className="btn btn-custom btn-lg page-scroll"
                >
                  了解更多
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};