import React from 'react';
import { NavTogglerProps } from './types';

/**
 * 导航切换按钮组件
 * 显示移动端导航菜单的折叠/展开按钮
 * 
 * @param props - 导航切换按钮组件属性
 * @returns React组件
 */
export const NavToggler: React.FC<NavTogglerProps> = ({ targetId }) => (
    <button
        className="navbar-toggler"
        type="button"
        data-bs-toggle="collapse"
        data-bs-target={`#${targetId}`}
        aria-controls={targetId}
        aria-expanded="false"
        aria-label="Toggle navigation"
    >
        <span className="navbar-toggler-icon"></span>
    </button>
);
