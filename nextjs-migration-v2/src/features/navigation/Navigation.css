/* 导航栏样式修复 */
.navbar {
  border: none !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  padding: 10px 15px;
  margin-right: 0;
  border: none !important;
}

.navbar-nav .nav-link {
  padding: 10px 15px;
  border: none !important;
}

/* 修复导航栏右侧可能的边框问题 */
.navbar-collapse {
  border: none !important;
}

.navbar-toggler {
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* 确保导航栏和内容之间没有意外的间隙 */
.navbar + main {
  margin-top: 56px; /* 导航栏的高度 */
}

@media (max-width: 768px) {
  .navbar-brand {
    padding: 10px;
  }
  
  .navbar-nav .nav-link {
    padding: 8px 10px;
  }
}
