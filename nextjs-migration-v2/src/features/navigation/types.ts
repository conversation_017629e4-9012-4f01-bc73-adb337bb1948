/**
 * 导航组件类型定义
 */

/**
 * 导航项目接口
 */
export interface NavItem {
  /** 链接地址 */
  href: string;
  /** 显示文本 */
  text: string;
  /** 是否为锚点链接 */
  isAnchor: boolean;
}

/**
 * 导航组件属性接口
 */
export interface NavigationProps {
  /** 品牌名称 */
  brandName?: string;
}

/**
 * 导航品牌属性接口
 */
export interface NavBrandProps {
  /** 品牌名称 */
  brandName: string;
  /** 链接地址 */
  href?: string;
}

/**
 * 导航折叠属性接口
 */
export interface NavCollapseProps {
  /** 折叠区域ID */
  id: string;
  /** 子元素 */
  children: React.ReactNode;
}

/**
 * 导航项目属性接口
 */
export interface NavItemProps {
  /** 链接地址 */
  href: string;
  /** 子元素 */
  children: React.ReactNode;
}

/**
 * 导航切换按钮属性接口
 */
export interface NavTogglerProps {
  /** 目标折叠区域ID */
  targetId: string;
}
