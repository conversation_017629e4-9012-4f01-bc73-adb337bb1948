'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import './Navigation.css';

/**
 * 导航项目数据
 */
const NAV_ITEMS = [
  { href: '/', text: '首页', isAnchor: false },
  { href: '/product', text: '产品信息', isAnchor: false },
  { href: '/cases', text: '客户案例', isAnchor: false },
  { href: '/team', text: '团队', isAnchor: false },
  { href: '/contact', text: '联系我们', isAnchor: false },
];

interface NavigationProps {
  brandName?: string;
}

/**
 * Next.js 导航组件
 * 完全保持原有的导航功能和样式
 */
export const NextNavigation: React.FC<NavigationProps> = ({ brandName = '寒链' }) => {
  const [isCollapsed, setIsCollapsed] = useState(true);
  const pathname = usePathname();

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  return (
    <nav className="navbar navbar-expand-lg navbar-light bg-light fixed-top">
      <div className="container">
        {/* 品牌 */}
        <Link href="/" className="navbar-brand">
          {brandName}
        </Link>

        {/* 移动端切换按钮 */}
        <button
          className="navbar-toggler"
          type="button"
          onClick={toggleCollapse}
          aria-controls="navbarCollapse"
          aria-expanded={!isCollapsed}
          aria-label="Toggle navigation"
        >
          <span className="navbar-toggler-icon"></span>
        </button>

        {/* 导航菜单 */}
        <div className={`collapse navbar-collapse ${!isCollapsed ? 'show' : ''}`} id="navbarCollapse">
          <ul className="navbar-nav ms-auto mb-2 mb-lg-0">
            {NAV_ITEMS.map((item, index) => (
              <li key={index} className="nav-item">
                <Link
                  href={item.href}
                  className={`nav-link ${isActive(item.href) ? 'active' : ''}`}
                  onClick={() => setIsCollapsed(true)}
                >
                  {item.text}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </nav>
  );
};
