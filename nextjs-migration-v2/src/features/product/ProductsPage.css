.products-page {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.product-card {
  transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.card-img-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 4px 4px 0 0;
}

.card-img-wrapper::before {
  content: '';
  display: block;
  padding-top: 75%;
}

.card-img-wrapper img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease-in-out;
}

.product-card:hover .card-img-wrapper img {
  transform: scale(1.05);
}

.card-body {
  padding: 1.5rem;
}

.card-title {
  font-size: 1.25rem;
  margin-bottom: 0.75rem;
  color: #333;
}

.card-text {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.5;
} 