.product-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.product-content {
  padding: 50px 0;
}

.product-content-no-hero {
  padding-top: 120px; /* 为顶部导航栏留出空间 */
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.text-center {
  text-align: center;
}

.mb-3 {
  margin-bottom: 1.5rem;
}

.mb-5 {
  margin-bottom: 3rem;
}

/* 标题装饰 */
.title-container {
  margin-bottom: 3rem;
  text-align: center;
}

.title-decoration {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.title-decoration span {
  position: relative;
  width: 80px;
  height: 4px;
  background-color: #1890ff;
  border-radius: 2px;
}

.title-decoration span::before,
.title-decoration span::after {
  content: '';
  position: absolute;
  top: 0;
  width: 8px;
  height: 4px;
  background-color: #1890ff;
  border-radius: 2px;
}

.title-decoration span::before {
  left: -16px;
}

.title-decoration span::after {
  right: -16px;
}

/* 产品标签页样式 - 重新设计 */
.product-tabs {
  margin-bottom: 2rem;
}

.product-tabs .ant-tabs-nav {
  margin-bottom: 2rem;
}

/* 自定义标签页样式 */
.custom-tabs .ant-tabs-tab {
  background-color: #f5f5f5 !important;
  border: 1px solid #e8e8e8 !important;
  border-radius: 8px !important;
  margin: 0 4px !important;
  transition: all 0.3s ease;
  padding: 10px 16px !important;
  height: auto !important;
}

.custom-tabs .ant-tabs-tab:hover {
  background-color: #e6f7ff !important;
  border-color: #91d5ff !important;
  color: #1890ff !important;
}

.custom-tabs .ant-tabs-tab-active {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
  color: white !important;
}

.custom-tabs .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: white !important;
  font-weight: 600;
}

.custom-tabs .ant-tabs-tab-active .anticon,
.custom-tabs .ant-tabs-tab-active .tab-text {
  color: white !important;
}

.custom-tabs .ant-tabs-tab-btn {
  padding: 0 !important;
}

/* 标签内容样式 */
.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-label .anticon {
  font-size: 18px;
}

.tab-text {
  font-size: 16px;
}

/* 产品数量标记 */
.product-count {
  margin-left: 4px;
}

.product-count .ant-badge-count {
  background-color: rgba(24, 144, 255, 0.2);
  color: #1890ff;
  font-size: 12px;
  height: 20px;
  line-height: 20px;
  padding: 0 6px;
  border-radius: 10px;
  box-shadow: none;
}

.ant-tabs-tab-active .product-count .ant-badge-count {
  background-color: white;
  color: #1890ff;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  width: 100%;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.loading-container .ant-spin-text {
  margin-top: 12px;
  color: #1890ff;
}

/* 数据来源标记 */
.data-source-indicator {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-left: 8px;
  vertical-align: middle;
}

.data-source-api {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.data-source-static {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .custom-tabs .ant-tabs-tab {
    padding: 8px 12px !important;
  }

  .tab-label .anticon {
    font-size: 16px;
  }

  .tab-text {
    font-size: 14px;
  }

  .loading-container {
    min-height: 200px;
  }
}

/* 分类描述样式 */
.category-description {
  font-size: 16px;
  text-align: center;
  max-width: 800px;
  margin: 0 auto 1.5rem;
  color: #666;
}

/* 产品分类内容区域 */
.product-category-content {
  padding: 1rem 0 3rem;
}

/* 空产品提示样式 */
.empty-products {
  padding: 3rem 0;
  text-align: center;
}