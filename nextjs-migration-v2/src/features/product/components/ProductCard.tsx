import React, { useState } from 'react';
import { Card, Button, Typography, Modal, Carousel, Tabs, List, Descriptions } from 'antd';
import { useNavigate } from 'react-router-dom';
import { ArrowRightOutlined } from '@ant-design/icons';
import { Product } from '../data/productData';
import './ProductCard.css';

interface ProductCardProps {
  product: Product;
}

const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const navigate = useNavigate();
  const [isModalVisible, setIsModalVisible] = useState(false);

  const handleViewDetails = () => {
    navigate(`/product/${product.id}`);
  };

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  return (
    <>
      <Card
        className="product-card"
        cover={
          <div className="product-card-cover">
            <img alt={product.title} src={product.images[0].src} />
          </div>
        }
      >
        <Title level={3}>{product.title}</Title>
        <Paragraph ellipsis={{ rows: 3 }}>{product.description}</Paragraph>
        <Button
          type="primary"
          onClick={showModal}
          className="product-card-button"
        >
          了解更多 <ArrowRightOutlined />
        </Button>
      </Card>

      <Modal
        title={product.title}
        open={isModalVisible}
        onCancel={handleCancel}
        footer={null}
        width={800}
        className="product-modal"
      >
        <Carousel autoplay className="product-carousel">
          {product.images.map(image => (
            <div key={image.id}>
              <img src={image.src} alt={image.alt} className="product-modal-image" />
            </div>
          ))}
        </Carousel>

        <Tabs defaultActiveKey="1" className="product-tabs">
          <TabPane tab="产品介绍" key="1">
            <Paragraph>{product.description}</Paragraph>
          </TabPane>

          <TabPane tab="产品特点" key="2">
            <List
              dataSource={product.features}
              renderItem={item => (
                <List.Item>
                  <Paragraph>{item}</Paragraph>
                </List.Item>
              )}
            />
          </TabPane>

          <TabPane tab="应用场景" key="3">
            <List
              dataSource={product.applications}
              renderItem={item => (
                <List.Item>
                  <Paragraph>{item}</Paragraph>
                </List.Item>
              )}
            />
          </TabPane>

          <TabPane tab="规格参数" key="4">
            {product.specifications && (
              <Descriptions bordered column={1}>
                {Object.entries(product.specifications).map(([key, value]) => (
                  <Descriptions.Item key={key} label={key}>{value}</Descriptions.Item>
                ))}
              </Descriptions>
            )}
          </TabPane>
        </Tabs>

        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Button type="primary" onClick={handleViewDetails}>
            查看详情页面 <ArrowRightOutlined />
          </Button>
        </div>
      </Modal>
    </>
  );
};

export default ProductCard;