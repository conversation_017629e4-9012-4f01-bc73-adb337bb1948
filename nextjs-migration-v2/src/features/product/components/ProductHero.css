.product-hero {
  position: relative;
  height: 300px;
  background-image: linear-gradient(45deg, #eef0f3 0%, #182848 100%);
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-top: 60px; /* 为导航栏留出空间 */
}

.product-hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.2);
}

.product-hero-content {
  position: relative;
  z-index: 1;
  text-align: center;
  padding: 0 20px;
}

.product-hero-content h1 {
  color: white;
  font-size: 2.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.product-hero-content p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.2rem;
  max-width: 600px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .product-hero {
    height: 250px;
  }
  
  .product-hero-content h1 {
    font-size: 2rem;
  }
  
  .product-hero-content p {
    font-size: 1rem;
  }
}
