.product-card {
  height: 100%;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.product-card-cover {
  height: 200px;
  overflow: hidden;
}

.product-card-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s;
}

.product-card:hover .product-card-cover img {
  transform: scale(1.05);
}

.product-card-button {
  margin-top: 1rem;
}

.product-modal-image {
  width: 100%;
  height: 400px;
  object-fit: cover;
}

.product-carousel {
  margin-bottom: 24px;
}

.product-tabs {
  margin-top: 24px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .product-modal-image {
    height: 250px;
  }
}