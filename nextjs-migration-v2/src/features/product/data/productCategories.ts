import { Product, products } from './productData';

/**
 * 产品分类接口
 */
export interface ProductCategory {
  /** 分类ID */
  id: string;
  /** 分类名称 */
  name: string;
  /** 分类描述 */
  description?: string;
  /** 分类图标 */
  icon?: string;
}

/**
 * 产品数据来源
 */
export enum ProductDataSource {
  /** 静态数据 */
  STATIC = 'static',
  /** API数据 */
  API = 'api'
}

/**
 * 产品分类数据
 */
export const productCategories: ProductCategory[] = [
  {
    id: 'all',
    name: '全部产品',
    description: '我们提供各种高品质的冰产品，满足不同行业和场景的需求。'
  },
  {
    id: 'industrial',
    name: '工业冰产品',
    description: '专为工业应用设计的高品质冰产品，满足各种工业冷却和温度控制需求。'
  },
  {
    id: 'food',
    name: '食用冰产品',
    description: '符合食品安全标准的食用冰产品，适用于餐饮、酒店和活动等场景。'
  },
  {
    id: 'service',
    name: '冰产品服务',
    description: '专业的冰产品配送、租赁和定制服务，为您提供全方位的冰产品解决方案。'
  }
];

/**
 * 根据分类获取产品
 * @param categoryId 分类ID
 * @returns 产品列表
 */
export const getProductsByCategory = (categoryId: string): Product[] => {
  if (categoryId === 'all') {
    return products;
  }

  // 根据分类ID筛选产品
  // 这里使用简单的匹配逻辑，实际项目中可能需要更复杂的分类逻辑
  switch (categoryId) {
    case 'industrial':
      return products.filter(product =>
        product.title.includes('工业') ||
        product.applications.some(app => app.includes('工业') || app.includes('冷却'))
      );
    case 'food':
      return products.filter(product =>
        product.title.includes('食用') ||
        product.applications.some(app => app.includes('餐厅') || app.includes('饮品'))
      );
    case 'service':
      // 目前没有服务类产品，返回空数组
      return [];
    default:
      return products;
  }
};

/**
 * 扩展产品数据，添加更多产品
 * 注意：这些产品将在实际API集成后被替换
 */
export const extendedProducts: Product[] = [
  ...products,
  {
    id: 3,
    title: '干冰产品',
    description: '我们的干冰产品是固态二氧化碳，温度极低，适用于特殊冷却和保鲜需求。',
    features: [
      '温度极低（-78.5°C）',
      '无液体残留',
      '快速降温效果',
      '多种形状和尺寸',
      '适用于特殊场景'
    ],
    applications: [
      '食品冷链运输',
      '医药产品保存',
      '工业清洗',
      '特效烟雾制作',
      '实验室冷却'
    ],
    images: [
      { id: 1, src: '/img/dry-ice-1.jpg', alt: '干冰产品' },
      { id: 2, src: '/img/dry-ice-2.jpg', alt: '干冰应用' }
    ],
    specifications: {
      '纯度': '99.99%',
      '形态': '块状、颗粒状、片状',
      '温度': '-78.5°C',
      '包装': '10kg/箱，特殊保温容器'
    }
  },
  {
    id: 4,
    title: '冰雕艺术',
    description: '我们提供精美的冰雕艺术服务，为各类活动和场合增添独特的视觉效果和氛围。',
    features: [
      '精细雕刻工艺',
      '定制设计方案',
      '多种尺寸可选',
      '专业安装和维护',
      '适合各类场合'
    ],
    applications: [
      '婚礼和庆典',
      '企业活动',
      '产品发布会',
      '酒店和餐厅装饰',
      '节日庆祝活动'
    ],
    images: [
      { id: 1, src: '/img/ice-sculpture-1.jpg', alt: '冰雕艺术' },
      { id: 2, src: '/img/ice-sculpture-2.jpg', alt: '冰雕应用' }
    ],
    specifications: {
      '材质': '纯净冰块',
      '尺寸范围': '30cm-300cm',
      '定制选项': '造型、颜色、灯光',
      '持续时间': '室内环境4-8小时'
    }
  },
  {
    id: 5,
    title: '冰块配送服务',
    description: '我们提供专业的冰块配送服务，满足各类商业和活动需求，保证及时送达和产品质量。',
    features: [
      '全天候配送',
      '定时送达',
      '批量订购优惠',
      '专业冷藏运输',
      '灵活的配送方案'
    ],
    applications: [
      '餐饮企业',
      '酒店和度假村',
      '活动和庆典',
      '零售商店',
      '医疗机构'
    ],
    images: [
      { id: 1, src: '/img/ice-delivery-1.jpg', alt: '冰块配送' },
      { id: 2, src: '/img/ice-delivery-2.jpg', alt: '冰块配送服务' }
    ],
    specifications: {
      '配送范围': '上海市区及周边',
      '最小订单': '10kg',
      '配送时间': '8:00-22:00',
      '紧急配送': '可提供2小时内紧急配送'
    }
  }
];

/**
 * 根据分类获取扩展产品
 * @param categoryId 分类ID
 * @returns 产品列表
 */
export const getExtendedProductsByCategory = (categoryId: string): Product[] => {
  if (categoryId === 'all') {
    return extendedProducts;
  }

  // 根据分类ID筛选产品
  switch (categoryId) {
    case 'industrial':
      return extendedProducts.filter(product =>
        product.title.includes('工业') ||
        product.title.includes('干冰') ||
        product.applications.some(app => app.includes('工业') || app.includes('冷却'))
      );
    case 'food':
      return extendedProducts.filter(product =>
        product.title.includes('食用') ||
        product.applications.some(app => app.includes('餐厅') || app.includes('饮品'))
      );
    case 'service':
      return extendedProducts.filter(product =>
        product.title.includes('服务') ||
        product.title.includes('配送') ||
        product.title.includes('冰雕')
      );
    default:
      return extendedProducts;
  }
};
