import { Product } from '../data/productData';
import { ProductCategory, ProductDataSource } from '../data/productCategories';

/**
 * 根据分类筛选产品
 * 
 * @param products 产品数组
 * @param categoryId 分类ID
 * @returns 筛选后的产品数组
 */
export const filterProductsByCategory = (products: Product[], categoryId: string): Product[] => {
  if (categoryId === 'all') {
    return products;
  }
  
  // 根据分类ID筛选产品
  switch (categoryId) {
    case 'industrial':
      return products.filter(product => 
        product.title.includes('工业') || 
        product.title.includes('干冰') ||
        product.applications.some(app => app.includes('工业') || app.includes('冷却'))
      );
    case 'food':
      return products.filter(product => 
        product.title.includes('食用') || 
        product.applications.some(app => app.includes('餐厅') || app.includes('饮品'))
      );
    case 'service':
      return products.filter(product => 
        product.title.includes('服务') || 
        product.title.includes('配送') ||
        product.title.includes('冰雕')
      );
    default:
      return products;
  }
};

/**
 * 记录产品数据来源
 * 
 * @param source 数据来源
 * @param products 产品数组
 */
export const logProductDataSource = (source: ProductDataSource, products: Product[]): void => {
  console.info(
    `产品数据来源: ${source === ProductDataSource.API ? 'API' : '静态数据'}, ` +
    `共 ${products.length} 个产品`
  );
};

/**
 * 合并产品分类数据
 * 
 * @param categories 分类数组
 * @param products 产品数组
 * @returns 带有产品计数的分类数组
 */
export const enrichCategoriesWithProductCounts = (
  categories: ProductCategory[],
  products: Product[]
): (ProductCategory & { count: number })[] => {
  return categories.map(category => {
    const categoryProducts = filterProductsByCategory(products, category.id);
    return {
      ...category,
      count: categoryProducts.length
    };
  });
};
