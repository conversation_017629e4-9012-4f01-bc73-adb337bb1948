import React from 'react';

// 定义组件Props接口
interface FeatureListItemProps {
  content: string;
}

export const FeatureListItem: React.FC<FeatureListItemProps> = ({ content }) => (
  <li className="d-flex mb-2">
    <IconBadge />
    <span className="align-self-center">{content}</span>
  </li>
);

// 图标组件
const IconBadge: React.FC = () => (
  <i
    className="bi bi-check2-circle text-primary me-2"
    aria-hidden="true"
  />
);