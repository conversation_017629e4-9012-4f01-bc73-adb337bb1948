import React from 'react';
import PropTypes from 'prop-types';
import { ImageColumn } from './ImageColumn'; // 图片列组件
import { FeatureList } from './FeatureList'; // 特性列表组件
import { Loading } from '../../components/feedback/Loading'; // 加载占位组件

// 定义数据接口
interface AboutData {
  imageUrl?: string;
  paragraph?: string;
  why?: string[];
  why2?: string[];
}

// 定义组件Props接口
interface AboutProps {
  data?: AboutData;
}

// 主组件：关于我们部分
export const About: React.FC<AboutProps> = ({ data }) => {
  // 合并默认值
  const mergedData: AboutData = {
    imageUrl: 'img/storybook-about.jpg',
    paragraph: '专业冷链解决方案提供商...',
    why: [
      "✔️ 纯净品质：每一块冰都经严格质检...",
      "✔️ 丰富选择：食用冰、碎冰...",
      "✔️ 灵活服务：小到家庭用冰袋..."
    ],
    why2: [
      '✔️智能温控系统',
      '✔️全网直供'
    ],
    ...data
  };

  return (
    <section id="about" className="py-5">
      <div className="container">
        <div className="row g-4 align-items-center">
          {/* 图片列 */}
          <ImageColumn
            src={data?.imageUrl || 'img/about.jpg'} // 默认图片路径
            alt="冷链物流专业团队工作场景" // 图片描述
          />

          <div className="col-12 col-md-6">
            {/* 内容块 */}
            <ContentBlock data={mergedData} />
          </div>
        </div>
      </div>
    </section>
  );
};

// 子组件Props接口
interface ContentBlockProps {
  data: AboutData;
}

// 子组件：内容块
const ContentBlock: React.FC<ContentBlockProps> = ({ data }) => (
  <div className="ps-md-4">
    <h2 className="mb-4">关于我们</h2>
    {/* 段落内容 */}
    <LeadParagraph content={data?.paragraph} />

    <h3 className="mt-4 mb-3">选择我们的理由</h3>

    <div className="row row-cols-1 row-cols-lg-2 g-3">
      {/* 特性列表 1 */}
      <FeatureList items={data?.why} />
      {/* 特性列表 2 */}
      <FeatureList items={data?.why2} />
    </div>
  </div>
);

// 段落Props接口
interface LeadParagraphProps {
  content?: string;
}

// 子组件：段落内容
const LeadParagraph: React.FC<LeadParagraphProps> = ({ content }) => (
  <p className="lead">
    {/* 如果没有内容则显示加载占位 */}
    {content || <Loading placeholder="paragraph" />}
  </p>
);

// PropTypes可以保留以支持旧代码，但在TypeScript中不是必需的
About.propTypes = {
  data: PropTypes.shape({
    imageUrl: PropTypes.string, // 图片 URL
    paragraph: PropTypes.string, // 段落内容
    why: PropTypes.arrayOf(PropTypes.string), // 选择我们的理由列表 1
    why2: PropTypes.arrayOf(PropTypes.string) // 选择我们的理由列表 2
  })
};