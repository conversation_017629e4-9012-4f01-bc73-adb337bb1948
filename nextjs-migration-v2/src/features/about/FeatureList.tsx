import React from 'react';
import { FeatureListItem } from './FeatureListItem';
import { Loading } from '../../components/feedback/Loading';

// 定义组件Props接口
interface FeatureListProps {
  items?: string[];
}

export const FeatureList: React.FC<FeatureListProps> = ({ items }) => {
  if (!items) return <Loading placeholder="list" />;

  return (
    <div className="col">
      <ul className="list-unstyled">
        {items.map((item, index) => (
          <FeatureListItem key={`feature-${index}`} content={item} />
        ))}
      </ul>
    </div>
  );
};