import React from 'react';
import { Row, Col, Image } from 'react-bootstrap';
import { ContactInfoCard } from './ContactInfoCard';
import { SocialLinks } from './SocialLinks';
import { PageFooter } from './PageFooter';
import { ContactUsProps, QRCodeItem } from './types';

/**
 * 联系我们页面组件
 * 显示联系信息、二维码和社交链接
 * 
 * @param props - 联系我们页面组件属性
 * @returns React组件
 */
export const ContactUs: React.FC<ContactUsProps> = ({
    address,
    phone,
    email,
    contactPerson,
    qrCodes = [],
    social
}) => {
    return (
        <div className="contact-section bg-light">
            <div id="contact" className="py-5">
                <div className="container">
                    <div className="row g-5">
                        {/* 联系方式表单列 */}
                        <div className="col-md-8 order-md-1">
                            <div className="section-title mb-5">
                                <h2 className="display-5 fw-bold mb-3">联系我们</h2>
                                <p className="text-muted mt-2 mb-0">添加微信</p>
                            </div>

                            {/* 微信联系区域 */}
                            <div className="wechat-contact mt-5 pt-4">
                                <Row className="g-4 justify-content-center">
                                    {qrCodes.map((qrCode: QRCodeItem, index: number) => (
                                        <Col xs={6} md={3} className="text-center" key={index}>
                                            <Image
                                                src={qrCode.image}
                                                fluid
                                                rounded
                                                thumbnail
                                                className="wechat-qrcode shadow-sm"
                                                alt={qrCode.title}
                                            />
                                            <p className="text-muted mt-2 mb-0">{qrCode.title}</p>
                                            <small className="text-secondary">{qrCode.description}</small>
                                        </Col>
                                    ))}
                                </Row>
                            </div>

                            <SocialLinks
                                platforms={['wechat', 'weibo', 'twitter', 'youtube']}
                                links={social}
                                className="mt-5"
                            />
                        </div>

                        {/* 联系信息卡片列 */}
                        <div className="col-md-4 order-md-2">
                            <ContactInfoCard
                                title="联系信息"
                                address={address}
                                phone={phone}
                                email={email}
                                contactPerson={contactPerson}
                            />
                        </div>
                    </div>
                </div>
            </div>

            <PageFooter recordNumbers={["沪ICP备2025123281号", "沪ICP备2025123281号-1"]} />
        </div>
    );
};
