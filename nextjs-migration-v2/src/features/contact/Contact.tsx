import React from 'react';
import { useContactForm } from './hooks/useContactForm';
import { ContactInfoCard } from './ContactInfoCard';
import { ContactForm } from './ContactForm';
import { SocialLinks } from './SocialLinks';
import { PageFooter } from './PageFooter';
import { ContactProps } from './types';

/**
 * 联系组件
 * 显示联系表单、联系信息和社交链接
 * 
 * @param props - 联系组件属性
 * @returns React组件
 */
export const Contact: React.FC<ContactProps> = ({ data }) => {
  // 使用联系表单钩子
  const {
    formState,
    errors,
    submitting,
    submitStatus,
    handleChange,
    handleBlur,
    handleSubmit
  } = useContactForm();

  return (
    <div className="contact-section bg-light">
      <div id="contact" className="py-5">
        <div className="container">
          <div className="row g-5">
            {/* 联系方式表单列 */}
            <div className="col-md-8 order-md-1">
              <ContactForm
                style={{ backgroundColor: 'transparent' }}
                formState={formState}
                errors={errors}
                submitting={submitting}
                submitStatus={submitStatus}
                onChange={handleChange}
                onBlur={handleBlur}
                onSubmit={handleSubmit}
              />
              <SocialLinks
                platforms={['wechat', 'weibo', 'twitter', 'youtube']}
                links={data?.social}
                className="mt-5"
              />
            </div>

            {/* 联系信息卡片列 */}
            <div className="col-md-4 order-md-2">
              <ContactInfoCard
                title="联系我们"
                address={data?.address}
                phone={data?.phone}
                email={data?.email}
              />
            </div>
          </div>
        </div>
      </div>

      <PageFooter recordNumbers={["沪ICP备2025123281号", "沪ICP备2025123281号-1"]} />
    </div>
  );
};
