import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
    faMapMarkerAlt,
    faPhone,
    faEnvelope,
    faUser
} from '@fortawesome/free-solid-svg-icons';
import { ContactInfoCardProps, ContactItemProps } from './types';
import './ContactInfoCard.css';

/**
 * 图标映射表
 */
const iconMap = {
    'geo-alt': faMapMarkerAlt,
    'telephone': faPhone,
    'envelope': faEnvelope,
    'user': faUser
};

/**
 * 联系项目组件
 * 显示带有图标的联系信息项
 * 
 * @param props - 联系项目组件属性
 * @returns React组件
 */
const ContactItem: React.FC<ContactItemProps> = ({ icon, value }) => (
    <div className="contact-item">
        <div className="d-flex align-items-center">
            <FontAwesomeIcon
                icon={iconMap[icon as keyof typeof iconMap]}
                className="text-primary"
                style={{
                    fontSize: '1rem',
                    width: '1.2em',
                    marginRight: '8px',
                    position: 'relative',
                    top: '1px'
                }}
            />
            <span style={{
                lineHeight: '1.2',
                display: 'block',
                marginBottom: '0'
            }}>{value || "加载中..."}</span>
        </div>
    </div>
);

/**
 * 联系信息卡片组件
 * 显示联系信息，包括地址、电话、邮箱等
 * 
 * @param props - 联系信息卡片组件属性
 * @returns React组件
 */
export const ContactInfoCard: React.FC<ContactInfoCardProps> = ({ 
    title,
    address, 
    phone, 
    email, 
    contactPerson 
}) => (
    <div className="contact-info-custom h-100 p-4">
        <h3 className="mb-4">{title || "联系信息"}</h3>
        <div className="vstack gap-3">
            <ContactItem icon="geo-alt" label="地址" value={address} />
            <ContactItem icon="user" label="联系人" value={contactPerson || "彭经理"} />
            <ContactItem icon="telephone" label="电话" value={phone} />
            <ContactItem icon="envelope" label="邮箱" value={email || "<EMAIL>"} />
        </div>
    </div>
);
