import React from 'react';
import { Row, Col, Image } from 'react-bootstrap';
import { ContactFormProps } from './types';

/**
 * 微信二维码图片路径
 */
const wechatImageFirst = '/img/wechat/wechat-1.jpg';
const wechatImageSecond = '/img/wechat/wechat-2.jpg';

/**
 * 联系表单组件
 * 显示微信二维码和联系方式
 * 
 * @param props - 联系表单组件属性
 * @returns React组件
 */
export const ContactForm: React.FC<ContactFormProps> = ({

    style
}) => (
    <>
        <div className="section-title mb-5" style={style}>
            <h2 className="display-5 fw-bold mb-3">联系我们</h2>
            <p className="text-muted mt-2 mb-0">添加微信</p>
        </div>

        {/* 微信联系区域 */}
        <div className="wechat-contact mt-5 pt-4">
            <Row className="g-4 justify-content-center">
                {/* 客服微信1 */}
                <Col xs={6} md={3} className="text-center">
                    <Image
                        src={wechatImageFirst}
                        fluid
                        rounded
                        thumbnail
                        className="wechat-qrcode shadow-sm"
                        alt="客服微信1"
                    />
                    <p className="text-muted mt-2 mb-0">客服微信 1 号</p>
                    <small className="text-secondary">微信号：wxid_dua7cqdft7nu12</small>
                </Col>

                {/* 客服微信2 */}
                <Col xs={6} md={3} className="text-center">
                    <Image
                        src={wechatImageSecond}
                        fluid
                        rounded
                        thumbnail
                        className="wechat-qrcode shadow-sm"
                        alt="客服微信2"
                    />
                    <p className="text-muted mt-2 mb-0">客服微信 2 号</p>
                    <small className="text-secondary">微信号：wxid_cn6z02vx4swl22</small>
                </Col>

                {/* 微信公众号1 */}
                <Col xs={6} md={3} className="text-center">
                    <Image
                        src={wechatImageSecond}
                        fluid
                        rounded
                        thumbnail
                        className="wechat-qrcode shadow-sm"
                        alt="官方公众号"
                    />
                    <p className="text-muted mt-2 mb-0">官方公众号</p>
                    <small className="text-secondary">扫码关注</small>
                </Col>

                {/* 微信公众号2 */}
                <Col xs={6} md={3} className="text-center">
                    <Image
                        src={wechatImageFirst}
                        fluid
                        rounded
                        thumbnail
                        className="wechat-qrcode shadow-sm"
                        alt="订阅号"
                    />
                    <p className="text-muted mt-2 mb-0">微信订阅号</p>
                    <small className="text-secondary">最新资讯</small>
                </Col>
            </Row>
        </div>
    </>
);
