import React from 'react';
import { FormFieldProps } from './types';

/**
 * 表单字段组件
 * 显示带有标签和错误提示的输入字段
 * 
 * @param props - 表单字段组件属性
 * @returns React组件
 */
export const FormField: React.FC<FormFieldProps> = ({
    type,
    id,
    label,
    value,
    error,
    onChange,
    onBlur,
    disabled
}) => (
    <div className="col-md-6">
        <div className="form-floating">
            <input
                type={type}
                id={id}
                name={id}
                className={`form-control ${error ? 'is-invalid' : ''}`}
                placeholder=" "
                value={value}
                onChange={onChange}
                onBlur={onBlur}
                disabled={disabled}
            />
            <label htmlFor={id}>{label}</label>

            {/* 错误提示 */}
            <div className="invalid-feedback d-block">
                {error && (
                    <div className="d-flex align-items-center gap-2 mt-1">
                        <i className="bi bi-exclamation-circle text-danger"></i>
                        <span>{error}</span>
                    </div>
                )}
            </div>
        </div>
    </div>
);
