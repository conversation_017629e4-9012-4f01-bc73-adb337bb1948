import { useState, useRef, useEffect, ChangeEvent, FormEvent, FocusEvent } from "react";
import emailjs from 'emailjs-com';
import { FormState, FormErrors } from '../types';

/**
 * 初始表单状态
 */
const initialState: FormState = {
    name: "",
    email: "",
    message: "",
};

/**
 * 联系表单钩子
 * 提供表单状态管理、验证和提交功能
 * 
 * @returns 表单状态和处理函数
 */
export const useContactForm = () => {
    // 表单状态
    const [formState, setFormState] = useState<FormState>(initialState);
    // 提交状态
    const [submitting, setSubmitting] = useState<boolean>(false);
    // 提交结果
    const [submitStatus, setSubmitStatus] = useState<'success' | 'error' | null>(null);
    // 表单错误
    const [errors, setErrors] = useState<FormErrors>({});
    // 验证定时器引用
    const validateTimeout = useRef<NodeJS.Timeout | null>(null);

    /**
     * 验证单个字段
     * 
     * @param name - 字段名称
     * @param value - 字段值
     */
    const validateField = (name: keyof FormState, value: string): void => {
        let error = '';

        switch (name) {
            case 'name':
                if (!value.trim()) error = '姓名不能为空';
                break;
            case 'email':
                if (!value) {
                    error = '邮箱地址不能为空';
                } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value)) {
                    error = '邮箱格式不正确（示例：<EMAIL>）';
                }
                break;
            case 'message':
                if (!value.trim()) error = '留言内容不能为空';
                break;
            default:
                break;
        }

        setErrors(prev => ({
            ...prev,
            [name]: error
        }));
    };

    /**
     * 处理表单字段变更
     * 
     * @param e - 变更事件
     */
    const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>): void => {
        const { name, value } = e.target;
        setFormState(prev => ({ ...prev, [name]: value }));

        // 对邮箱字段进行延迟验证
        if (name === 'email') {
            if (validateTimeout.current) {
                clearTimeout(validateTimeout.current);
            }
            validateTimeout.current = setTimeout(() => {
                validateField(name as keyof FormState, value);
            }, 500);
        }
    };

    /**
     * 组件卸载时清理定时器
     */
    useEffect(() => {
        return () => {
            if (validateTimeout.current) {
                clearTimeout(validateTimeout.current);
            }
        };
    }, []);

    /**
     * 处理表单字段失焦
     * 
     * @param e - 失焦事件
     */
    const handleBlur = (e: FocusEvent<HTMLInputElement | HTMLTextAreaElement>): void => {
        const { name, value } = e.target;
        validateField(name as keyof FormState, value);
    };

    /**
     * 验证整个表单
     * 
     * @returns 表单是否有效
     */
    const validateForm = (): boolean => {
        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
        return (
            formState.name.trim() !== '' &&
            emailRegex.test(formState.email) &&
            formState.message.trim() !== ''
        );
    };

    /**
     * 处理表单提交
     * 
     * @param e - 提交事件
     */
    const handleSubmit = async (e: FormEvent<HTMLFormElement>): Promise<void> => {
        e.preventDefault();

        // 验证所有字段
        validateField('name', formState.name);
        validateField('email', formState.email);
        validateField('message', formState.message);

        if (!validateForm()) {
            return;
        }
        
        setSubmitting(true);

        try {
            await emailjs.sendForm(
                "YOUR_SERVICE_ID",
                "YOUR_TEMPLATE_ID",
                e.currentTarget,
                "YOUR_PUBLIC_KEY"
            );
            setSubmitStatus("success");
            setFormState(initialState);
        } catch (error) {
            console.error("邮件发送失败:", error);
            setSubmitStatus("error");
        } finally {
            setSubmitting(false);
            setTimeout(() => setSubmitStatus(null), 5000);
        }
    };

    return {
        formState,
        submitting,
        submitStatus,
        handleChange,
        handleSubmit,
        errors,
        handleBlur
    };
};
