'use client';

import React from 'react';
import { Gallery } from '@/features/gallery';
import { Features } from '@/features/feature';
import { About } from '@/features/about/about';
import { Services } from '@/features/services';

// 模拟数据 - 保持与原项目相同的数据结构
const mockData = {
  Gallery: {},
  Features: {},
  About: {},
  Services: {},
  Testimonials: {},
  Team: {}
};

/**
 * 首页组件
 * 完全保持原有的首页布局和功能
 */
export default function Home() {
  return (
    <>
      <Gallery data={mockData.Gallery} />
      <Features data={mockData.Features} />
      <About data={mockData.About} />
      <Services data={mockData.Services} />
    </>
  );
}


