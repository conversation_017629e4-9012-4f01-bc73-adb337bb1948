import type { Metadata } from "next";
import { AntdRegistry } from '@ant-design/nextjs-registry';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { NextMainLayout } from "@/layouts/NextMainLayout";
import "./globals.css";

export const metadata: Metadata = {
  title: "上海寒链实业有限公司",
  description: "专业的制冰解决方案提供商，提供工业冰、食用冰等多种产品和服务",
  keywords: "制冰,工业冰,食用冰,冷链,上海寒链",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <head>
        {/* Bootstrap CSS */}
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
          rel="stylesheet"
        />
        {/* Google Fonts - 保持原有字体 */}
        <link
          href="https://fonts.googleapis.com/css2?family=Raleway:wght@300;400;600;700&family=Lato:wght@300;400;700&display=swap"
          rel="stylesheet"
        />
      </head>
      <body>
        {/* Ant Design 注册表 */}
        <AntdRegistry>
          <ConfigProvider
            locale={zhCN}
            theme={{
              token: {
                colorPrimary: '#1890ff',
                borderRadius: 6,
              },
            }}
          >
            {/* 使用原有的主布局组件 */}
            <NextMainLayout>
              {children}
            </NextMainLayout>
          </ConfigProvider>
        </AntdRegistry>

        {/* Bootstrap JS */}
        <script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"
          async
        />
      </body>
    </html>
  );
}
