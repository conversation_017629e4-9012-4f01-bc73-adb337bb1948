// 注释: 定义通用类型，可在整个项目中复用

// 通用响应类型
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  message: string;
  success: boolean;
}

// 分页数据类型
export interface PaginatedData<T = any> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 用户类型
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  role: 'admin' | 'user' | 'guest';
  createdAt: string;
  updatedAt: string;
}

// 主题模式类型
export type ThemeMode = 'light' | 'dark' | 'system';

// 特性项类型
export interface FeatureItemType {
  id?: string;
  title: string;
  text: string;
  icon?: string;
  image?: string;
}

// 画廊项类型
export interface GalleryItemType {
  id?: string;
  title?: string;
  description?: string;
  smallImage: string;
  largeImage: string;
  category?: string;
}

// 产品项类型
export interface ProductItemType {
  id: number | string;
  title: string;
  description: string;
  price?: number;
  image: string;
  category?: string;
  features?: string[];
  specifications?: Record<string, string>;
}

// 联系信息类型
export interface ContactInfoType {
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  socialMedia?: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
  };
  mapLocation?: {
    lat: number;
    lng: number;
    zoom?: number;
  };
}

//