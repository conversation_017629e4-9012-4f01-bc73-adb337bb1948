// 用户信息类型
export interface UserInfo {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  role?: string;
  permissions?: string[];
  createdAt?: string;
  updatedAt?: string;
}

// 用户登录请求
export interface LoginRequest {
  username: string;
  password: string;
  remember?: boolean;
}

// 用户令牌类型
export interface UserToken {
  token: string;
  expiresIn: number;
}

// 用户注册请求
export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

// 用户登录响应
export interface LoginResponse {
  token: string;
  user: UserInfo;
}
