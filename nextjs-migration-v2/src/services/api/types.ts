/**
 * 统一的 API 类型定义
 * 定义标准的请求和响应格式
 */

// 基础响应接口
export interface BaseResponse<T = any> {
  /** 响应状态码 */
  code: number;
  /** 响应消息 */
  message: string;
  /** 响应数据 */
  data: T;
  /** 请求是否成功 */
  success: boolean;
  /** 时间戳 */
  timestamp: number;
  /** 请求追踪ID */
  traceId?: string;
}

// 分页响应接口
export interface PaginatedResponse<T = any> extends BaseResponse<T[]> {
  /** 分页信息 */
  pagination: {
    /** 当前页码 */
    current: number;
    /** 每页大小 */
    pageSize: number;
    /** 总数量 */
    total: number;
    /** 总页数 */
    totalPages: number;
  };
}

// 请求配置接口
export interface RequestConfig {
  /** 是否显示加载状态 */
  showLoading?: boolean;
  /** 是否显示错误提示 */
  showError?: boolean;
  /** 是否显示成功提示 */
  showSuccess?: boolean;
  /** 自定义错误处理 */
  customErrorHandler?: (error: ApiError) => void;
  /** 请求超时时间 */
  timeout?: number;
  /** 重试次数 */
  retryCount?: number;
}

// API 错误类型
export class ApiError extends Error {
  public code: number;
  public data?: any;
  public traceId?: string;

  constructor(
    message: string,
    code: number = 500,
    data?: any,
    traceId?: string
  ) {
    super(message);
    this.name = 'ApiError';
    this.code = code;
    this.data = data;
    this.traceId = traceId;
  }
}

// 错误类型枚举
export enum ErrorType {
  /** 网络错误 */
  NETWORK_ERROR = 'NETWORK_ERROR',
  /** 请求超时 */
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  /** 服务器错误 */
  SERVER_ERROR = 'SERVER_ERROR',
  /** 客户端错误 */
  CLIENT_ERROR = 'CLIENT_ERROR',
  /** 认证错误 */
  AUTH_ERROR = 'AUTH_ERROR',
  /** 权限错误 */
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  /** 业务逻辑错误 */
  BUSINESS_ERROR = 'BUSINESS_ERROR',
  /** 未知错误 */
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

// 错误详情接口
export interface ErrorDetail {
  type: ErrorType;
  code: number;
  message: string;
  details?: any;
  timestamp: number;
  traceId?: string;
}

// HTTP 状态码映射
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
} as const;

// 业务状态码映射
export const BUSINESS_CODE = {
  SUCCESS: 0,
  INVALID_PARAMS: 1001,
  RESOURCE_NOT_FOUND: 1002,
  RESOURCE_ALREADY_EXISTS: 1003,
  INSUFFICIENT_PERMISSIONS: 1004,
  OPERATION_FAILED: 1005,
  DATA_VALIDATION_FAILED: 1006,
  EXTERNAL_SERVICE_ERROR: 1007,
} as const;

// API 端点配置
export interface ApiEndpoint {
  /** 端点路径 */
  path: string;
  /** HTTP 方法 */
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  /** 描述 */
  description?: string;
  /** 是否需要认证 */
  requiresAuth?: boolean;
  /** 默认配置 */
  defaultConfig?: RequestConfig;
}

// 常用 API 端点定义
export const API_ENDPOINTS = {
  // 用户相关
  USER: {
    LOGIN: { path: '/auth/login', method: 'POST' as const },
    LOGOUT: { path: '/auth/logout', method: 'POST' as const },
    PROFILE: { path: '/user/profile', method: 'GET' as const, requiresAuth: true },
    UPDATE_PROFILE: { path: '/user/profile', method: 'PUT' as const, requiresAuth: true },
  },
  // 产品相关
  PRODUCT: {
    LIST: { path: '/products', method: 'GET' as const },
    DETAIL: { path: '/products/:id', method: 'GET' as const },
    CREATE: { path: '/products', method: 'POST' as const, requiresAuth: true },
    UPDATE: { path: '/products/:id', method: 'PUT' as const, requiresAuth: true },
    DELETE: { path: '/products/:id', method: 'DELETE' as const, requiresAuth: true },
  },
  // 分析相关
  ANALYTICS: {
    REPORT: { path: '/analytics/report', method: 'POST' as const },
    STATS: { path: '/analytics/stats', method: 'GET' as const, requiresAuth: true },
  },
} as const;
