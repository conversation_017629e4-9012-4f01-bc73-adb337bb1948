import React from 'react';
import { SEO, CompanySchema, BreadcrumbSchema } from '../components/seo';
import { Row, Col, Typography, Image } from 'antd';
import { EnvironmentOutlined, PhoneOutlined, MailOutlined } from '@ant-design/icons';
import './ContactPage.css';

const { Title, Text } = Typography;

/**
 * 联系页面组件
 * 显示公司联系信息、地址和二维码
 *
 * @returns React组件
 */
const ContactPage: React.FC = () => {
  // 公司联系信息
  const contactInfo = {
    address: '上海市宝山区长江西路2311号1-2层',
    phone: '13761182299',
    email: '<EMAIL>'
  };

  // 二维码数据
  const qrCodes = [
    {
      id: 1,
      title: '客服微信 1 号',
      description: 'wxid_dua7cqdft7nu12',
      image: '/img/wechat/wechat-1.jpg'
    },
    {
      id: 2,
      title: '客服微信 2 号',
      description: 'wxid_cn6z02vx4swl22',
      image: '/img/wechat/wechat-2.jpg'
    },
    {
      id: 3,
      title: '官方公众号',
      description: '扫码关注',
      image: '/img/wechat/wechat-1.jpg'
    },
    {
      id: 4,
      title: '微信订阅号',
      description: '最新资讯',
      image: '/img/wechat/wechat-2.jpg'
    }
  ];

  return (
    <div className="contact-page contact-page-wrapper">
      <SEO
        title="联系我们"
        description="联系上海寒链实业有限公司，获取更多关于我们产品和服务的信息。我们的专业团队将为您提供一对一的咨询服务。"
        keywords="联系我们,上海寒链,公司地址,联系方式"
        ogType="website"
        ogImage="/img/contact-og-image.jpg"
        ogImageAlt="上海寒链实业有限公司联系方式"
      />
      <CompanySchema />
      <BreadcrumbSchema
        items={[
          { name: '首页', url: 'https://www.hanchain.com/' },
          { name: '联系我们', url: 'https://www.hanchain.com/contact' }
        ]}
      />

      {/* 页面标题 */}
      <div className="contact-header">
        <div className="container">
          <Title level={1}>联系我们</Title>
        </div>
      </div>

      {/* 联系信息区域 */}
      <div className="contact-content">
        <div className="container">
          {/* 公司地址区域 */}
          <div className="address-section">
            <Title level={2} className="section-title">公司地址</Title>
            <div className="address-card">
              <Row gutter={[24, 24]} align="middle">
                <Col xs={24} md={12}>
                  <div className="address-info">
                    <div className="info-item">
                      <EnvironmentOutlined className="info-icon" />
                      <div className="info-content">
                        <Text strong>公司地址：</Text>
                        <Text>{contactInfo.address}</Text>
                      </div>
                    </div>
                    <div className="info-item">
                      <PhoneOutlined className="info-icon" />
                      <div className="info-content">
                        <Text strong>联系电话：</Text>
                        <Text>{contactInfo.phone}</Text>
                      </div>
                    </div>
                    <div className="info-item">
                      <MailOutlined className="info-icon" />
                      <div className="info-content">
                        <Text strong>电子邮箱：</Text>
                        <Text>{contactInfo.email}</Text>
                      </div>
                    </div>
                  </div>
                </Col>
                <Col xs={24} md={12}>
                  <div className="map-container">
                    <div className="map-placeholder">
                      <EnvironmentOutlined className="map-icon" />
                      <Text>{contactInfo.address}</Text>
                    </div>
                    <div className="map-actions">
                      <a
                        href={`https://map.baidu.com/search/${encodeURIComponent(contactInfo.address)}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="map-link"
                      >
                        在百度地图中查看
                      </a>
                      <a
                        href={`https://uri.amap.com/marker?position=121.422751,31.405372&name=${encodeURIComponent('上海寒链实业有限公司')}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="map-link"
                      >
                        导航到这里
                      </a>
                    </div>
                  </div>
                </Col>
              </Row>
            </div>
          </div>

          {/* 微信联系区域 */}
          <div className="qrcode-section">
            <Title level={2} className="section-title">添加微信</Title>
            <div className="qrcode-container">
              <Row gutter={[24, 32]} justify="center">
                {qrCodes.map(qrCode => (
                  <Col xs={12} sm={6} key={qrCode.id}>
                    <div className="qrcode-card">
                      <Image
                        src={qrCode.image}
                        alt={qrCode.title}
                        preview={false}
                        className="qrcode-image"
                        fallback="data:image/png;base64,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"
                      />
                      <div className="qrcode-info">
                        <Text strong className="qrcode-title">{qrCode.title}</Text>
                        <Text type="secondary" className="qrcode-desc">{qrCode.description}</Text>
                      </div>
                    </div>
                  </Col>
                ))}
              </Row>
            </div>
          </div>

          {/* 社交媒体链接 */}
          <div className="social-section">
            <div className="social-links">
              <a href="#" className="social-link" aria-label="微信">
                <i className="fab fa-weixin"></i>
              </a>
              <a href="#" className="social-link" aria-label="微博">
                <i className="fab fa-weibo"></i>
              </a>
              <a href="#" className="social-link" aria-label="推特">
                <i className="fab fa-twitter"></i>
              </a>
              <a href="#" className="social-link" aria-label="YouTube">
                <i className="fab fa-youtube"></i>
              </a>
            </div>
          </div>
        </div>
      </div>


    </div>
  );
};

export default ContactPage;
