import React from 'react';
import { Helmet } from 'react-helmet-async';

interface ServicesProps {
  data?: any;
}

const Services: React.FC<ServicesProps> = ({ data }) => {
  return (
    <>
      <Helmet>
        <title>我们的服务 | 上海寒链实业有限公司</title>
        <meta name="description" content="探索上海寒链实业有限公司提供的冷链物流和食用冰生产服务。" />
      </Helmet>

      <div className="page-header bg-light py-5 mt-5">
        <div className="container">
          <div className="row">
            <div className="col-12 text-center">
              <h1 className="display-4 fw-bold">我们的服务</h1>
              <p className="lead">专业的冷链解决方案</p>
            </div>
          </div>
        </div>
      </div>

      <section className="py-5">
        <div className="container">
          <div className="row mb-5">
            <div className="col-lg-8 mx-auto text-center">
              <p className="lead">
                我们提供全方位的冷链物流和食用冰生产服务，满足各行业客户的需求。
              </p>
            </div>
          </div>

          <div className="row g-4">
            {data && data.length > 0 ? (
              data.map((service: any, index: number) => (
                <div key={index} className="col-md-6 col-lg-4">
                  <div className="card h-100 shadow-sm">
                    <div className="card-body text-center">
                      <i className={`fas fa-${service.icon} fa-3x mb-3 text-primary`}></i>
                      <h3 className="h4 mb-3">{service.name}</h3>
                      <p>{service.text}</p>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-12 text-center">
                <p>暂无服务信息</p>
              </div>
            )}
          </div>
        </div>
      </section>
    </>
  );
};

export default Services;