import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';

const NotFound: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>页面未找到 | 上海寒链实业有限公司</title>
      </Helmet>

      <div className="container py-5 mt-5">
        <div className="row justify-content-center">
          <div className="col-md-8 text-center">
            <h1 className="display-1 fw-bold">404</h1>
            <h2 className="mb-4">页面未找到</h2>
            <p className="lead mb-5">您访问的页面不存在或已被移除。</p>
            <Link to="/" className="btn btn-primary">返回首页</Link>
          </div>
        </div>
      </div>
    </>
  );
};

export default NotFound;