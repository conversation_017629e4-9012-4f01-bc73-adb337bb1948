import React from 'react';
import { Helmet } from 'react-helmet-async';
import CompanyAddressMap from '../features/contact/CompanyAddressMap';

interface ContactProps {
  data?: any;
}

const Contact: React.FC<ContactProps> = ({ data }) => {
  return (
    <>
      <Helmet>
        <title>联系我们 | 上海寒链实业有限公司</title>
        <meta name="description" content="联系上海寒链实业有限公司，获取更多关于我们产品和服务的信息。" />
      </Helmet>

      <div className="page-header bg-light py-5 mt-5">
        <div className="container">
          <div className="row">
            <div className="col-12 text-center">
              <h1 className="display-4 fw-bold">联系我们</h1>
              <p className="lead">我们期待您的来信</p>
            </div>
          </div>
        </div>
      </div>

      {/* 公司地址地图 */}
      <section className="py-5 bg-light">
        <div className="container">
          <CompanyAddressMap
            address={data?.address || "上海市宝山区长江西路2311号1-2层"}
            phone={data?.phone || "13761182299"}
            mapImageUrl="/img/map/company-map.jpg"
          />
        </div>
      </section>

      {/* 联系表单 */}
      <section className="py-5">
        <div className="container">
          <div className="row">
            <div className="col-lg-6 mb-4 mb-lg-0">
              <h2 className="mb-4">联系信息</h2>
              <p>如果您对我们的产品和服务有任何疑问，请随时联系我们。我们的团队将很乐意为您提供帮助。</p>

              <div className="mt-4">
                <h5>地址</h5>
                <p>{data?.address || "上海市宝山区长江西路2311号1-2层"}</p>

                <h5>电话</h5>
                <p>{data?.phone || "13761182299"}</p>

                <h5>邮箱</h5>
                <p>{data?.email || "<EMAIL>"}</p>
              </div>
            </div>

            <div className="col-lg-6">
              <h2 className="mb-4">发送消息</h2>
              <form>
                <div className="mb-3">
                  <label htmlFor="name" className="form-label">姓名</label>
                  <input type="text" className="form-control" id="name" required />
                </div>

                <div className="mb-3">
                  <label htmlFor="email" className="form-label">邮箱</label>
                  <input type="email" className="form-control" id="email" required />
                </div>

                <div className="mb-3">
                  <label htmlFor="message" className="form-label">消息</label>
                  <textarea className="form-control" id="message" rows={5} required></textarea>
                </div>

                <button type="submit" className="btn btn-primary">发送</button>
              </form>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Contact;