Beacon 数据（信标数据） 指的是通过浏览器提供的 navigator.sendBeacon() API 发送的异步数据。这一技术专为 可靠且不阻塞页面 的数据上报场景设计，核心目的是解决传统 AJAX 请求在页面关闭时可能丢失数据的痛点。

通俗解释
想象这样一个场景：用户即将关闭网页，但此时需要上报「用户离开页面」这一行为。如果用传统 fetch 或 XMLHttpRequest 发送请求，可能因页面卸载而被浏览器强制中断，导致数据丢失。而 Beacon 数据 就像一个「信号弹」，允许浏览器在后台安静地完成数据发送，即使页面已经关闭。

Beacon 数据的核心特点
特性	说明
异步非阻塞	不会阻塞页面跳转/关闭流程
高优先级保障	浏览器会优先处理 Beacon 请求
自动重试机制	浏览器底层自动处理网络失败重试
无返回值处理	只关注是否成功加入发送队列，不关心响应结果
适合小数据量	通常建议数据量 < 64KB（不同浏览器有差异）
Beacon 数据 vs 传统请求
javascript
// 传统方式（可能丢失数据）
window.addEventListener('beforeunload', () => {
fetch('/api', { method: 'POST', body: data }) // 可能来不及完成
});

// Beacon 方式（可靠发送）
window.addEventListener('beforeunload', () => {
navigator.sendBeacon('/api', data); // 浏览器保证发送
});
Beacon 数据的典型使用场景
页面性能统计
（PV/UV 上报、页面停留时长计算）

用户行为追踪
（按钮点击、滚动深度、表单放弃填写）

错误日志收集
（JS 错误、API 请求失败）

广告转化跟踪
（用户点击广告后的行为追踪）

技术细节
1. 数据格式
   javascript
   // 支持多种数据格式
   navigator.sendBeacon(url, new Blob([JSON.stringify(data)])); // 二进制数据
   navigator.sendBeacon(url, new FormData(form)); // 表单数据
   navigator.sendBeacon(url, plainText); // 纯文本
2. 兼容性处理
   javascript
   // 降级方案示例
   if (navigator.sendBeacon) {
   navigator.sendBeacon(url, data);
   } else {
   fetch(url, {
   method: 'POST',
   body: data,
   keepalive: true // 模拟 Beacon 行为
   });
   }
3. 最佳实践
   javascript
   // 在页面隐藏时发送关键数据
   document.addEventListener('visibilitychange', () => {
   if (document.visibilityState === 'hidden') {
   const analyticsData = { /* ... */ };
   navigator.sendBeacon('/api/analytics', analyticsData);
   }
   });
   为什么需要 Beacon 数据？
   问题场景	Beacon 的解决方案
   用户快速关闭页面导致数据丢失	浏览器保证请求完成
   统计请求影响页面性能	低优先级不影响主线程
   移动端网络不稳定	自动等待网络恢复
   SPA 应用路由跳转时的数据竞争	请求队列有序处理
   开发者注意事项
   数据大小限制
   不同浏览器对 Beacon 数据大小的限制不同（Chrome 约 64KB）

CORS 限制
需配置正确的跨域头（Access-Control-Allow-Origin）

无法自定义请求头
Beacon 请求会自动携带基础头信息

无法获取响应
设计上报逻辑时应假设「发送即成功」

通过 Beacon 数据方案，开发者可以以最小的性能代价，实现关键业务数据的可靠上报，这是现代 Web 数据分析系统的基石技术之一。