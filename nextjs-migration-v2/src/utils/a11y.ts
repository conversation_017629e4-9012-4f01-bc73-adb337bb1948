import React from 'react';

/**
 * 可访问性辅助工具
 */

/**
 * 创建键盘可访问的点击处理函数
 * 允许用户使用 Enter 或 Space 键触发点击事件
 *
 * @param onClick - 点击处理函数
 * @returns 键盘事件处理函数
 */
export const keyboardAccessibleClick = (onClick: (event: React.MouseEvent | React.KeyboardEvent) => void) => {
  return {
    onClick,
    onKeyDown: (event: React.KeyboardEvent) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        onClick(event);
      }
    },
    role: 'button',
    tabIndex: 0,
  };
};

/**
 * 创建可访问的图片属性
 *
 * @param alt - 图片替代文本
 * @param isDecorative - 是否为装饰性图片
 * @returns 图片属性对象
 */
export const accessibleImageProps = (alt: string, isDecorative = false) => {
  if (isDecorative) {
    return {
      alt: '',
      role: 'presentation',
      'aria-hidden': 'true',
    };
  }

  return {
    alt,
  };
};

/**
 * 创建可访问的表单标签属性
 *
 * @param id - 表单元素ID
 * @param label - 标签文本
 * @param required - 是否必填
 * @returns 标签属性对象
 */
export const accessibleLabelProps = (id: string, label: string, required = false) => {
  // 返回标签属性
  return {
    htmlFor: id,
    label: label,
    required: required,
    // 不使用 JSX，而是返回纯文本属性
    'data-required': required ? 'true' : 'false',
    'aria-required': required ? 'true' : 'false'
  };
};

/**
 * 创建可访问的表单输入属性
 *
 * @param id - 表单元素ID
 * @param label - 标签文本
 * @param required - 是否必填
 * @returns 输入属性对象
 */
export const accessibleInputProps = (id: string, label: string, required = false) => {
  return {
    id,
    'aria-label': label,
    'aria-required': required,
    required,
  };
};

/**
 * 创建跳过导航链接属性
 *
 * @returns 跳过导航链接属性
 */
export const skipNavProps = () => {
  return {
    href: '#main-content',
    className: 'skip-nav',
    children: '跳到主要内容',
  };
};
