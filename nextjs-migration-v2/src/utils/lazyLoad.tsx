import React, { lazy, Suspense } from 'react';
import { LoadingSpinner } from '../components/feedback/LoadingSpinner';

/**
 * 组件懒加载工具函数
 * 
 * @param importFunc - 动态导入函数
 * @param fallback - 加载时显示的组件，默认为 LoadingSpinner
 * @returns 懒加载的组件
 */
export function lazyLoad(
  importFunc: () => Promise<{ default: React.ComponentType<any> }>,
  fallback: React.ReactNode = <LoadingSpinner />
) {
  const LazyComponent = lazy(importFunc);

  return (props: any) => (
    <Suspense fallback={fallback}>
      <LazyComponent {...props} />
    </Suspense>
  );
}

/**
 * 延迟加载组件
 * 仅在组件进入视口时才加载
 * 
 * @param importFunc - 动态导入函数
 * @returns 延迟加载的组件
 */
export function deferLoad(
  importFunc: () => Promise<{ default: React.ComponentType<any> }>
) {
  return (props: any) => {
    const [Component, setComponent] = React.useState<React.ComponentType<any> | null>(null);
    const containerRef = React.useRef<HTMLDivElement>(null);

    React.useEffect(() => {
      // 创建 Intersection Observer 实例
      const observer = new IntersectionObserver(
        (entries) => {
          // 当组件进入视口
          if (entries[0].isIntersecting) {
            // 动态导入组件
            importFunc().then(module => {
              setComponent(() => module.default);
              // 停止观察
              if (containerRef.current) {
                observer.unobserve(containerRef.current);
              }
            });
          }
        },
        { threshold: 0.1 } // 当 10% 的组件可见时触发
      );

      // 开始观察容器
      if (containerRef.current) {
        observer.observe(containerRef.current);
      }

      // 清理函数
      return () => {
        if (containerRef.current) {
          observer.unobserve(containerRef.current);
        }
      };
    }, [importFunc]);

    return (
      <div ref={containerRef} style={{ minHeight: '10px' }}>
        {Component ? <Component {...props} /> : <LoadingSpinner />}
      </div>
    );
  };
}
