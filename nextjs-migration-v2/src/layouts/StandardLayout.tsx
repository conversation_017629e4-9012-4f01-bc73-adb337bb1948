import React from 'react';
import { BaseLayout, type BaseLayoutProps } from './BaseLayout';
import { standardPageConfig } from './configs/layoutConfigs';

/**
 * 标准页面布局组件
 * 使用标准配置的基础布局，适用于大多数页面
 */
export const StandardLayout: React.FC<Omit<BaseLayoutProps, 'config'> & { 
    config?: Partial<typeof standardPageConfig> 
}> = ({ config, ...props }) => {
    const mergedConfig = {
        ...standardPageConfig,
        ...config
    };

    return <BaseLayout config={mergedConfig} {...props} />;
};

/**
 * 产品页面布局组件
 */
export const ProductLayout: React.FC<Omit<BaseLayoutProps, 'config'> & { 
    config?: Partial<typeof standardPageConfig> 
}> = ({ config, ...props }) => {
    const mergedConfig = {
        ...standardPageConfig,
        ...config,
        contactFloatBarConfig: {
            ...standardPageConfig.contactFloatBarConfig,
            themeColor: "#52c41a",
            top: "45%",
            ...config?.contactFloatBarConfig
        }
    };

    return <BaseLayout config={mergedConfig} {...props} />;
};

/**
 * 案例页面布局组件
 */
export const CaseLayout: React.FC<Omit<BaseLayoutProps, 'config'> & { 
    config?: Partial<typeof standardPageConfig> 
}> = ({ config, ...props }) => {
    const mergedConfig = {
        ...standardPageConfig,
        ...config,
        contactFloatBarConfig: {
            ...standardPageConfig.contactFloatBarConfig,
            themeColor: "#722ed1",
            top: "55%",
            ...config?.contactFloatBarConfig
        }
    };

    return <BaseLayout config={mergedConfig} {...props} />;
};

/**
 * 联系页面布局组件
 */
export const ContactLayout: React.FC<Omit<BaseLayoutProps, 'config'> & { 
    config?: Partial<typeof standardPageConfig> 
}> = ({ config, ...props }) => {
    const mergedConfig = {
        ...standardPageConfig,
        ...config,
        showContactFloatBar: false, // 联系页面不显示浮动联系条
        ...config
    };

    return <BaseLayout config={mergedConfig} {...props} />;
};

/**
 * 团队页面布局组件
 */
export const TeamLayoutNew: React.FC<Omit<BaseLayoutProps, 'config'> & { 
    config?: Partial<typeof standardPageConfig> 
}> = ({ config, ...props }) => {
    const mergedConfig = {
        ...standardPageConfig,
        ...config,
        contactFloatBarConfig: {
            ...standardPageConfig.contactFloatBarConfig,
            themeColor: "#1677ff",
            top: "50%",
            ...config?.contactFloatBarConfig
        }
    };

    return <BaseLayout config={mergedConfig} {...props} />;
};
