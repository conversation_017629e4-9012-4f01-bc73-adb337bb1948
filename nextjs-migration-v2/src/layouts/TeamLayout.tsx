import React, { useEffect } from "react";
import { Outlet, useLocation } from 'react-router-dom';
import { Navigation } from "../features/navigation";
import { PageFooter } from "../components/layout/PageFooter";
import { ErrorBoundary } from "../components/common/ErrorBoundary";
import { ContactFloatBar } from "../features/contact-float-bar";
import { defaultContactItems } from "../features/contact-float-bar/data/contactItems";
import "./TeamLayout.css"; // 导入样式

// 滚动到顶部组件
const ScrollToTop = () => {
    const { pathname } = useLocation();

    useEffect(() => {
        window.scrollTo(0, 0);
    }, [pathname]);

    return null;
};

export const TeamLayout = () => {
    return (
        <div className="d-flex flex-column min-vh-100">
            <ScrollToTop />
            <Navigation />
            <main id="main-content" tabIndex={-1} className="flex-grow-1 mt-5 pt-4">
                <ErrorBoundary>
                    <Outlet /> {/* 子路由内容将在这里渲染 */}
                </ErrorBoundary>
            </main>
            {/* 添加页脚，但不包含联系表单部分 */}
            <PageFooter recordNumbers={["沪ICP备2025123281号", "沪ICP备2025123281号-1"]} />
            <ContactFloatBar items={defaultContactItems} themeColor="#1890ff" top="50%" />
        </div>
    );
};