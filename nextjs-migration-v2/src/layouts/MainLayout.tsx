import React, { useEffect } from "react";
import { Outlet, useLocation } from 'react-router-dom';
import { Navigation } from "../features/navigation";
import { Header } from "../features/header";
import { Contact } from "../features/contact";
import { CookieConsent } from "../features/cookie-consent/index";
import { ContactFloatBar } from "../features/contact-float-bar";
import { defaultContactItems } from "../features/contact-float-bar/data/contactItems";

/**
 * 主布局组件属性接口
 */
interface MainLayoutProps {
  /** 数据对象 */
  data: Record<string, any>;
}

/**
 * 滚动到顶部组件
 * 当路由路径变化时，自动滚动到页面顶部
 *
 * @returns React组件
 */
const ScrollToTop: React.FC = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
};

/**
 * 主布局组件
 * 提供网站的主要布局结构，包括导航、头部、内容区域和页脚
 *
 * @param props - 主布局组件属性
 * @returns React组件
 */
export const MainLayout: React.FC<MainLayoutProps> = ({ data }) => {
  return (
    <div className="d-flex flex-column min-vh-100">
      <ScrollToTop />
      <Navigation />
      <Header data={data.Header} />
      <main id="main-content" tabIndex={-1} className="flex-grow-1">
        <Outlet /> {/* 子路由内容将在这里渲染 */}
      </main>
      <Contact data={data.Contact} />
      <CookieConsent />
      <ContactFloatBar items={defaultContactItems} themeColor="#1890ff" top="50%" />
    </div>
  );
};
