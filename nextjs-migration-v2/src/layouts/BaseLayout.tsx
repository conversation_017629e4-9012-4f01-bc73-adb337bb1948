import React, { useEffect } from "react";
import { Outlet, useLocation } from 'react-router-dom';
import { Navigation } from "../features/navigation";
import { PageFooter } from "../components/layout/PageFooter";
import { ErrorBoundary } from "../components/common/ErrorBoundary";
import { ContactFloatBar } from "../features/contact-float-bar";
import { defaultContactItems } from "../features/contact-float-bar/data/contactItems";
import type { ContactItem } from "../features/contact-float-bar/types";

// 滚动到顶部组件
const ScrollToTop = () => {
    const { pathname } = useLocation();

    useEffect(() => {
        window.scrollTo(0, 0);
    }, [pathname]);

    return null;
};

// 基础布局配置接口
export interface BaseLayoutConfig {
    /** 是否显示导航栏 */
    showNavigation?: boolean;
    /** 是否显示页脚 */
    showFooter?: boolean;
    /** 是否显示联系浮动条 */
    showContactFloatBar?: boolean;
    /** 是否显示错误边界 */
    showErrorBoundary?: boolean;
    /** 是否自动滚动到顶部 */
    autoScrollToTop?: boolean;
    /** 主内容区域的CSS类名 */
    mainClassName?: string;
    /** 容器的CSS类名 */
    containerClassName?: string;
    /** 页脚配置 */
    footerConfig?: {
        recordNumbers?: string[];
        [key: string]: any;
    };
    /** 联系浮动条配置 */
    contactFloatBarConfig?: {
        items?: ContactItem[];
        themeColor?: string;
        top?: string;
        [key: string]: any;
    };
}

// 基础布局组件属性接口
export interface BaseLayoutProps {
    /** 布局配置 */
    config?: BaseLayoutConfig;
    /** 子组件 */
    children?: React.ReactNode;
}

// 默认配置
const defaultConfig: Required<BaseLayoutConfig> = {
    showNavigation: true,
    showFooter: true,
    showContactFloatBar: true,
    showErrorBoundary: true,
    autoScrollToTop: true,
    mainClassName: "flex-grow-1 mt-5 pt-4",
    containerClassName: "d-flex flex-column min-vh-100",
    footerConfig: {
        recordNumbers: ["沪ICP备2025123281号", "沪ICP备2025123281号-1"]
    },
    contactFloatBarConfig: {
        items: defaultContactItems,
        themeColor: "#1890ff",
        top: "50%"
    }
};

/**
 * 基础布局组件
 * 提供可配置的页面布局结构
 */
export const BaseLayout: React.FC<BaseLayoutProps> = ({ 
    config = {}, 
    children 
}) => {
    // 合并配置
    const mergedConfig = {
        ...defaultConfig,
        ...config,
        footerConfig: {
            ...defaultConfig.footerConfig,
            ...config.footerConfig
        },
        contactFloatBarConfig: {
            ...defaultConfig.contactFloatBarConfig,
            ...config.contactFloatBarConfig
        }
    };

    const renderMainContent = () => {
        const content = children || <Outlet />;
        
        if (mergedConfig.showErrorBoundary) {
            return <ErrorBoundary>{content}</ErrorBoundary>;
        }
        
        return content;
    };

    return (
        <div className={mergedConfig.containerClassName}>
            {mergedConfig.autoScrollToTop && <ScrollToTop />}
            
            {mergedConfig.showNavigation && <Navigation />}
            
            <main 
                id="main-content" 
                tabIndex={-1} 
                className={mergedConfig.mainClassName}
            >
                {renderMainContent()}
            </main>
            
            {mergedConfig.showFooter && (
                <PageFooter {...mergedConfig.footerConfig} />
            )}
            
            {mergedConfig.showContactFloatBar && (
                <ContactFloatBar {...mergedConfig.contactFloatBarConfig} />
            )}
        </div>
    );
};
