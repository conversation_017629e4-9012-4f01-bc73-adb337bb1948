'use client';

import React, { useEffect } from "react";
import { usePathname } from 'next/navigation';
import { NextNavigation } from "../features/navigation/NextNavigation";
import { NextHeader } from "../features/header/NextHeader";
import { Contact } from "../features/contact";
import { CookieConsent } from "../features/cookie-consent/index";
import { ContactFloatBar } from "../features/contact-float-bar";
import { defaultContactItems } from "../features/contact-float-bar/data/contactItems";

/**
 * 主布局组件属性接口
 */
interface NextMainLayoutProps {
  /** 子组件 */
  children: React.ReactNode;
  /** 数据对象 */
  data?: Record<string, any>;
}

/**
 * 滚动到顶部组件
 * 当路由路径变化时，自动滚动到页面顶部
 *
 * @returns React组件
 */
const ScrollToTop: React.FC = () => {
  const pathname = usePathname();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
};

/**
 * Next.js 主布局组件
 * 提供网站的主要布局结构，包括导航、头部、内容区域和页脚
 * 完全保持原有的 MainLayout 功能和样式
 *
 * @param props - 主布局组件属性
 * @returns React组件
 */
export const NextMainLayout: React.FC<NextMainLayoutProps> = ({
  children,
  data = {
    Header: {},
    Contact: {}
  }
}) => {
  return (
    <div className="d-flex flex-column min-vh-100">
      <ScrollToTop />
      <NextNavigation />
      <NextHeader data={data.Header} />
      <main id="main-content" tabIndex={-1} className="flex-grow-1">
        {children} {/* Next.js 子组件将在这里渲染 */}
      </main>
      <Contact data={data.Contact} />
      <CookieConsent />
      <ContactFloatBar items={defaultContactItems} themeColor="#1890ff" top="50%" />
    </div>
  );
};
