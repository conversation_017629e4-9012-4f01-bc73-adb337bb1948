import React from 'react';
import { LoadingSpinner } from './LoadingSpinner';
import { LoadingProps } from './types';

/**
 * 统一的加载组件
 * 整合了 LoadingSpinner 的功能，提供更丰富的加载状态显示
 */
export const Loading: React.FC<LoadingProps> = ({
    placeholder = 'default',
    size = 'md',
    showSpinner = true,
    className
}) => {
    const getPlaceholderText = () => {
        const messages = {
            list: '正在加载优势列表...',
            paragraph: '正在加载内容描述...',
            default: '数据加载中...'
        };

        return messages[placeholder] || messages.default;
    };

    if (showSpinner) {
        return (
            <LoadingSpinner
                size={size}
                tip={getPlaceholderText()}
                className={className}
            />
        );
    }

    return <span className="text-muted">{getPlaceholderText()}</span>;
};