/**
 * 反馈组件类型定义
 */

/**
 * 简单加载旋转器属性接口
 */
export interface SimpleLoadingSpinnerProps {
  /** 尺寸，默认为'md' */
  size?: 'sm' | 'md' | 'lg';
  /** 颜色，默认为'primary' */
  color?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  /** 自定义类名 */
  className?: string;
}

/**
 * 加载旋转器属性接口
 */
export interface LoadingSpinnerProps {
  /** 尺寸，默认为'md' */
  size?: 'sm' | 'md' | 'lg';
  /** 颜色，默认为'primary' */
  color?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  /** 自定义类名 */
  className?: string;
}

/**
 * 加载提示属性接口
 */
export interface LoadingProps {
  /** 占位符类型 */
  placeholder?: 'list' | 'paragraph' | 'default';
  /** 尺寸，默认为'md' */
  size?: 'sm' | 'md' | 'lg';
  /** 是否显示旋转器，默认为true */
  showSpinner?: boolean;
  /** 自定义类名 */
  className?: string;
}
