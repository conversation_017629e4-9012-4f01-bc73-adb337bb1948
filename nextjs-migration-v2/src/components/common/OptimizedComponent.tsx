import React, { memo, useMemo, useCallback, forwardRef } from 'react';
import { usePerformanceMonitor } from '../../hooks/usePerformanceMonitor';
import { useErrorHandler } from '../../hooks/useErrorHandler';
import { ErrorBoundaryWithFallback } from './ErrorBoundary/ErrorBoundaryWithFallback';

/**
 * 优化组件的基础属性
 */
interface OptimizedComponentProps {
  /** 组件名称，用于性能监控 */
  componentName?: string;
  /** 是否启用性能监控 */
  enablePerformanceMonitor?: boolean;
  /** 是否启用错误边界 */
  enableErrorBoundary?: boolean;
  /** 是否启用错误处理 */
  enableErrorHandler?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 子组件 */
  children?: React.ReactNode;
}

/**
 * 优化的组件基类
 * 集成性能监控、错误处理和常用优化
 */
export const OptimizedComponent = memo(
  forwardRef<HTMLDivElement, OptimizedComponentProps>(
    (
      {
        componentName = 'OptimizedComponent',
        enablePerformanceMonitor = process.env.NODE_ENV === 'development',
        enableErrorBoundary = true,
        enableErrorHandler = true,
        className,
        children,
        ...props
      },
      ref
    ) => {
      // 性能监控
      const { metrics } = usePerformanceMonitor({
        componentName,
        enabled: enablePerformanceMonitor,
        verbose: false,
      });

      // 错误处理
      const { handleError } = useErrorHandler({
        showToast: enableErrorHandler,
        reportError: true,
        logError: true,
      });

      // 错误边界回调
      const handleErrorBoundary = useCallback(
        (error: Error, errorInfo: any) => {
          handleError(error);
        },
        [handleError]
      );

      // 渲染内容
      const renderContent = useMemo(() => {
        return (
          <div ref={ref} className={className} {...props}>
            {children}
          </div>
        );
      }, [ref, className, children, props]);

      // 如果启用错误边界，包装内容
      if (enableErrorBoundary) {
        return (
          <ErrorBoundaryWithFallback
            name={componentName}
            level="component"
            onError={handleErrorBoundary}
          >
            {renderContent}
          </ErrorBoundaryWithFallback>
        );
      }

      return renderContent;
    }
  )
);

OptimizedComponent.displayName = 'OptimizedComponent';

/**
 * 创建优化的组件 HOC
 */
export const createOptimizedComponent = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: {
    componentName?: string;
    enablePerformanceMonitor?: boolean;
    enableErrorBoundary?: boolean;
    enableErrorHandler?: boolean;
    memoCompare?: (prevProps: P, nextProps: P) => boolean;
  } = {}
) => {
  const {
    componentName,
    enablePerformanceMonitor = process.env.NODE_ENV === 'development',
    enableErrorBoundary = true,
    enableErrorHandler = true,
    memoCompare,
  } = options;

  const OptimizedWrappedComponent = memo(
    forwardRef<any, P>((props, ref) => {
      const displayName = componentName || WrappedComponent.displayName || WrappedComponent.name;

      // 性能监控
      usePerformanceMonitor({
        componentName: displayName,
        enabled: enablePerformanceMonitor,
      });

      // 错误处理
      const { handleError } = useErrorHandler({
        showToast: enableErrorHandler,
        reportError: true,
        logError: true,
      });

      // 错误边界回调
      const handleErrorBoundary = useCallback(
        (error: Error, errorInfo: any) => {
          handleError(error);
        },
        [handleError]
      );

      // 渲染组件
      const renderComponent = useMemo(() => {
        return <WrappedComponent ref={ref} {...props} />;
      }, [ref, props]);

      // 如果启用错误边界，包装组件
      if (enableErrorBoundary) {
        return (
          <ErrorBoundaryWithFallback
            name={displayName}
            level="component"
            onError={handleErrorBoundary}
          >
            {renderComponent}
          </ErrorBoundaryWithFallback>
        );
      }

      return renderComponent;
    }),
    memoCompare
  );

  OptimizedWrappedComponent.displayName = `OptimizedComponent(${componentName || WrappedComponent.displayName || WrappedComponent.name})`;

  return OptimizedWrappedComponent;
};

/**
 * 性能优化的 Hook 集合
 */
export const useOptimizedCallbacks = <T extends Record<string, any>>(
  callbacks: T,
  deps: React.DependencyList
): T => {
  return useMemo(() => {
    const optimizedCallbacks = {} as T;
    
    Object.keys(callbacks).forEach((key) => {
      const callback = callbacks[key];
      if (typeof callback === 'function') {
        optimizedCallbacks[key] = useCallback(callback, deps);
      } else {
        optimizedCallbacks[key] = callback;
      }
    });
    
    return optimizedCallbacks;
  }, deps);
};

/**
 * 性能优化的值缓存 Hook
 */
export const useOptimizedValues = <T extends Record<string, any>>(
  values: T,
  deps: React.DependencyList
): T => {
  return useMemo(() => values, deps);
};

/**
 * 防抖 Hook
 */
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = React.useState<T>(value);

  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * 节流 Hook
 */
export const useThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const lastRun = React.useRef(Date.now());

  return useCallback(
    ((...args) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    }) as T,
    [callback, delay]
  );
};
