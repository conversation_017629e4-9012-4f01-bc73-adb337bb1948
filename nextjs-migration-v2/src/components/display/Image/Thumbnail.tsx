import React from 'react';
import { ThumbnailProps } from './types';
import './image.css';

/**
 * 缩略图组件
 * 显示带有悬停效果的图片缩略图
 * 
 * @param props - 缩略图组件属性
 * @returns React组件
 */
export const Thumbnail: React.FC<ThumbnailProps> = ({ title, imageSrc, onClick }) => (
    <div className="portfolio-item position-relative overflow-hidden rounded-3 shadow-hover">
        <div
            className="hover-overlay w-100 h-100 position-absolute top-0 start-0"
            onClick={onClick}
            role="button"
            tabIndex={0}
            aria-label={`查看大图：${title}`}
            style={{ cursor: "pointer" }}
            onKeyDown={(e) => {
                // 添加键盘可访问性
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    onClick();
                }
            }}
        >
            <div className="hover-content w-100 h-100 d-flex align-items-center justify-content-center bg-dark bg-opacity-50 opacity-0 transition-opacity">
                <h4 className="text-white mb-0">{title}</h4>
            </div>
        </div>
        <img
            src={imageSrc}
            className="img-fluid"
            alt={title}
            loading="lazy"
            decoding="async"
        />
    </div>
);
