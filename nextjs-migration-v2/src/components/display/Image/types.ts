/**
 * 图片组件类型定义
 */

/**
 * 图片组件属性接口
 */
export interface ImageProps {
  /** 图片标题 */
  title: string;
  /** 大图URL */
  largeImage: string;
  /** 缩略图URL */
  smallImage: string;
}

/**
 * 缩略图组件属性接口
 */
export interface ThumbnailProps {
  /** 图片标题 */
  title: string;
  /** 图片URL */
  imageSrc: string;
  /** 点击事件处理函数 */
  onClick: () => void;
}

/**
 * 图片模态框属性接口
 */
export interface ImageModalProps {
  /** 是否显示模态框 */
  show: boolean;
  /** 图片标题 */
  title: string;
  /** 图片URL */
  imageSrc: string;
  /** 缩放比例 */
  scale: number;
  /** 关闭事件处理函数 */
  onClose: () => void;
  /** 缩放事件处理函数 */
  onZoom: () => void;
}

/**
 * 图片缩放钩子返回值接口
 */
export interface ImageZoomHookResult {
  /** 当前缩放比例 */
  scale: number;
  /** 切换缩放状态的函数 */
  toggleZoom: () => void;
}

/**
 * 键盘控制钩子类型
 */
export type KeyboardControlHandler = (e: KeyboardEvent) => void;
