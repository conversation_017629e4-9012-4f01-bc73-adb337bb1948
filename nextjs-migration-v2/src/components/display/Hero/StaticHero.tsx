import React from 'react';
import './StaticHero.css';

interface HeroItem {
    id: number;
    webp: string;
    fallback: string;
    alt: string;
    title: string;
    description: string;
    buttonText?: string;
    buttonLink?: string;
}

interface StaticHeroProps {
    items: HeroItem[];
}

export const StaticHero: React.FC<StaticHeroProps> = ({items}) => {
    // Just display the first item as a static hero
    const item = items[0];

    return (
        <div className="static-hero-container">
            <div className="hero-content">
                <picture>
                    <source srcSet={item.webp} type="image/webp"/>
                    <img
                        src={item.fallback}
                        alt={item.alt}
                        className="hero-image"
                        loading="eager"
                        decoding="async"
                    />
                </picture>

                <div className="hero-caption">
                    <h3 data-aos="fade-right">{item.title}</h3>
                    <p data-aos="fade-left" data-aos-delay="200">{item.description}</p>
                    {item.buttonText && (
                        <div className="hero-button" data-aos="fade-up" data-aos-delay="300">
                            <a
                                className="btn btn-primary btn-lg rounded-pill"
                                href={item.buttonLink || "#about"}
                            >
                                {item.buttonText} <i className="fas fa-arrow-right ms-2"></i>
                            </a>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};