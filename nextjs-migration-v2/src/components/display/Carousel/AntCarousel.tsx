import * as React from 'react';
import { useEffect, useState } from 'react';
import { Carousel } from 'antd';
import { Button } from 'antd';
import { ArrowRightOutlined } from '@ant-design/icons';
import { CarouselProps, CarouselItem } from './types';
import { carouselService } from './carouselService';
import './AntCarousel.css';

/**
 * Ant Design轮播组件
 * 基于Ant Design的Carousel组件，支持WebP格式和响应式设计
 *
 * @param props - 轮播组件属性
 * @returns React组件
 */
export const AntCarousel: React.FC<CarouselProps> = ({
  items: initialItems,
  controls = true,
  indicators = true,
  interval = 5000,
  pause = false,
  className: _className = "ant-carousel"
}) => {
  // 状态管理
  const [items, setItems] = useState<CarouselItem[]>(initialItems);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // 如果没有提供初始数据，尝试从服务获取
  useEffect(() => {
    if (!initialItems || initialItems.length === 0) {
      setLoading(true);
      carouselService.getCarouselItems()
        .then(data => {
          setItems(data);
          setError(null);
        })
        .catch(err => {
          console.error('加载轮播数据失败:', err);
          setError('无法加载轮播数据');
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [initialItems]);

  // 轮播图设置
  const settings = {
    dots: indicators,
    autoplay: true,
    autoplaySpeed: interval,
    pauseOnHover: pause === 'hover',
    effect: 'fade' as const, // 使用 as const 断言为字面量类型
    arrows: controls,
  };

  // 加载中状态
  if (loading) {
    return (
      <div className="ant-carousel-container d-flex justify-content-center align-items-center">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">加载中...</span>
        </div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="ant-carousel-container d-flex justify-content-center align-items-center">
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      </div>
    );
  }

  // 没有数据
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <div className="ant-carousel-container">
      <Carousel {...settings}>
        {items.map((item) => (
          <div key={item.id} className="carousel-slide">
            <div className="slide-content">
              <picture>
                <source srcSet={item.webp} type="image/webp" />
                <img
                  src={item.fallback}
                  alt={item.alt}
                  className="slide-image"
                  loading={item.loading}
                  decoding="async"
                />
              </picture>

              <div className="slide-caption">
                {item.buttonText && (
                  <div className="slide-button" data-aos="fade-up" data-aos-delay="300">
                    <Button
                      type="primary"
                      shape="round"
                      size="large"
                      href={item.buttonLink || "#about"}
                    >
                      {item.buttonText} <ArrowRightOutlined />
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </Carousel>
    </div>
  );
};