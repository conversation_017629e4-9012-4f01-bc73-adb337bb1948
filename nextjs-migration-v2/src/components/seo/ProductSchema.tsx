import React from 'react';
import { Helmet } from 'react-helmet-async';

interface ProductSchemaProps {
  name: string;
  description: string;
  image: string;
  sku?: string;
  brand?: string;
  category?: string;
  offers?: {
    price?: number;
    priceCurrency?: string;
    availability?: string;
  };
}

/**
 * 产品结构化数据组件
 * 添加符合Schema.org标准的产品信息，提升搜索引擎展示效果
 * 
 * @param props - 产品结构化数据属性
 * @returns React组件
 */
export const ProductSchema: React.FC<ProductSchemaProps> = ({
  name,
  description,
  image,
  sku,
  brand = '上海寒链实业有限公司',
  category,
  offers
}) => {
  const schemaData = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name,
    description,
    image,
    ...(sku && { sku }),
    brand: {
      '@type': 'Brand',
      name: brand
    },
    ...(category && { category }),
    ...(offers && {
      offers: {
        '@type': 'Offer',
        price: offers.price,
        priceCurrency: offers.priceCurrency || 'CNY',
        availability: offers.availability || 'https://schema.org/InStock'
      }
    })
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(schemaData)}
      </script>
    </Helmet>
  );
};

export default ProductSchema;
