import React from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOProps {
  title: string;
  description: string;
  keywords?: string;
  canonicalUrl?: string;
  ogType?: 'website' | 'article' | 'product';
  ogImage?: string;
  ogImageAlt?: string;
  ogImageWidth?: number;
  ogImageHeight?: number;
  twitterCard?: 'summary' | 'summary_large_image';
  twitterSite?: string;
  twitterCreator?: string;
  noIndex?: boolean;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  language?: string;
  structuredData?: Record<string, any>;
  children?: React.ReactNode;
}

/**
 * SEO组件 - 统一管理页面元数据
 *
 * @param props - SEO组件属性
 * @returns React组件
 */
export const SEO: React.FC<SEOProps> = ({
  title,
  description,
  keywords,
  canonicalUrl,
  ogType = 'website',
  ogImage = '/img/og-image.jpg',
  ogImageAlt,
  ogImageWidth = 1200,
  ogImageHeight = 630,
  twitterCard = 'summary_large_image',
  twitterSite = '@hanchain',
  twitterCreator = '@hanchain',
  noIndex = false,
  author = '上海寒链实业有限公司',
  publishedTime,
  modifiedTime,
  language = 'zh-Hans',
  structuredData,
  children
}) => {
  // 构建完整标题
  const fullTitle = title.includes('上海寒链实业有限公司')
    ? title
    : `${title} | 上海寒链实业有限公司`;

  // 当前URL
  const currentUrl = typeof window !== 'undefined' ? window.location.href : '';
  const url = canonicalUrl || currentUrl;

  return (
    <Helmet prioritizeSeoTags>
      {/* 基本元数据 */}
      <html lang={language} />
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      {keywords && <meta name="keywords" content={keywords} />}
      {author && <meta name="author" content={author} />}

      {/* 规范链接 */}
      <link rel="canonical" href={url} />

      {/* 索引控制 */}
      {noIndex && <meta name="robots" content="noindex, nofollow" />}
      {!noIndex && <meta name="robots" content="index, follow" />}

      {/* Open Graph / Facebook */}
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={url} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:image:width" content={String(ogImageWidth)} />
      <meta property="og:image:height" content={String(ogImageHeight)} />
      {ogImageAlt && <meta property="og:image:alt" content={ogImageAlt} />}
      <meta property="og:locale" content="zh_CN" />
      <meta property="og:site_name" content="上海寒链实业有限公司" />
      {publishedTime && <meta property="article:published_time" content={publishedTime} />}
      {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}

      {/* Twitter */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:site" content={twitterSite} />
      <meta name="twitter:creator" content={twitterCreator} />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImage} />
      {ogImageAlt && <meta name="twitter:image:alt" content={ogImageAlt} />}

      {/* 移动优化 */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="theme-color" content="#1890ff" />
      <meta name="format-detection" content="telephone=no" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />

      {/* 百度站长工具验证 */}
      <meta name="baidu-site-verification" content="code-xxxxxxxxxx" />

      {/* 结构化数据 */}
      {structuredData && (
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      )}

      {/* 允许传入额外的元数据 */}
      {children}
    </Helmet>
  );
};

export default SEO;
