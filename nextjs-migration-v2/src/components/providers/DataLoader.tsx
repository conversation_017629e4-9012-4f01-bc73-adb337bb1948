import React, { useState, useEffect } from 'react';
import { LoadingSpinner } from '../feedback/LoadingSpinner';
import JsonData from '../../data/data.json';

interface DataLoaderProps {
  children: (data: any) => React.ReactNode;
  onError?: (error: Error) => void;
}

/**
 * 数据加载组件
 * 负责应用程序初始数据的加载和错误处理
 */
export const DataLoader: React.FC<DataLoaderProps> = ({ children, onError }) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        // 模拟异步数据加载
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 在实际应用中，这里可能是从 API 获取初始数据
        setData(JsonData);
      } catch (err) {
        const error = err instanceof Error ? err : new Error('数据加载失败');
        setError(error);
        onError?.(error);
        console.error('Failed to load initial data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [onError]);

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <LoadingSpinner size="lg" tip="正在加载应用数据..." />
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        flexDirection: 'column',
        gap: '16px'
      }}>
        <div style={{ color: '#ff4d4f', fontSize: '18px' }}>
          数据加载失败
        </div>
        <div style={{ color: '#666', fontSize: '14px' }}>
          {error.message}
        </div>
        <button 
          onClick={() => window.location.reload()}
          style={{
            padding: '8px 16px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          重新加载
        </button>
      </div>
    );
  }

  return <>{children(data)}</>;
};
