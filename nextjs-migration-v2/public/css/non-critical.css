/* 非关键CSS，延迟加载 */

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 悬停效果 */
.hover-effect:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* 打印样式 */
@media print {
  header, footer, .no-print {
    display: none;
  }
  
  body {
    font-size: 12pt;
    color: #000;
    background: #fff;
  }
  
  a {
    text-decoration: none;
    color: #000;
  }
  
  .container {
    width: 100%;
    margin: 0;
    padding: 0;
  }
}

/* 高对比度模式 */
@media (forced-colors: active) {
  .btn, 
  .nav-link,
  .card {
    border: 1px solid transparent;
  }
}
