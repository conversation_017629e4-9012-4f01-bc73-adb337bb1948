/* 可访问性样式 */

/* 跳过导航链接 */
.skip-nav {
  position: absolute;
  top: -40px;
  left: 0;
  background: #1890ff;
  color: white;
  padding: 8px;
  z-index: 100;
  transition: top 0.2s;
}

.skip-nav:focus {
  top: 0;
}

/* 屏幕阅读器专用文本 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* 焦点样式 */
:focus {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

:focus-visible {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

/* 高对比度模式支持 */
@media (forced-colors: active) {
  .btn, 
  .nav-link,
  .card {
    border: 1px solid transparent;
  }
}
