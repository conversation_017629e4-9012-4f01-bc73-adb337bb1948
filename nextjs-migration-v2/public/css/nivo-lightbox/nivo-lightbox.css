/*
 * Nivo Lightbox v1.2.0
 * http://dev7studios.com/nivo-lightbox
 *
 * Copyright 2013, Dev7studios
 * Free to use and abuse under the MIT license.
 * http://www.opensource.org/licenses/mit-license.php
 */

.nivo-lightbox-overlay {
	position: fixed;
	top: 0;
	left: 0;
	z-index: 99998;
	width: 100%;
	height: 100%;
	overflow: hidden;
	visibility: hidden;
	opacity: 0;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
.nivo-lightbox-overlay.nivo-lightbox-open {
	visibility: visible;
	opacity: 1;
}
.nivo-lightbox-wrap  {
	position: absolute;
	top: 10%;
	bottom: 10%;
	left: 10%;
	right: 10%;
}
.nivo-lightbox-content {
	width: 100%;
	height: 100%;
}
.nivo-lightbox-title-wrap {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	z-index: 99999;
	text-align: center;
}
.nivo-lightbox-nav { display: none; }
.nivo-lightbox-prev {
	position: absolute;
	top: 50%;
	left: 0;
}
.nivo-lightbox-next {
	position: absolute;
	top: 50%;
	right: 0;
}
.nivo-lightbox-close {
	position: absolute;
	top: 2%;
	right: 2%;
}

.nivo-lightbox-image { text-align: center; }
.nivo-lightbox-image img {
	max-width: 100%;
	max-height: 100%;
	width: auto;
	height: auto;
	vertical-align: middle;
}
.nivo-lightbox-content iframe {
	width: 100%;
	height: 100%;
}
.nivo-lightbox-inline,
.nivo-lightbox-ajax {
	max-height: 100%;
	overflow: auto;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	/* https://bugzilla.mozilla.org/show_bug.cgi?id=308801 */
}
.nivo-lightbox-error {
	display: table;
	text-align: center;
	width: 100%;
	height: 100%;
	color: #fff;
	text-shadow: 0 1px 1px #000;
}
.nivo-lightbox-error p {
	display: table-cell;
	vertical-align: middle;
}

/* Effects
 **********************************************/
.nivo-lightbox-notouch .nivo-lightbox-effect-fade,
.nivo-lightbox-notouch .nivo-lightbox-effect-fadeScale,
.nivo-lightbox-notouch .nivo-lightbox-effect-slideLeft,
.nivo-lightbox-notouch .nivo-lightbox-effect-slideRight,
.nivo-lightbox-notouch .nivo-lightbox-effect-slideUp,
.nivo-lightbox-notouch .nivo-lightbox-effect-slideDown,
.nivo-lightbox-notouch .nivo-lightbox-effect-fall {
	-webkit-transition: all 0.2s ease-in-out;
	   -moz-transition: all 0.2s ease-in-out;
	    -ms-transition: all 0.2s ease-in-out;
	     -o-transition: all 0.2s ease-in-out;
	        transition: all 0.2s ease-in-out;
}

/* fadeScale */
.nivo-lightbox-effect-fadeScale .nivo-lightbox-wrap {
	-webkit-transition: all 0.3s;
	   -moz-transition: all 0.3s;
	    -ms-transition: all 0.3s;
	     -o-transition: all 0.3s;
	        transition: all 0.3s;
	-webkit-transform: scale(0.7);
	   -moz-transform: scale(0.7);
	    -ms-transform: scale(0.7);
	        transform: scale(0.7);
}
.nivo-lightbox-effect-fadeScale.nivo-lightbox-open .nivo-lightbox-wrap {
	-webkit-transform: scale(1);
	   -moz-transform: scale(1);
	    -ms-transform: scale(1);
	        transform: scale(1);
}

/* slideLeft / slideRight / slideUp / slideDown */
.nivo-lightbox-effect-slideLeft .nivo-lightbox-wrap,
.nivo-lightbox-effect-slideRight .nivo-lightbox-wrap,
.nivo-lightbox-effect-slideUp .nivo-lightbox-wrap,
.nivo-lightbox-effect-slideDown .nivo-lightbox-wrap {
	-webkit-transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
	   -moz-transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
	    -ms-transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
	     -o-transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
	        transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
}
.nivo-lightbox-effect-slideLeft .nivo-lightbox-wrap {
	-webkit-transform: translateX(-10%);
	   -moz-transform: translateX(-10%);
	    -ms-transform: translateX(-10%);
	        transform: translateX(-10%);
}
.nivo-lightbox-effect-slideRight .nivo-lightbox-wrap {
	-webkit-transform: translateX(10%);
	   -moz-transform: translateX(10%);
	    -ms-transform: translateX(10%);
	        transform: translateX(10%);
}
.nivo-lightbox-effect-slideLeft.nivo-lightbox-open .nivo-lightbox-wrap,
.nivo-lightbox-effect-slideRight.nivo-lightbox-open .nivo-lightbox-wrap {
	-webkit-transform: translateX(0);
	   -moz-transform: translateX(0);
	    -ms-transform: translateX(0);
	        transform: translateX(0);
}
.nivo-lightbox-effect-slideDown .nivo-lightbox-wrap {
	-webkit-transform: translateY(-10%);
	   -moz-transform: translateY(-10%);
	    -ms-transform: translateY(-10%);
	        transform: translateY(-10%);
}
.nivo-lightbox-effect-slideUp .nivo-lightbox-wrap {
	-webkit-transform: translateY(10%);
	   -moz-transform: translateY(10%);
	    -ms-transform: translateY(10%);
	        transform: translateY(10%);
}
.nivo-lightbox-effect-slideUp.nivo-lightbox-open .nivo-lightbox-wrap,
.nivo-lightbox-effect-slideDown.nivo-lightbox-open .nivo-lightbox-wrap {
	-webkit-transform: translateY(0);
	   -moz-transform: translateY(0);
	    -ms-transform: translateY(0);
	        transform: translateY(0);
}

/* fall */
.nivo-lightbox-body-effect-fall .nivo-lightbox-effect-fall {
	-webkit-perspective: 1000px;
	   -moz-perspective: 1000px;
	        perspective: 1000px;
}
.nivo-lightbox-effect-fall .nivo-lightbox-wrap {
	-webkit-transition: all 0.3s ease-out;
	   -moz-transition: all 0.3s ease-out;
	    -ms-transition: all 0.3s ease-out;
	     -o-transition: all 0.3s ease-out;
	        transition: all 0.3s ease-out;
	-webkit-transform: translateZ(300px);
	   -moz-transform: translateZ(300px);
	    -ms-transform: translateZ(300px);
	        transform: translateZ(300px);
}
.nivo-lightbox-effect-fall.nivo-lightbox-open .nivo-lightbox-wrap {
	-webkit-transform: translateZ(0);
	   -moz-transform: translateZ(0);
	    -ms-transform: translateZ(0);
	        transform: translateZ(0);
}
