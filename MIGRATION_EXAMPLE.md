# TeamLayout 迁移实例

## 🎯 迁移目标

将当前使用 TeamLayout 的 4 个页面迁移到新的可配置布局系统，实现：
- 每个页面独立配置
- 避免相互影响
- 提高可维护性

## 📋 当前使用 TeamLayout 的页面

1. **团队页面** (`/team`) - 蓝色主题，标准配置
2. **产品页面** (`/product`) - 绿色主题，位置稍低
3. **案例展示页面** (`/cases`) - 紫色主题，位置稍高
4. **联系页面** (`/contact`) - 无浮动条

## 🔄 迁移步骤

### 步骤 1: 更新路由配置

**当前路由配置 (src/routes/index.tsx):**
```typescript
// 所有页面都使用相同的 TeamLayout
<Route path="/team" element={<TeamLayout />}>
<Route path="/product" element={<TeamLayout />}>
<Route path="/cases" element={<TeamLayout />}>
<Route path="/contact" element={<TeamLayout />}>
```

**新的路由配置:**
```typescript
import { 
    ProductLayout, 
    CaseLayout, 
    ContactLayout, 
    TeamLayoutNew 
} from '@/layouts';

// 每个页面使用专门的布局
<Route path="/team" element={<TeamLayoutNew />}>
<Route path="/product" element={<ProductLayout />}>
<Route path="/cases" element={<CaseLayout />}>
<Route path="/contact" element={<ContactLayout />}>
```

### 步骤 2: 渐进式迁移

**方案 A: 一次性迁移**
```bash
# 直接替换所有路由配置
cp src/routes/index.tsx src/routes/index.backup.tsx
cp src/routes/index.new.tsx src/routes/index.tsx
```

**方案 B: 逐页面迁移**
```typescript
// 先迁移一个页面测试
<Route path="/product" element={<ProductLayout />}>
// 其他页面保持原样
<Route path="/team" element={<TeamLayout />}>
<Route path="/cases" element={<TeamLayout />}>
<Route path="/contact" element={<TeamLayout />}>
```

### 步骤 3: 验证迁移效果

**检查项目:**
- [ ] 页面布局正常显示
- [ ] 联系浮动条主题色正确
- [ ] 联系浮动条位置正确
- [ ] 页脚显示正常
- [ ] 导航栏功能正常
- [ ] 响应式布局正常

## 🎨 配置对比表

| 页面 | 原配置 | 新配置 | 主要差异 |
|------|--------|--------|----------|
| 团队 | 硬编码 | `TeamLayoutNew` | 蓝色主题 (#1677ff) |
| 产品 | 硬编码 | `ProductLayout` | 绿色主题 (#52c41a), top: 45% |
| 案例 | 硬编码 | `CaseLayout` | 紫色主题 (#722ed1), top: 55% |
| 联系 | 硬编码 | `ContactLayout` | 无浮动条 |

## 🧪 测试用例

### 1. 基础功能测试
```typescript
// 测试每个布局组件是否正常渲染
describe('Layout Components', () => {
  test('ProductLayout renders correctly', () => {
    render(<ProductLayout><div>Test</div></ProductLayout>);
    expect(screen.getByText('Test')).toBeInTheDocument();
  });
  
  test('CaseLayout renders with correct theme', () => {
    render(<CaseLayout />);
    // 验证紫色主题
  });
});
```

### 2. 配置测试
```typescript
// 测试自定义配置是否生效
test('Custom config overrides default', () => {
  const customConfig = {
    contactFloatBarConfig: { themeColor: '#ff0000' }
  };
  render(<ProductLayout config={customConfig} />);
  // 验证红色主题生效
});
```

## 🔧 故障排除

### 问题 1: 样式不一致
**症状:** 新布局的样式与原来不同
**解决:** 检查 CSS 类名和样式文件导入

### 问题 2: 浮动条位置错误
**症状:** 联系浮动条位置不正确
**解决:** 检查 `top` 配置值

### 问题 3: 页脚配置错误
**症状:** 备案号显示错误
**解决:** 检查 `footerConfig.recordNumbers` 配置

## 📊 迁移前后对比

### 维护性对比
| 方面 | 迁移前 | 迁移后 |
|------|--------|--------|
| 修改影响范围 | 4个页面 | 单个页面 |
| 配置方式 | 硬编码 | 可配置 |
| 代码复用 | 低 | 高 |
| 测试难度 | 高 | 低 |

### 性能对比
| 指标 | 迁移前 | 迁移后 | 变化 |
|------|--------|--------|------|
| 构建时间 | 9.18s | 9.18s | 无变化 |
| 包大小 | 500.87 kB | 500.87 kB | 无变化 |
| 运行时性能 | 正常 | 正常 | 无影响 |

## 🚀 迁移后的优势

### 1. 独立配置
```typescript
// 产品页面可以独立调整
<ProductLayout 
  config={{
    contactFloatBarConfig: {
      themeColor: "#custom-green",
      top: "40%"
    }
  }}
/>
```

### 2. 灵活扩展
```typescript
// 可以轻松添加新的页面类型
<BaseLayout 
  config={{
    showNavigation: false,
    showFooter: false,
    mainClassName: "fullscreen-layout"
  }}
>
  <SpecialPage />
</BaseLayout>
```

### 3. 易于测试
```typescript
// 每个布局组件可以独立测试
test('ProductLayout specific features', () => {
  // 只测试产品页面相关功能
});
```

## 📝 迁移检查清单

- [ ] 备份原始路由配置
- [ ] 更新路由配置使用新布局组件
- [ ] 测试每个页面的显示效果
- [ ] 验证联系浮动条配置
- [ ] 检查页脚配置
- [ ] 测试响应式布局
- [ ] 验证错误边界功能
- [ ] 更新相关文档
- [ ] 通知团队成员变更

## 🎯 总结

通过这次迁移，我们实现了：
- ✅ 解决了 4 个页面共享布局的问题
- ✅ 提供了灵活的配置系统
- ✅ 保持了向后兼容性
- ✅ 提高了代码的可维护性
- ✅ 为未来扩展奠定了基础

新的布局系统让每个页面都能独立配置，同时保持了代码的复用性和一致性。
