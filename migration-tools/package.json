{"name": "migration-tools", "version": "1.0.0", "description": "Next.js 迁移对比测试工具", "main": "monitor.ts", "scripts": {"build": "tsc", "monitor:full": "ts-node monitor.ts full", "monitor:quick": "ts-node monitor.ts quick", "monitor:watch": "ts-node monitor.ts watch", "test:home": "ts-node monitor.ts quick /", "test:about": "ts-node monitor.ts quick /about", "test:contact": "ts-node monitor.ts quick /contact", "test:products": "ts-node monitor.ts quick /products", "setup": "mkdir -p migration-reports/screenshots migration-reports/diffs", "clean": "rm -rf migration-reports/*", "install-deps": "npm install playwright pixelmatch pngjs @types/node typescript ts-node"}, "dependencies": {"playwright": "^1.40.0", "pixelmatch": "^5.3.0", "pngjs": "^7.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/pixelmatch": "^5.2.0", "@types/pngjs": "^6.0.0", "typescript": "^5.0.0", "ts-node": "^10.9.0"}, "keywords": ["migration", "testing", "comparison", "nextjs", "vite"], "author": "Frost Chain Team", "license": "MIT"}