#!/usr/bin/env node

/**
 * 快速测试脚本
 * 简单验证两个版本的页面是否都能正常访问
 */

import { chromium } from 'playwright';

async function quickTest() {
  console.log('🚀 开始快速测试...');
  
  const browser = await chromium.launch({ headless: true });
  
  try {
    // 测试原项目
    console.log('📄 测试原项目 (Vite)...');
    const vitePage = await browser.newPage();
    
    try {
      await vitePage.goto('http://localhost:3010/about', { 
        waitUntil: 'networkidle',
        timeout: 10000 
      });
      console.log('✅ 原项目关于页面访问成功');
    } catch (error) {
      console.log('❌ 原项目关于页面访问失败:', error.message);
    }
    
    await vitePage.close();
    
    // 测试新项目
    console.log('📄 测试新项目 (Next.js)...');
    const nextjsPage = await browser.newPage();
    
    try {
      await nextjsPage.goto('http://localhost:3000/about', { 
        waitUntil: 'networkidle',
        timeout: 10000 
      });
      console.log('✅ 新项目关于页面访问成功');
      
      // 检查页面标题
      const title = await nextjsPage.title();
      console.log(`📋 页面标题: ${title}`);
      
      // 检查主要内容
      const mainHeading = await nextjsPage.textContent('h1');
      console.log(`📝 主标题: ${mainHeading}`);
      
    } catch (error) {
      console.log('❌ 新项目关于页面访问失败:', error.message);
    }
    
    await nextjsPage.close();
    
  } finally {
    await browser.close();
  }
  
  console.log('✅ 快速测试完成');
}

// 运行测试
quickTest().catch(console.error);
