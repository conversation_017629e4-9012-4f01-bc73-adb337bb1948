/**
 * 迁移对比测试工具
 * 自动化对比原项目和新项目的功能、样式、性能
 */

import { chromium, Browser, Page } from 'playwright';
import pixelmatch from 'pixelmatch';
import { PNG } from 'pngjs';
import fs from 'fs/promises';
import path from 'path';

// 测试配置
interface TestConfig {
  viteUrl: string;
  nextjsUrl: string;
  outputDir: string;
  viewport: { width: number; height: number };
  threshold: number; // 图片对比阈值
}

// 测试结果
interface ComparisonResult {
  route: string;
  timestamp: string;
  functional: {
    passed: boolean;
    errors: string[];
  };
  visual: {
    passed: boolean;
    diffPercentage: number;
    diffImagePath?: string;
  };
  performance: {
    vite: PerformanceMetrics;
    nextjs: PerformanceMetrics;
    improvements: Record<string, number>;
  };
}

interface PerformanceMetrics {
  FCP: number;
  LCP: number;
  TTFB: number;
  loadTime: number;
  memoryUsage: number;
}

// 测试页面配置
interface TestPage {
  route: string;
  name: string;
  actions?: (page: Page) => Promise<void>;
  selectors?: string[];
  skipVisual?: boolean;
}

export class MigrationComparisonTool {
  private browser: Browser | null = null;
  private config: TestConfig;

  constructor(config: TestConfig) {
    this.config = config;
  }

  async init(): Promise<void> {
    this.browser = await chromium.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    // 确保输出目录存在
    await fs.mkdir(this.config.outputDir, { recursive: true });
    await fs.mkdir(path.join(this.config.outputDir, 'screenshots'), { recursive: true });
    await fs.mkdir(path.join(this.config.outputDir, 'diffs'), { recursive: true });
  }

  async close(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
    }
  }

  /**
   * 对比单个页面
   */
  async comparePage(testPage: TestPage): Promise<ComparisonResult> {
    if (!this.browser) throw new Error('Browser not initialized');

    console.log(`🔍 开始对比页面: ${testPage.name} (${testPage.route})`);

    const timestamp = new Date().toISOString();
    const result: ComparisonResult = {
      route: testPage.route,
      timestamp,
      functional: { passed: true, errors: [] },
      visual: { passed: true, diffPercentage: 0 },
      performance: {
        vite: {} as PerformanceMetrics,
        nextjs: {} as PerformanceMetrics,
        improvements: {},
      },
    };

    try {
      // 创建两个页面实例
      const vitePage = await this.browser.newPage();
      const nextjsPage = await this.browser.newPage();

      // 设置视口
      await vitePage.setViewport(this.config.viewport);
      await nextjsPage.setViewport(this.config.viewport);

      // 功能对比测试
      await this.runFunctionalComparison(vitePage, nextjsPage, testPage, result);

      // 视觉对比测试
      if (!testPage.skipVisual) {
        await this.runVisualComparison(vitePage, nextjsPage, testPage, result);
      }

      // 性能对比测试
      await this.runPerformanceComparison(vitePage, nextjsPage, testPage, result);

      await vitePage.close();
      await nextjsPage.close();

      console.log(`✅ 页面对比完成: ${testPage.name}`);
    } catch (error) {
      console.error(`❌ 页面对比失败: ${testPage.name}`, error);
      result.functional.passed = false;
      result.functional.errors.push(`对比过程出错: ${error}`);
    }

    return result;
  }

  /**
   * 功能对比测试
   */
  private async runFunctionalComparison(
    vitePage: Page,
    nextjsPage: Page,
    testPage: TestPage,
    result: ComparisonResult
  ): Promise<void> {
    try {
      // 访问页面
      const viteUrl = `${this.config.viteUrl}${testPage.route}`;
      const nextjsUrl = `${this.config.nextjsUrl}${testPage.route}`;

      await Promise.all([
        vitePage.goto(viteUrl, { waitUntil: 'networkidle' }),
        nextjsPage.goto(nextjsUrl, { waitUntil: 'networkidle' }),
      ]);

      // 执行自定义操作
      if (testPage.actions) {
        await Promise.all([
          testPage.actions(vitePage),
          testPage.actions(nextjsPage),
        ]);
      }

      // 对比页面标题
      const [viteTitle, nextjsTitle] = await Promise.all([
        vitePage.title(),
        nextjsPage.title(),
      ]);

      if (viteTitle !== nextjsTitle) {
        result.functional.errors.push(`页面标题不一致: Vite="${viteTitle}", Next.js="${nextjsTitle}"`);
      }

      // 对比指定选择器的内容
      if (testPage.selectors) {
        for (const selector of testPage.selectors) {
          try {
            const [viteContent, nextjsContent] = await Promise.all([
              vitePage.textContent(selector),
              nextjsPage.textContent(selector),
            ]);

            if (this.normalizeContent(viteContent) !== this.normalizeContent(nextjsContent)) {
              result.functional.errors.push(
                `选择器 "${selector}" 内容不一致: Vite="${viteContent?.slice(0, 100)}...", Next.js="${nextjsContent?.slice(0, 100)}..."`
              );
            }
          } catch (error) {
            result.functional.errors.push(`选择器 "${selector}" 检查失败: ${error}`);
          }
        }
      }

      // 检查控制台错误
      const viteErrors: string[] = [];
      const nextjsErrors: string[] = [];

      vitePage.on('console', (msg) => {
        if (msg.type() === 'error') {
          viteErrors.push(msg.text());
        }
      });

      nextjsPage.on('console', (msg) => {
        if (msg.type() === 'error') {
          nextjsErrors.push(msg.text());
        }
      });

      // 等待一段时间收集错误
      await new Promise(resolve => setTimeout(resolve, 2000));

      if (nextjsErrors.length > viteErrors.length) {
        result.functional.errors.push(`Next.js 版本有更多控制台错误: ${nextjsErrors.length} vs ${viteErrors.length}`);
      }

      result.functional.passed = result.functional.errors.length === 0;
    } catch (error) {
      result.functional.errors.push(`功能对比测试失败: ${error}`);
      result.functional.passed = false;
    }
  }

  /**
   * 视觉对比测试
   */
  private async runVisualComparison(
    vitePage: Page,
    nextjsPage: Page,
    testPage: TestPage,
    result: ComparisonResult
  ): Promise<void> {
    try {
      // 等待页面稳定
      await Promise.all([
        vitePage.waitForTimeout(1000),
        nextjsPage.waitForTimeout(1000),
      ]);

      // 截图
      const [viteScreenshot, nextjsScreenshot] = await Promise.all([
        vitePage.screenshot({ fullPage: true }),
        nextjsPage.screenshot({ fullPage: true }),
      ]);

      // 保存截图
      const timestamp = Date.now();
      const viteScreenshotPath = path.join(this.config.outputDir, 'screenshots', `vite-${testPage.route.replace(/\//g, '_')}-${timestamp}.png`);
      const nextjsScreenshotPath = path.join(this.config.outputDir, 'screenshots', `nextjs-${testPage.route.replace(/\//g, '_')}-${timestamp}.png`);

      await Promise.all([
        fs.writeFile(viteScreenshotPath, viteScreenshot),
        fs.writeFile(nextjsScreenshotPath, nextjsScreenshot),
      ]);

      // 对比图片
      const img1 = PNG.sync.read(viteScreenshot);
      const img2 = PNG.sync.read(nextjsScreenshot);

      const { width, height } = img1;
      const diff = new PNG({ width, height });

      const numDiffPixels = pixelmatch(
        img1.data,
        img2.data,
        diff.data,
        width,
        height,
        { threshold: this.config.threshold }
      );

      const diffPercentage = (numDiffPixels / (width * height)) * 100;
      result.visual.diffPercentage = diffPercentage;

      // 如果有差异，保存差异图片
      if (diffPercentage > 1) {
        const diffImagePath = path.join(this.config.outputDir, 'diffs', `diff-${testPage.route.replace(/\//g, '_')}-${timestamp}.png`);
        await fs.writeFile(diffImagePath, PNG.sync.write(diff));
        result.visual.diffImagePath = diffImagePath;
        result.visual.passed = false;
      }

      console.log(`📸 视觉对比完成: ${testPage.name}, 差异: ${diffPercentage.toFixed(2)}%`);
    } catch (error) {
      console.error(`❌ 视觉对比失败: ${testPage.name}`, error);
      result.visual.passed = false;
    }
  }

  /**
   * 性能对比测试
   */
  private async runPerformanceComparison(
    vitePage: Page,
    nextjsPage: Page,
    testPage: TestPage,
    result: ComparisonResult
  ): Promise<void> {
    try {
      // 测试 Vite 版本性能
      result.performance.vite = await this.measurePagePerformance(vitePage, `${this.config.viteUrl}${testPage.route}`);

      // 测试 Next.js 版本性能
      result.performance.nextjs = await this.measurePagePerformance(nextjsPage, `${this.config.nextjsUrl}${testPage.route}`);

      // 计算改进百分比
      const vite = result.performance.vite;
      const nextjs = result.performance.nextjs;

      result.performance.improvements = {
        FCP: this.calculateImprovement(vite.FCP, nextjs.FCP),
        LCP: this.calculateImprovement(vite.LCP, nextjs.LCP),
        TTFB: this.calculateImprovement(vite.TTFB, nextjs.TTFB),
        loadTime: this.calculateImprovement(vite.loadTime, nextjs.loadTime),
        memoryUsage: this.calculateImprovement(vite.memoryUsage, nextjs.memoryUsage),
      };

      console.log(`⚡ 性能对比完成: ${testPage.name}`);
      console.log(`   FCP: ${vite.FCP.toFixed(0)}ms → ${nextjs.FCP.toFixed(0)}ms (${result.performance.improvements.FCP.toFixed(1)}%)`);
      console.log(`   LCP: ${vite.LCP.toFixed(0)}ms → ${nextjs.LCP.toFixed(0)}ms (${result.performance.improvements.LCP.toFixed(1)}%)`);
    } catch (error) {
      console.error(`❌ 性能对比失败: ${testPage.name}`, error);
    }
  }

  /**
   * 测量页面性能
   */
  private async measurePagePerformance(page: Page, url: string): Promise<PerformanceMetrics> {
    await page.goto(url, { waitUntil: 'networkidle' });

    const metrics = await page.evaluate(() => {
      return new Promise<PerformanceMetrics>((resolve) => {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const result: Partial<PerformanceMetrics> = {};

          entries.forEach((entry) => {
            if (entry.entryType === 'paint' && entry.name === 'first-contentful-paint') {
              result.FCP = entry.startTime;
            }
            if (entry.entryType === 'largest-contentful-paint') {
              result.LCP = entry.startTime;
            }
          });

          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
          result.TTFB = navigation.responseStart - navigation.requestStart;
          result.loadTime = navigation.loadEventEnd - navigation.fetchStart;

          // 内存使用（如果可用）
          if ('memory' in performance) {
            result.memoryUsage = (performance as any).memory.usedJSHeapSize / 1024 / 1024; // MB
          }

          resolve(result as PerformanceMetrics);
        });

        observer.observe({ entryTypes: ['paint', 'largest-contentful-paint'] });

        // 3秒后返回结果
        setTimeout(() => {
          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
          resolve({
            FCP: 0,
            LCP: 0,
            TTFB: navigation.responseStart - navigation.requestStart,
            loadTime: navigation.loadEventEnd - navigation.fetchStart,
            memoryUsage: 0,
          });
        }, 3000);
      });
    });

    return metrics;
  }

  /**
   * 标准化内容（移除动态部分）
   */
  private normalizeContent(content: string | null): string {
    if (!content) return '';
    return content
      .replace(/\d{4}-\d{2}-\d{2}/g, 'DATE')
      .replace(/\d{2}:\d{2}:\d{2}/g, 'TIME')
      .replace(/\d{13}/g, 'TIMESTAMP')
      .trim();
  }

  /**
   * 计算性能改进百分比
   */
  private calculateImprovement(oldValue: number, newValue: number): number {
    if (oldValue === 0) return 0;
    return ((oldValue - newValue) / oldValue) * 100;
  }

  /**
   * 生成对比报告
   */
  async generateReport(results: ComparisonResult[]): Promise<void> {
    const reportPath = path.join(this.config.outputDir, `comparison-report-${Date.now()}.html`);

    const html = `
<!DOCTYPE html>
<html>
<head>
    <title>迁移对比报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 8px; }
        .result { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .passed { border-color: #4caf50; background: #f1f8e9; }
        .failed { border-color: #f44336; background: #ffebee; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; }
        .metric { padding: 10px; background: #f9f9f9; border-radius: 4px; }
        .improvement { color: #4caf50; font-weight: bold; }
        .regression { color: #f44336; font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Next.js 迁移对比报告</h1>
        <p>生成时间: ${new Date().toLocaleString()}</p>
        <p>测试页面数: ${results.length}</p>
    </div>

    ${results.map(result => `
        <div class="result ${result.functional.passed && result.visual.passed ? 'passed' : 'failed'}">
            <h2>${result.route}</h2>
            
            <h3>功能对比</h3>
            <p>状态: ${result.functional.passed ? '✅ 通过' : '❌ 失败'}</p>
            ${result.functional.errors.length > 0 ? `
                <ul>
                    ${result.functional.errors.map(error => `<li>${error}</li>`).join('')}
                </ul>
            ` : ''}

            <h3>视觉对比</h3>
            <p>状态: ${result.visual.passed ? '✅ 通过' : '❌ 失败'}</p>
            <p>差异百分比: ${result.visual.diffPercentage.toFixed(2)}%</p>

            <h3>性能对比</h3>
            <div class="metrics">
                <div class="metric">
                    <strong>FCP</strong><br>
                    Vite: ${result.performance.vite.FCP?.toFixed(0) || 'N/A'}ms<br>
                    Next.js: ${result.performance.nextjs.FCP?.toFixed(0) || 'N/A'}ms<br>
                    <span class="${result.performance.improvements.FCP > 0 ? 'improvement' : 'regression'}">
                        ${result.performance.improvements.FCP?.toFixed(1) || 'N/A'}%
                    </span>
                </div>
                <div class="metric">
                    <strong>LCP</strong><br>
                    Vite: ${result.performance.vite.LCP?.toFixed(0) || 'N/A'}ms<br>
                    Next.js: ${result.performance.nextjs.LCP?.toFixed(0) || 'N/A'}ms<br>
                    <span class="${result.performance.improvements.LCP > 0 ? 'improvement' : 'regression'}">
                        ${result.performance.improvements.LCP?.toFixed(1) || 'N/A'}%
                    </span>
                </div>
                <div class="metric">
                    <strong>加载时间</strong><br>
                    Vite: ${result.performance.vite.loadTime?.toFixed(0) || 'N/A'}ms<br>
                    Next.js: ${result.performance.nextjs.loadTime?.toFixed(0) || 'N/A'}ms<br>
                    <span class="${result.performance.improvements.loadTime > 0 ? 'improvement' : 'regression'}">
                        ${result.performance.improvements.loadTime?.toFixed(1) || 'N/A'}%
                    </span>
                </div>
            </div>
        </div>
    `).join('')}
</body>
</html>
    `;

    await fs.writeFile(reportPath, html);
    console.log(`📊 报告已生成: ${reportPath}`);
  }
}
