#!/usr/bin/env node

/**
 * 迁移监控脚本
 * 自动运行对比测试并生成报告
 */

import { MigrationComparisonTool } from './comparison-test';
import { spawn, ChildProcess } from 'child_process';
import path from 'path';
import fs from 'fs/promises';

// 测试页面配置
const testPages = [
  {
    route: '/',
    name: '首页',
    selectors: ['h1', '.hero-section', '.product-gallery'],
  },
  {
    route: '/about',
    name: '关于页面',
    selectors: ['h1', '.about-content', '.team-section'],
  },
  {
    route: '/contact',
    name: '联系页面',
    selectors: ['h1', '.contact-form', '.contact-info'],
    actions: async (page) => {
      // 测试表单交互
      await page.fill('input[name="name"]', '测试用户');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('textarea[name="message"]', '这是一条测试消息');
    },
  },
  {
    route: '/products',
    name: '产品列表页',
    selectors: ['.product-list', '.product-card', '.pagination'],
    actions: async (page) => {
      // 测试产品筛选
      await page.click('.filter-button');
      await page.waitForTimeout(1000);
    },
  },
  {
    route: '/products/1',
    name: '产品详情页',
    selectors: ['.product-detail', '.product-images', '.product-info'],
  },
];

// 配置
const config = {
  viteUrl: 'http://localhost:5173',
  nextjsUrl: 'http://localhost:3000',
  outputDir: './migration-reports',
  viewport: { width: 1200, height: 800 },
  threshold: 0.1,
};

class MigrationMonitor {
  private viteProcess: ChildProcess | null = null;
  private nextjsProcess: ChildProcess | null = null;
  private comparisonTool: MigrationComparisonTool;

  constructor() {
    this.comparisonTool = new MigrationComparisonTool(config);
  }

  /**
   * 启动项目服务
   */
  async startServices(): Promise<void> {
    console.log('🚀 启动项目服务...');

    // 启动 Vite 项目
    console.log('启动 Vite 项目 (端口 5173)...');
    this.viteProcess = spawn('npm', ['run', 'dev'], {
      cwd: process.cwd(), // 当前项目目录
      stdio: 'pipe',
    });

    // 启动 Next.js 项目
    console.log('启动 Next.js 项目 (端口 3000)...');
    this.nextjsProcess = spawn('npm', ['run', 'dev'], {
      cwd: path.join(process.cwd(), 'nextjs-migration'), // Next.js 项目目录
      stdio: 'pipe',
    });

    // 等待服务启动
    console.log('等待服务启动...');
    await this.waitForServices();
  }

  /**
   * 等待服务启动完成
   */
  private async waitForServices(): Promise<void> {
    const maxRetries = 30;
    let retries = 0;

    while (retries < maxRetries) {
      try {
        // 检查 Vite 服务
        const viteResponse = await fetch(config.viteUrl);
        if (!viteResponse.ok) throw new Error('Vite service not ready');

        // 检查 Next.js 服务
        const nextjsResponse = await fetch(config.nextjsUrl);
        if (!nextjsResponse.ok) throw new Error('Next.js service not ready');

        console.log('✅ 服务启动完成');
        return;
      } catch (error) {
        retries++;
        console.log(`⏳ 等待服务启动... (${retries}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    throw new Error('服务启动超时');
  }

  /**
   * 停止项目服务
   */
  async stopServices(): Promise<void> {
    console.log('🛑 停止项目服务...');

    if (this.viteProcess) {
      this.viteProcess.kill();
      this.viteProcess = null;
    }

    if (this.nextjsProcess) {
      this.nextjsProcess.kill();
      this.nextjsProcess = null;
    }
  }

  /**
   * 运行完整的对比测试
   */
  async runFullComparison(): Promise<void> {
    console.log('🔍 开始完整对比测试...');

    try {
      await this.comparisonTool.init();

      const results = [];

      for (const testPage of testPages) {
        console.log(`\n📄 测试页面: ${testPage.name}`);
        const result = await this.comparisonTool.comparePage(testPage);
        results.push(result);

        // 输出简要结果
        const functionalStatus = result.functional.passed ? '✅' : '❌';
        const visualStatus = result.visual.passed ? '✅' : '❌';
        const fcpImprovement = result.performance.improvements.FCP?.toFixed(1) || 'N/A';
        
        console.log(`   功能: ${functionalStatus} | 视觉: ${visualStatus} | FCP改进: ${fcpImprovement}%`);
      }

      // 生成报告
      await this.comparisonTool.generateReport(results);
      await this.generateSummaryReport(results);

      console.log('\n✅ 对比测试完成');
    } finally {
      await this.comparisonTool.close();
    }
  }

  /**
   * 生成汇总报告
   */
  private async generateSummaryReport(results: any[]): Promise<void> {
    const summary = {
      timestamp: new Date().toISOString(),
      totalPages: results.length,
      functionalPassed: results.filter(r => r.functional.passed).length,
      visualPassed: results.filter(r => r.visual.passed).length,
      averagePerformanceImprovement: {
        FCP: this.calculateAverage(results.map(r => r.performance.improvements.FCP)),
        LCP: this.calculateAverage(results.map(r => r.performance.improvements.LCP)),
        loadTime: this.calculateAverage(results.map(r => r.performance.improvements.loadTime)),
      },
      issues: results.flatMap(r => r.functional.errors),
    };

    const summaryPath = path.join(config.outputDir, 'summary.json');
    await fs.writeFile(summaryPath, JSON.stringify(summary, null, 2));

    // 控制台输出汇总
    console.log('\n📊 测试汇总:');
    console.log(`   总页面数: ${summary.totalPages}`);
    console.log(`   功能通过: ${summary.functionalPassed}/${summary.totalPages}`);
    console.log(`   视觉通过: ${summary.visualPassed}/${summary.totalPages}`);
    console.log(`   平均性能改进:`);
    console.log(`     FCP: ${summary.averagePerformanceImprovement.FCP.toFixed(1)}%`);
    console.log(`     LCP: ${summary.averagePerformanceImprovement.LCP.toFixed(1)}%`);
    console.log(`     加载时间: ${summary.averagePerformanceImprovement.loadTime.toFixed(1)}%`);

    if (summary.issues.length > 0) {
      console.log(`\n⚠️  发现 ${summary.issues.length} 个问题:`);
      summary.issues.slice(0, 5).forEach((issue, index) => {
        console.log(`   ${index + 1}. ${issue}`);
      });
      if (summary.issues.length > 5) {
        console.log(`   ... 还有 ${summary.issues.length - 5} 个问题`);
      }
    }
  }

  /**
   * 计算平均值
   */
  private calculateAverage(values: number[]): number {
    const validValues = values.filter(v => !isNaN(v) && isFinite(v));
    if (validValues.length === 0) return 0;
    return validValues.reduce((sum, val) => sum + val, 0) / validValues.length;
  }

  /**
   * 运行单页面快速测试
   */
  async runQuickTest(route: string): Promise<void> {
    console.log(`🔍 快速测试页面: ${route}`);

    const testPage = testPages.find(p => p.route === route);
    if (!testPage) {
      console.error(`❌ 未找到页面配置: ${route}`);
      return;
    }

    try {
      await this.comparisonTool.init();
      const result = await this.comparisonTool.comparePage(testPage);

      console.log('\n📊 测试结果:');
      console.log(`   功能: ${result.functional.passed ? '✅ 通过' : '❌ 失败'}`);
      console.log(`   视觉: ${result.visual.passed ? '✅ 通过' : '❌ 失败'} (差异: ${result.visual.diffPercentage.toFixed(2)}%)`);
      console.log(`   性能改进:`);
      console.log(`     FCP: ${result.performance.improvements.FCP?.toFixed(1) || 'N/A'}%`);
      console.log(`     LCP: ${result.performance.improvements.LCP?.toFixed(1) || 'N/A'}%`);

      if (result.functional.errors.length > 0) {
        console.log('\n⚠️  功能问题:');
        result.functional.errors.forEach((error, index) => {
          console.log(`   ${index + 1}. ${error}`);
        });
      }
    } finally {
      await this.comparisonTool.close();
    }
  }

  /**
   * 持续监控模式
   */
  async runContinuousMonitoring(intervalMinutes: number = 30): Promise<void> {
    console.log(`🔄 启动持续监控模式 (间隔: ${intervalMinutes} 分钟)`);

    const runTest = async () => {
      try {
        console.log(`\n⏰ ${new Date().toLocaleString()} - 开始定时测试`);
        await this.runFullComparison();
      } catch (error) {
        console.error('❌ 定时测试失败:', error);
      }
    };

    // 立即运行一次
    await runTest();

    // 设置定时器
    const interval = setInterval(runTest, intervalMinutes * 60 * 1000);

    // 处理退出信号
    process.on('SIGINT', () => {
      console.log('\n🛑 停止持续监控...');
      clearInterval(interval);
      this.stopServices();
      process.exit(0);
    });
  }
}

// 命令行接口
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const monitor = new MigrationMonitor();

  try {
    await monitor.startServices();

    switch (command) {
      case 'full':
        await monitor.runFullComparison();
        break;
      case 'quick':
        const route = args[1] || '/';
        await monitor.runQuickTest(route);
        break;
      case 'watch':
        const interval = parseInt(args[1]) || 30;
        await monitor.runContinuousMonitoring(interval);
        break;
      default:
        console.log('使用方法:');
        console.log('  npm run monitor full          # 完整对比测试');
        console.log('  npm run monitor quick /about  # 快速测试单个页面');
        console.log('  npm run monitor watch 30      # 持续监控 (30分钟间隔)');
        break;
    }
  } catch (error) {
    console.error('❌ 监控失败:', error);
    process.exit(1);
  } finally {
    await monitor.stopServices();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

export { MigrationMonitor };
