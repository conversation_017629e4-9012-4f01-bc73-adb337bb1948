# 第一阶段：构建 React 项目
FROM node:20-alpine AS builder

# 定义构建参数，默认为生产环境
ARG NODE_ENV=production

# 设置工作目录
WORKDIR /app

# 增加 Node.js 内存限制
ENV NODE_OPTIONS="--max-old-space-size=4096"

# 安装依赖
COPY package.json yarn.lock ./
RUN yarn install --network-timeout 300000 --frozen-lockfile

# 复制源代码
COPY . .

# 显示环境信息
RUN echo "Node version: $(node -v)" && \
    echo "Yarn version: $(yarn -v)" && \
    echo "Environment: $NODE_ENV"

# 根据构建参数选择构建模式
RUN if [ "$NODE_ENV" = "production" ]; then \
      echo "Building for production environment..." && \
      yarn build:prod; \
    else \
      echo "Building for development environment..." && \
      yarn build; \
    fi

# 第二阶段：Nginx 服务
FROM nginx:alpine

# 删除默认的 Nginx 配置
RUN rm /etc/nginx/conf.d/default.conf

# 复制自定义 Nginx 配置
COPY nginx.conf /etc/nginx/conf.d

# 创建环境变量注入脚本
RUN echo '#!/bin/sh' > /docker-entrypoint.d/40-inject-env.sh && \
    echo 'envsubst < /usr/share/nginx/html/env-config.template.js > /usr/share/nginx/html/env-config.js' >> /docker-entrypoint.d/40-inject-env.sh && \
    chmod +x /docker-entrypoint.d/40-inject-env.sh

# 从构建阶段复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# 启动 Nginx
CMD ["nginx", "-g", "daemon off;"]