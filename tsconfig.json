{"compilerOptions": {"target": "ESNext", "module": "ESNext", "lib": ["ESNext", "DOM", "DOM.Iterable"], "useDefineForClassFields": true, "skipLibCheck": true, "allowJs": true, "moduleResolution": "bundler", "sourceMap": true, "declaration": true, "preserveWatchOutput": true, "removeComments": true, "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react", "esModuleInterop": true, "strict": true, "forceConsistentCasingInFileNames": true, "strictNullChecks": true, "noImplicitAny": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "useUnknownInCatchVariables": false, "typeRoots": ["node_modules/@types", "src/types"], "types": ["vite/client", "node"], "baseUrl": ".", "paths": {"@/*": ["src/*"], "#/*": ["src/types/*"]}}, "include": ["src", "src/types", "tailwind.config.ts", "src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "dist"]}