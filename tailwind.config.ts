// 导入 TailwindCSS 的类型定义
import type { Config } from "tailwindcss";

// 导入断点令牌配置
import { breakpointsTokens } from "./src/theme/tokens/breakpoints";

// 导入 HTML 数据属性枚举
import { HtmlDataAttribute } from "./src/types/enum";

// 导入创建颜色通道和 Tailwind 配置的工具函数
import { creatColorChannel, createTailwinConfg } from "./src/utils/theme";

// 导出 TailwindCSS 配置
export default {
	// 配置暗模式，基于自定义选择器
	darkMode: ["selector", `[${HtmlDataAttribute.ThemeMode}='dark']`],

	// 配置 TailwindCSS 的内容扫描路径
	content: ["./src/**/*.{js,ts,jsx,tsx,mdx}"],

	// 配置主题
	theme: {
		extend: {
			// 扩展颜色配置
			colors: {
				// slash admin 主题颜色
				primary: creatColorChannel("colors.palette.primary"), // 主色
				success: creatColorChannel("colors.palette.success"), // 成功色
				warning: creatColorChannel("colors.palette.warning"), // 警告色
				error: creatColorChannel("colors.palette.error"), // 错误色
				info: creatColorChannel("colors.palette.info"), // 信息色
				gray: creatColorChannel("colors.palette.gray"), // 灰色
				common: creatColorChannel("colors.common"), // 通用颜色
				text: creatColorChannel("colors.text"), // 文本颜色
				bg: creatColorChannel("colors.background"), // 背景颜色
				action: createTailwinConfg("colors.action"), // 动作颜色

				// shadcn UI 主题颜色
				background: "var(--background)", // 背景
				foreground: "var(--foreground)", // 前景
				card: "var(--card)", // 卡片背景
				cardForeground: "var(--card-foreground)", // 卡片前景
				popover: "var(--popover)", // 弹出框背景
				popoverForeground: "var(--popover-foreground)", // 弹出框前景
				primaryForeground: "var(--primary-foreground)", // 主色前景
				secondary: "var(--secondary)", // 次要颜色
				secondaryForeground: "var(--secondary-foreground)", // 次要颜色前景
				muted: "var(--muted)", // 弱化颜色
				mutedForeground: "var(--muted-foreground)", // 弱化颜色前景
				accent: "var(--accent)", // 强调色
				accentForeground: "var(--accent-foreground)", // 强调色前景
				destructive: "var(--destructive)", // 破坏性操作颜色
				border: "var(--border)", // 边框颜色
				input: "var(--input)", // 输入框颜色
				ring: "var(--ring)", // 环形颜色
				chart1: "var(--chart-1)", // 图表颜色 1
				chart2: "var(--chart-2)", // 图表颜色 2
				chart3: "var(--chart-3)", // 图表颜色 3
				chart4: "var(--chart-4)", // 图表颜色 4
				chart5: "var(--chart-5)", // 图表颜色 5
				sidebar: "var(--sidebar)", // 侧边栏背景
				sidebarForeground: "var(--sidebar-foreground)", // 侧边栏前景
				sidebarPrimary: "var(--sidebar-primary)", // 侧边栏主色
				sidebarPrimaryForeground: "var(--sidebar-primary-foreground)", // 侧边栏主色前景
				sidebarAccent: "var(--sidebar-accent)", // 侧边栏强调色
				sidebarAccentForeground: "var(--sidebar-accent-foreground)", // 侧边栏强调色前景
				sidebarBorder: "var(--sidebar-border)", // 侧边栏边框
				sidebarRing: "var(--sidebar-ring)", // 侧边栏环形颜色
			},

			// 扩展透明度配置
			opacity: createTailwinConfg("opacity"),

			// 扩展边框半径配置
			borderRadius: createTailwinConfg("borderRadius"),

			// 扩展阴影配置
			boxShadow: createTailwinConfg("shadows"),

			// 扩展间距配置
			spacing: createTailwinConfg("spacing"),

			// 扩展屏幕断点配置
			screens: breakpointsTokens,
		},
	},
} satisfies Config; // 确保配置符合 TailwindCSS 的类型定义