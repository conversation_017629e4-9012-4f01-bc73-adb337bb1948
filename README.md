

项目架构
```angular2html
src/
├── assets/              # 静态资源
│   ├── images/          # 图片资源
│   ├── fonts/           # 字体资源
│   └── icons/           # 图标资源
├── components/          # 通用UI组件
│   ├── common/          # 基础组件
│   ├── layout/          # 布局组件
│   └── feedback/        # 反馈组件
├── features/            # 业务功能模块
│   ├── home/            # 首页功能
│   ├── about/           # 关于页功能
│   ├── services/        # 服务页功能
│   └── contact/         # 联系页功能
├── hooks/               # 自定义Hooks
├── services/            # 服务层
│   ├── api/             # API通信
│   ├── analytics/       # 分析服务
│   └── storage/         # 存储服务
├── store/               # 状态管理
│   ├── slices/          # 状态切片
│   └── middleware/      # 中间件
├── utils/               # 工具函数
├── styles/              # 样式文件
│   ├── theme/           # 主题配置
│   └── global/          # 全局样式
├── types/               # 类型定义
├── routes/              # 路由配置
├── pages/               # 页面组件
└── Ap
```


部署生产 image 使用 docker
```bash
docker run -d -p 80:80 \
  -e NODE_ENV=production \
  -e API_BASE_URL=https://api.starrier.org \
  -e APP_BASE_PATH=/ \
  ice-company-website:production
```

部署生产，使用 docker compose
```bash
# 或者使用 Docker Compose 运行容器
docker-compose up -d
```

特性	本地测试环境	生产环境
构建模式	development	production
端口	8080	80
API 基础路径	http://localhost:3000/api	/api
模拟服务	启用	禁用
容器名称	ice-company-local-test	无特定名称
镜像标签	local-test	production